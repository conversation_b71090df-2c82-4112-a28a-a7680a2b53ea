import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import type { Product, ProductFormat, ShoppingCartItem } from '../../types'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { formatPrice } from '../lib/utils'
import { ShoppingCart, Plus, Minus } from 'lucide-react'
import { hoverLift, lineClamp, transition } from '../styles/utils'

const StyledCard = styled(Card)<{ $accentColor?: string }>`
  width: 100%;
  max-width: 24rem;
  margin: 0 auto;
  position: relative;
  ${hoverLift}

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${({ $accentColor, theme }) => $accentColor || theme.colors.primary[500]};
    border-radius: ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0 0;
  }
`

const ImageContainer = styled.div`
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0 0;
`

const ProductImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  ${transition('transform')}

  &:hover {
    transform: scale(1.05);
  }
`

const ProductTitle = styled(CardTitle)`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  ${lineClamp(2)}
`

const ProductDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  ${lineClamp(3)}
`

const PriceContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const Price = styled.span<{ $accentColor?: string }>`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ $accentColor, theme }) => $accentColor || theme.colors.primary[500]};
`

const FormatSelector = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const FormatOption = styled.button<{ $selected: boolean; $accentColor?: string }>`
  width: 100%;
  padding: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  border: 2px solid ${({ $selected, $accentColor, theme }) =>
    $selected ? ($accentColor || theme.colors.primary[500]) : theme.colors.border.default};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ $selected, theme }) =>
    $selected ? 'rgba(0, 114, 206, 0.1)' : theme.colors.white};
  cursor: pointer;
  text-align: left;
  ${transition()}

  &:hover {
    border-color: ${({ $accentColor, theme }) => $accentColor || theme.colors.primary[300]};
  }

  &:last-child {
    margin-bottom: 0;
  }
`

const FormatName = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`

const FormatDescription = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const FormatPrice = styled.div<{ $accentColor?: string }>`
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ $accentColor, theme }) => $accentColor || theme.colors.primary[600]};
`

const StockInfo = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-top: ${({ theme }) => theme.spacing[1]};
`

const CategoryBadge = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  background: ${({ theme }) => theme.colors.secondary[100]};
  color: ${({ theme }) => theme.colors.secondary[800]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
`

const StockWarning = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};

  &.low-stock {
    color: #ea580c;
  }

  &.out-of-stock {
    color: ${({ theme }) => theme.colors.error};
  }
`

const QuantityControls = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`

const QuantityGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`

const QuantityDisplay = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  min-width: 2rem;
  text-align: center;
`

const TotalPrice = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
`

interface ProductCardProps {
  product: Product
  cartItem?: ShoppingCartItem
  onAddToCart: (product: Product, format: ProductFormat) => void
  onUpdateQuantity: (productId: string, quantity: number) => void
}

/**
 * Componente para mostrar una tarjeta de producto
 */
export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  cartItem,
  onAddToCart,
  onUpdateQuantity
}) => {
  // Estado para el formato seleccionado (por defecto el primero)
  const [selectedFormat, setSelectedFormat] = useState<ProductFormat>(
    product.formats[0] || { name: '', description: '', price: 0, stock: 0 }
  )

  // Estado para manejar la confirmación de agregar al carrito
  const [isConfirming, setIsConfirming] = useState(false)
  const [lockedFormat, setLockedFormat] = useState<ProductFormat | null>(null)
  const [pendingQuantity, setPendingQuantity] = useState(1)

  const handleAddToCart = () => {
    // Primera vez: bloquear formato y mostrar controles de cantidad + confirmación
    setIsConfirming(true)
    setLockedFormat(selectedFormat)
    setPendingQuantity(1)
  }

  const handleConfirm = () => {
    // Agregar al carrito con la cantidad seleccionada
    for (let i = 0; i < pendingQuantity; i++) {
      onAddToCart(product, lockedFormat || selectedFormat)
    }
    // Resetear estados
    setIsConfirming(false)
    setLockedFormat(null)
    setPendingQuantity(1)
  }

  const handleCancelConfirmation = () => {
    setIsConfirming(false)
    setLockedFormat(null)
    setPendingQuantity(1)
  }

  const handleIncreasePendingQuantity = () => {
    const maxStock = lockedFormat?.stock || selectedFormat.stock
    if (maxStock === -1 || pendingQuantity < maxStock) {
      setPendingQuantity(prev => prev + 1)
    }
  }

  const handleDecreasePendingQuantity = () => {
    if (pendingQuantity > 1) {
      setPendingQuantity(prev => prev - 1)
    }
  }

  // Resetear estado cuando el producto se agrega al carrito
  useEffect(() => {
    if (cartItem && isConfirming) {
      setIsConfirming(false)
      setLockedFormat(null)
    }
  }, [cartItem, isConfirming])

  const handleIncreaseQuantity = () => {
    const newQuantity = (cartItem?.quantity || 0) + 1
    onUpdateQuantity(product.id, newQuantity)
  }

  const handleDecreaseQuantity = () => {
    const newQuantity = Math.max(0, (cartItem?.quantity || 0) - 1)
    onUpdateQuantity(product.id, newQuantity)
  }

  return (
    <StyledCard $accentColor={product.color}>
      <CardHeader style={{ padding: 0 }}>
        <ImageContainer>
          <ProductImage
            src={product.imageUrl}
            alt={product.name}
          />
        </ImageContainer>
      </CardHeader>

      <CardContent style={{ padding: '1rem' }}>
        <ProductTitle>
          {product.name}
        </ProductTitle>
        <ProductDescription>
          {product.description}
        </ProductDescription>

        {/* Selector de formatos */}
        {product.formats.length > 1 && (
          <FormatSelector>
            {product.formats.map((format, index) => (
              <FormatOption
                key={index}
                $selected={isConfirming ? lockedFormat?.name === format.name : selectedFormat.name === format.name}
                $accentColor={product.color}
                onClick={() => !isConfirming && setSelectedFormat(format)}
                style={{
                  opacity: isConfirming && lockedFormat?.name !== format.name ? 0.5 : 1,
                  cursor: isConfirming ? 'not-allowed' : 'pointer'
                }}
              >
                <FormatName>{format.name}</FormatName>
                <FormatDescription>{format.description}</FormatDescription>
                <FormatPrice $accentColor={product.color}>
                  {formatPrice(format.price)}
                </FormatPrice>
                <StockInfo>
                  {format.stock > 0 ? `${format.stock} disponibles` : 'Agotado'}
                </StockInfo>
              </FormatOption>
            ))}
          </FormatSelector>
        )}

        <PriceContainer>
          <Price $accentColor={product.color}>
            {formatPrice((isConfirming ? lockedFormat : selectedFormat)?.price || 0)}
          </Price>
          {product.category && (
            <CategoryBadge>
              {product.category}
            </CategoryBadge>
          )}
        </PriceContainer>
        {selectedFormat.stock <= 5 && selectedFormat.stock > 0 && (
          <StockWarning className="low-stock">
            ¡Solo quedan {selectedFormat.stock} unidades!
          </StockWarning>
        )}
        {selectedFormat.stock === 0 && (
          <StockWarning className="out-of-stock">
            Agotado
          </StockWarning>
        )}
      </CardContent>

      <CardFooter style={{ padding: '1rem', paddingTop: 0 }}>
        {!cartItem ? (
          isConfirming ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', width: '100%' }}>
              {/* Controles de cantidad */}
              <QuantityControls>
                <QuantityGroup>
                  <Button
                    variant="outline"
                    style={{ color: product.color, borderColor: product.color }}
                    size="icon"
                    onClick={handleDecreasePendingQuantity}
                    disabled={pendingQuantity <= 1}
                  >
                    <Minus size={16} />
                  </Button>
                  <QuantityDisplay>
                    {pendingQuantity}
                  </QuantityDisplay>
                  <Button
                    variant="outline"
                    style={{ color: product.color, borderColor: product.color }}
                    size="icon"
                    onClick={handleIncreasePendingQuantity}
                    disabled={lockedFormat?.stock !== -1 && pendingQuantity >= (lockedFormat?.stock || 0)}
                  >
                    <Plus size={16} />
                  </Button>
                </QuantityGroup>
                <TotalPrice>
                  {formatPrice(pendingQuantity * (lockedFormat?.price || selectedFormat.price))}
                </TotalPrice>
              </QuantityControls>

              {/* Botones de confirmación */}
              <div style={{ display: 'flex', gap: '0.5rem', width: '100%' }}>
                <Button
                  onClick={handleConfirm}
                  style={{
                    flex: 1,
                    backgroundColor: product.color,
                    borderColor: product.color
                  }}
                >
                  ✓ Confirmar
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCancelConfirmation}
                  style={{
                    color: product.color,
                    borderColor: product.color
                  }}
                >
                  ✕ Cancelar
                </Button>
              </div>
            </div>
          ) : (
            <Button
              onClick={handleAddToCart}
              disabled={selectedFormat.stock === 0}
              style={{ width: '100%', backgroundColor: product.color, borderColor: product.color }}
            >
              <ShoppingCart size={16} style={{ marginRight: '0.5rem' }} />
              Agregar al carrito
            </Button>
          )
        ) : (
          <QuantityControls>
            <QuantityGroup>
              <Button
                variant="outline"
                style={{ color: product.color, borderColor: product.color }}
                size="icon"
                onClick={handleDecreaseQuantity}
                disabled={cartItem.quantity <= 1}
              >
                <Minus size={16} />
              </Button>
              <QuantityDisplay>
                {cartItem.quantity}
              </QuantityDisplay>
              <Button
                variant="outline"
                style={{ color: product.color, borderColor: product.color }}
                size="icon"
                onClick={handleIncreaseQuantity}
                disabled={selectedFormat.stock !== -1 && cartItem.quantity >= selectedFormat.stock}
              >
                <Plus size={16} />
              </Button>
            </QuantityGroup>
            <TotalPrice>
              {formatPrice(cartItem.quantity * cartItem.unitPrice)}
            </TotalPrice>
          </QuantityControls>
        )}
      </CardFooter>
    </StyledCard>
  )
}
