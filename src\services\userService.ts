import { db } from '../lib/firebase'
import { doc, getDoc, updateDoc, arrayUnion, arrayRemove } from 'firebase/firestore'
import type { User, UserAddress } from '../../types'

export class UserService {
  /**
   * Obtiene las direcciones de un usuario
   */
  static async getAddresses(uid: string): Promise<UserAddress[]> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid))
      if (userDoc.exists()) {
        const userData = userDoc.data() as User
        return userData.addresses || []
      }
      return []
    } catch (error) {
      console.error('Error obteniendo direcciones:', error)
      throw new Error('Error al obtener las direcciones')
    }
  }

  /**
   * Agrega una nueva dirección a un usuario
   */
  static async addAddress(uid: string, address: UserAddress): Promise<void> {
    try {
      const userRef = doc(db, 'users', uid)
      
      // Si es la dirección predeterminada, primero quitar el flag de las otras
      if (address.isDefault) {
        const addresses = await this.getAddresses(uid)
        const updatedAddresses = addresses.map(addr => ({ ...addr, isDefault: false }))
        
        await updateDoc(userRef, {
          addresses: [...updatedAddresses, address]
        })
      } else {
        await updateDoc(userRef, {
          addresses: arrayUnion(address)
        })
      }
    } catch (error) {
      console.error('Error agregando dirección:', error)
      throw new Error('Error al agregar la dirección')
    }
  }

  /**
   * Actualiza una dirección existente
   */
  static async updateAddress(uid: string, updatedAddress: UserAddress): Promise<void> {
    try {
      const addresses = await this.getAddresses(uid)
      const addressIndex = addresses.findIndex(addr => addr.id === updatedAddress.id)
      
      if (addressIndex === -1) {
        throw new Error('Dirección no encontrada')
      }

      // Si es la dirección predeterminada, quitar el flag de las otras
      if (updatedAddress.isDefault) {
        addresses.forEach(addr => {
          if (addr.id !== updatedAddress.id) {
            addr.isDefault = false
          }
        })
      }

      addresses[addressIndex] = updatedAddress
      
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        addresses: addresses
      })
    } catch (error) {
      console.error('Error actualizando dirección:', error)
      throw new Error('Error al actualizar la dirección')
    }
  }

  /**
   * Elimina una dirección
   */
  static async deleteAddress(uid: string, addressId: string): Promise<void> {
    try {
      const addresses = await this.getAddresses(uid)
      const addressToDelete = addresses.find(addr => addr.id === addressId)
      
      if (!addressToDelete) {
        throw new Error('Dirección no encontrada')
      }

      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        addresses: arrayRemove(addressToDelete)
      })
    } catch (error) {
      console.error('Error eliminando dirección:', error)
      throw new Error('Error al eliminar la dirección')
    }
  }

  /**
   * Establece una dirección como predeterminada
   */
  static async setDefaultAddress(uid: string, addressId: string): Promise<void> {
    try {
      const addresses = await this.getAddresses(uid)
      const updatedAddresses = addresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }))

      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        addresses: updatedAddresses
      })
    } catch (error) {
      console.error('Error estableciendo dirección predeterminada:', error)
      throw new Error('Error al establecer la dirección predeterminada')
    }
  }

  /**
   * Obtiene la información completa del usuario
   */
  static async getUser(uid: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid))
      if (userDoc.exists()) {
        return userDoc.data() as User
      }
      return null
    } catch (error) {
      console.error('Error obteniendo usuario:', error)
      throw new Error('Error al obtener la información del usuario')
    }
  }

  /**
   * Actualiza la información del usuario
   */
  static async updateUser(uid: string, userData: Partial<User>): Promise<void> {
    try {
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        ...userData,
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error actualizando usuario:', error)
      throw new Error('Error al actualizar la información del usuario')
    }
  }
}
