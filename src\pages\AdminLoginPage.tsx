import React, { useState } from 'react'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { AuthService } from '../services/authService'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { Admin } from '../../types'
import { Shield } from 'lucide-react'

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing[4]};
`

const AdminCard = styled(Card)`
  width: 100%;
  max-width: 28rem;
  box-shadow: ${({ theme }) => theme.shadows.lg};
`

const ShieldIcon = styled.div`
  width: 4rem;
  height: 4rem;
  background: ${({ theme }) => theme.colors.primary[500]};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[4]};
`

const GoogleButton = styled(Button)`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  font-size: ${({ theme }) => theme.fontSizes.base};
  background: #4285f4;
  border-color: #4285f4;

  &:hover:not(:disabled) {
    background: #3367d6;
    border-color: #3367d6;
  }
`

const GoogleIcon = styled.div`
  width: 1.25rem;
  height: 1.25rem;
  background: white;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #4285f4;
  font-size: 0.875rem;
`

const BackButton = styled(Button)`
  margin-top: ${({ theme }) => theme.spacing[4]};
`

const InfoCard = styled(Card)`
  margin-top: ${({ theme }) => theme.spacing[4]};
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
`

const InfoTitle = styled.h4`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: #1e40af;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const InfoText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: #1e40af;

  p {
    margin-bottom: ${({ theme }) => theme.spacing[1]};
  }

  .note {
    font-size: ${({ theme }) => theme.fontSizes.xs};
    color: #3b82f6;
    margin-top: ${({ theme }) => theme.spacing[2]};
  }
`

interface AdminLoginPageProps {
  onAdminLogin: (admin: Admin) => void
}

/**
 * Página de inicio de sesión para administradores con Google OAuth
 */
export const AdminLoginPage: React.FC<AdminLoginPageProps> = ({ onAdminLogin }) => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleGoogleSignIn = async () => {
    setLoading(true)
    setError('')

    try {
      const admin = await AuthService.loginAdmin('', '')
      onAdminLogin(admin)
      navigate('/admin/dashboard')
    } catch (error: any) {
      console.error('Error en login de admin:', error)
      setError(error.message || 'Error en la autenticación de administrador')
    } finally {
      setLoading(false)
    }
  }

  return (
    <PageContainer>
      <div style={{ width: '100%', maxWidth: '28rem' }}>
        <AdminCard>
          <CardHeader style={{ textAlign: 'center' }}>
            <ShieldIcon>
              <Shield size={32} color="white" />
            </ShieldIcon>
            <CardTitle style={{ fontSize: '1.5rem' }}>Panel de Administración</CardTitle>
            <CardDescription>
              Acceso exclusivo para administradores del sistema
            </CardDescription>
          </CardHeader>

          <CardContent>
            {/* Error */}
            {error && (
              <div style={{
                fontSize: '0.875rem',
                color: '#ef4444',
                background: 'rgba(239, 68, 68, 0.1)',
                padding: '0.75rem',
                borderRadius: '0.375rem',
                marginBottom: '1rem'
              }}>
                {error}
              </div>
            )}

            {/* Botón de Google */}
            <GoogleButton onClick={handleGoogleSignIn} disabled={loading}>
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <>
                  <GoogleIcon>G</GoogleIcon>
                  Acceder con Google
                </>
              )}
            </GoogleButton>

            {/* Información adicional */}
            <div style={{
              marginTop: '1.5rem',
              textAlign: 'center',
              fontSize: '0.75rem',
              color: '#6b7280'
            }}>
              <p>
                Solo personal autorizado puede acceder a esta área.
                <br />
                Todos los accesos son monitoreados y registrados.
              </p>
            </div>

            {/* Enlace de regreso */}
            <div style={{ marginTop: '1rem', textAlign: 'center' }}>
              <BackButton variant="link" onClick={() => navigate('/')}>
                ← Volver a la tienda
              </BackButton>
            </div>
          </CardContent>
        </AdminCard>

        {/* Información de demostración */}
        <InfoCard>
          <CardContent style={{ padding: '1rem' }}>
            <InfoTitle>Demo - Información importante:</InfoTitle>
            <InfoText>
              <p><strong>Para ser administrador:</strong></p>
              <p>1. Inicia sesión con Google</p>
              <p>2. Tu cuenta debe estar registrada en la colección 'admins' de Firestore</p>
              <p className="note">
                * Contacta al administrador del sistema para obtener permisos
              </p>
            </InfoText>
          </CardContent>
        </InfoCard>
      </div>
    </PageContainer>
  )
}
