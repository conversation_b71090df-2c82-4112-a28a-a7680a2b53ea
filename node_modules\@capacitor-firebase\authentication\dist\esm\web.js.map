{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAW5C,OAAO,EACL,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,eAAe,EACf,aAAa,EACb,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,uBAAuB,EACvB,yBAAyB,EACzB,oBAAoB,EACpB,mBAAmB,EACnB,8BAA8B,EAC9B,UAAU,EACV,0BAA0B,EAC1B,qBAAqB,EACrB,OAAO,EACP,iBAAiB,EACjB,mBAAmB,EACnB,yBAAyB,EACzB,qBAAqB,EACrB,kBAAkB,EAClB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,MAAM,EACN,iBAAiB,EACjB,qBAAqB,EACrB,sBAAsB,EACtB,qBAAqB,EACrB,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,0BAA0B,EAC1B,mBAAmB,EACnB,qBAAqB,EACrB,eAAe,EACf,kBAAkB,EAClB,MAAM,EACN,WAAW,EACX,cAAc,EACd,aAAa,EACb,uBAAuB,GACxB,MAAM,eAAe,CAAC;AAsDvB,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAExD,MAAM,OAAO,yBACX,SAAQ,SAAS;IAkBjB;QACE,KAAK,EAAE,CAAC;QAHF,2BAAsB,GAAoC,IAAI,GAAG,EAAE,CAAC;QAI1E,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IACrE,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,OAA+B;QAC1D,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAEM,KAAK,CAAC,8BAA8B,CACzC,OAA8C;QAE9C,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,8BAA8B,CACzD,IAAI,EACJ,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,QAAQ,CACjB,CAAC;QACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAC/B,OAAoC;QAEpC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;IAC1E,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAClC,OAAuC;QAEvC,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QACrD,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC3E,IAAI,CAAC,kBAAkB,EAAE;YACvB,MAAM,IAAI,KAAK,CACb,yBAAyB,CAAC,iCAAiC,CAC5D,CAAC;SACH;QACD,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,0BAA0B,CACrC,OAA0C;QAE1C,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,MAAM,0BAA0B,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5E,OAAO;YACL,aAAa;SACd,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAyB;YACnC,IAAI,EAAE,UAAU;SACjB,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,UAAU,CACrB,OAA2B;QAE3B,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,CAAC,CAAC;QACzE,MAAM,MAAM,GAAqB;YAC/B,KAAK,EAAE,OAAO,IAAI,EAAE;SACrB,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,cAAc,GAAG,cAAc;YACnC,CAAC,CAAC,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,IAAI,CAAC;QACT,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAqC;QAErC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO;YACL,qBAAqB,EAAE,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC;SACtE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,wBAAwB,CACnC,OAAwC;QAExC,MAAM,cAAc,GAAG,iBAAiB,CAAC,UAAU,CACjD,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,QAAQ,CACjB,CAAC;QACF,MAAM,cAAc,GAClB,MAAM,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,OAAiC;QAEjC,MAAM,cAAc,GAAG,iBAAiB,CAAC,kBAAkB,CACzD,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,SAAS,CAClB,CAAC;QACF,MAAM,cAAc,GAClB,MAAM,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC5C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,oBAAoB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,kBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,kBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAuC;QAEvC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC3C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,CAAC,IAAI,CACb,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAC9B,OAAmC;QAEnC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,0BAA0B,CAAC,CAAC;SACvE;QACD,IACE,CAAC,OAAO,CAAC,iBAAiB;YAC1B,CAAC,CAAC,OAAO,CAAC,iBAAiB,YAAY,iBAAiB,CAAC,EACzD;YACA,MAAM,IAAI,KAAK,CACb,yBAAyB,CAAC,gCAAgC,CAC3D,CAAC;SACH;QACD,IAAI;YACF,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAClD,WAAW,EACX,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,iBAAiB,CAC1B,CAAC;YACF,MAAM,EAAE,cAAc,EAAE,GAAG,kBAAkB,CAAC;YAC9C,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YACpE,MAAM,KAAK,GAAuB;gBAChC,cAAc;aACf,CAAC;YACF,IAAI,CAAC,eAAe,CAClB,yBAAyB,CAAC,qBAAqB,EAC/C,KAAK,CACN,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,KAAK,GAAiC;gBAC1C,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;aACrC,CAAC;YACF,IAAI,CAAC,eAAe,CAClB,yBAAyB,CAAC,+BAA+B,EACzD,KAAK,CACN,CAAC;SACH;IACH,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,mBAAmB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAClE,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,OAAiC;QAEjC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAqC;QAErC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,qBAAqB,CAAC,WAAW,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,CAAC,CAAC;IACzE,CAAC;IAEM,KAAK,CAAC,sBAAsB,CACjC,OAAsC;QAEtC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO,sBAAsB,CAC3B,IAAI,EACJ,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,kBAAkB,CAC3B,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAqC;QAErC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,OAAO,qBAAqB,CAC1B,IAAI,EACJ,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,kBAAkB,CAC3B,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,OAA+B;QAC1D,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAA8B;QACxD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,QAAQ,OAAO,CAAC,WAAW,EAAE;YAC3B,KAAK,WAAW,CAAC,YAAY;gBAC3B,MAAM,cAAc,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;gBACpD,MAAM;YACR,KAAK,WAAW,CAAC,cAAc;gBAC7B,MAAM,cAAc,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,WAAW,CAAC,cAAc;gBAC7B,MAAM,cAAc,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,WAAW,CAAC,QAAQ;gBACvB,MAAM,cAAc,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBAChD,MAAM;SACT;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAA2B;QAClD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAqC;QAErC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,KAAK,CAAC,0BAA0B,CACrC,OAA0C;QAE1C,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,0BAA0B,CACrD,IAAI,EACJ,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,QAAQ,CACjB,CAAC;QACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAC9B,OAAmC;QAEnC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,mBAAmB,CAC9C,IAAI,EACJ,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,SAAS,CAClB,CAAC;QACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC7B,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC5C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,oBAAoB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,kBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,kBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAC9B,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAClC,OAAuC;QAEvC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC3C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,CAAC,IAAI,CACb,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,OAAqC;QAErC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,0BAA0B,CAAC,CAAC;SACvE;QACD,IACE,CAAC,OAAO,CAAC,iBAAiB;YAC1B,CAAC,CAAC,OAAO,CAAC,iBAAiB,YAAY,iBAAiB,CAAC,EACzD;YACA,MAAM,IAAI,KAAK,CACb,yBAAyB,CAAC,gCAAgC,CAC3D,CAAC;SACH;QACD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI;YACF,MAAM,kBAAkB,GAAG,MAAM,qBAAqB,CACpD,IAAI,EACJ,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,iBAAiB,CAC1B,CAAC;YACF,MAAM,EAAE,cAAc,EAAE,GAAG,kBAAkB,CAAC;YAC9C,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YACpE,MAAM,KAAK,GAAuB;gBAChC,cAAc;aACf,CAAC;YACF,IAAI,CAAC,eAAe,CAClB,yBAAyB,CAAC,qBAAqB,EAC/C,KAAK,CACN,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,KAAK,GAAiC;gBAC1C,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;aACrC,CAAC;YACF,IAAI,CAAC,eAAe,CAClB,yBAAyB,CAAC,+BAA+B,EACzD,KAAK,CACN,CAAC;SACH;IACH,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3C,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAClB,mBAAmB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACzD,QAAQ,EACR,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CACd,CAAC;QACF,MAAM,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,OAAO;QAClB,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,OAAsB;QACxC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAChE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAiB;YAC3B,IAAI,EAAE,UAAU;SACjB,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAA2B;QAClD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAA8B;QACxD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;IAC1D,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAA6B;QACtD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,aAAa,CAAC,WAAW,EAAE;YAChC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAA2B;QAClD,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;QAClC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC;QACxC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAChC,mBAAmB,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;SACtD;aAAM;YACL,mBAAmB,CAAC,IAAI,EAAE,GAAG,MAAM,MAAM,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;SAClE;IACH,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAClC,OAAuC;QAEvC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,uBAAuB,CAC5B,WAAW,EACX,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EACjB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,CAC5B,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,IAAyB;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAoB;YAC9B,IAAI,EAAE,UAAU;SACjB,CAAC;QACF,IAAI,CAAC,eAAe,CAClB,yBAAyB,CAAC,uBAAuB,EACjD,MAAM,EACN,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAyB;QACzD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAqB;YAC/B,KAAK,EAAE,OAAO;SACf,CAAC;QACF,IAAI,CAAC,eAAe,CAClB,yBAAyB,CAAC,qBAAqB,EAC/C,MAAM,EACN,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,kBAAkB,CACxB,OAA+B,EAC/B,QAAmE;QAEnE,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC5B,MAAM,gBAAgB,GAA6B,EAAE,CAAC;YACtD,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBACvC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YACpD,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;gBAClC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC1B;SACF;IACH,CAAC;IAEM,yBAAyB,CAC9B,QAA8B,EAC9B,IAA2B;QAE3B,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,IAAI,KAAK,UAAU,EAAE;YACvB,OAAO,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC3C;aAAM;YACL,OAAO,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACxC;IACH,CAAC;IAEM,kCAAkC,CACvC,QAA8B,EAC9B,IAA2B;QAE3B,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,IAAI,IAAI,KAAK,UAAU,EAAE;YACvB,OAAO,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;SACrD;aAAM;YACL,OAAO,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;SAClD;IACH,CAAC;IAEM,6BAA6B,CAClC,UAAkC;QAElC,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;SACpE;QACD,OAAO,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAEM,wCAAwC;QAC7C,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,sCAAsC;QAC3C,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,kBAAkB,CACxB,cAA6C,EAC7C,cAA6C;QAE7C,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,IAAI,KAAI,IAAI,CAAC,CAAC;QACvE,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QACrE,MAAM,wBAAwB,GAC5B,IAAI,CAAC,8BAA8B,CAAC,cAAc,CAAC,CAAC;QACtD,MAAM,MAAM,GAAiB;YAC3B,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,gBAAgB;YAC5B,kBAAkB,EAAE,wBAAwB;SAC7C,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAC5B,UAAyC;QAEzC,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QACD,MAAM,MAAM,GAAmB;YAC7B,UAAU,EAAE,UAAU,CAAC,UAAU;SAClC,CAAC;QACF,IAAI,UAAU,YAAY,eAAe,EAAE;YACzC,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;YAC5C,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACpC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;SACnC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,IAAyB;QAChD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI,CAAC;SACb;QACD,MAAM,MAAM,GAAS;YACnB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC;YAClE,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,wBAAwB,CAC9B,QAA8B;QAE9B,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,IAAI,QAAQ,CAAC,YAAY,EAAE;YACzB,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SACzD;QACD,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;SAC7D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,4BAA4B,CAClC,YAAgC;QAEhC,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,8BAA8B,CACpC,UAAyC;QAEzC,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QACD,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAC7D,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO,IAAI,CAAC;SACb;QACD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,kBAAkB,CAAC;QACxE,MAAM,MAAM,GAAuB;YACjC,SAAS;SACV,CAAC;QACF,IAAI,UAAU,KAAK,IAAI,EAAE;YACvB,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;SAChC;QACD,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAM,CAAC,OAAO,GAAG,OAAqC,CAAC;SACxD;QACD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC/C,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC5B;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,KAAc;QACpC,IACE,KAAK,YAAY,MAAM;YACvB,SAAS,IAAI,KAAK;YAClB,OAAO,KAAK,CAAC,SAAS,CAAC,KAAK,QAAQ,EACpC;YACA,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC;SACzB;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAEO,sBAAsB;QAC5B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;;AA32BsB,iDAAuB,GAAG,iBAAiB,CAAC;AAC5C,+CAAqB,GAAG,eAAe,CAAC;AACxC,+CAAqB,GAAG,eAAe,CAAC;AACxC,yDAA+B,GACpD,yBAAyB,CAAC;AACL,iDAAuB,GAAG,uBAAuB,CAAC;AAClD,oDAA0B,GAC/C,+BAA+B,CAAC;AACX,0DAAgC,GACrD,kFAAkF,CAAC;AAC9D,2DAAiC,GACtD,6DAA6D,CAAC", "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nimport type {\n  ConfirmationR<PERSON>ult,\n  AuthCredential as FirebaseAuthCredential,\n  AuthProvider as FirebaseAuthProvider,\n  CustomParameters as FirebaseCustomParameters,\n  User as FirebaseUser,\n  UserCredential as FirebaseUserCredential,\n  UserInfo as FirebaseUserInfo,\n  UserMetadata as FirebaseUserMeatdata,\n} from 'firebase/auth';\nimport {\n  EmailAuthProvider,\n  FacebookAuthProvider,\n  GithubAuthProvider,\n  GoogleAuthProvider,\n  OAuthCredential,\n  OAuthProvider,\n  RecaptchaVerifier,\n  TwitterAuthProvider,\n  applyActionCode,\n  browserLocalPersistence,\n  browserSessionPersistence,\n  confirmPasswordReset,\n  connectAuthEmulator,\n  createUserWithEmailAndPassword,\n  deleteUser,\n  fetchSignInMethodsForEmail,\n  getAdditionalUserInfo,\n  getAuth,\n  getRedirectResult,\n  inMemoryPersistence,\n  indexedDBLocalPersistence,\n  isSignInWithEmailLink,\n  linkWithCredential,\n  linkWithPhoneNumber,\n  linkWithPopup,\n  linkWithRedirect,\n  reload,\n  revokeAccessToken,\n  sendEmailVerification,\n  sendPasswordResetEmail,\n  sendSignInLinkToEmail,\n  setPersistence,\n  signInAnonymously,\n  signInWithCustomToken,\n  signInWithEmailAndPassword,\n  signInWithEmailLink,\n  signInWithPhoneNumber,\n  signInWithPopup,\n  signInWithRedirect,\n  unlink,\n  updateEmail,\n  updatePassword,\n  updateProfile,\n  verifyBeforeUpdateEmail,\n} from 'firebase/auth';\n\nimport type {\n  AdditionalUserInfo,\n  ApplyActionCodeOptions,\n  AuthCredential,\n  AuthStateChange,\n  CheckAppTrackingTransparencyPermissionResult,\n  ConfirmPasswordResetOptions,\n  ConfirmVerificationCodeOptions,\n  CreateUserWithEmailAndPasswordOptions,\n  FetchSignInMethodsForEmailOptions,\n  FetchSignInMethodsForEmailResult,\n  FirebaseAuthenticationPlugin,\n  GetCurrentUserResult,\n  GetIdTokenOptions,\n  GetIdTokenResult,\n  GetTenantIdResult,\n  IsSignInWithEmailLinkOptions,\n  IsSignInWithEmailLinkResult,\n  LinkResult,\n  LinkWithEmailAndPasswordOptions,\n  LinkWithEmailLinkOptions,\n  LinkWithOAuthOptions,\n  LinkWithPhoneNumberOptions,\n  PhoneCodeSentEvent,\n  PhoneVerificationFailedEvent,\n  RequestAppTrackingTransparencyPermissionResult,\n  RevokeAccessTokenOptions,\n  SendEmailVerificationOptions,\n  SendPasswordResetEmailOptions,\n  SendSignInLinkToEmailOptions,\n  SetLanguageCodeOptions,\n  SetPersistenceOptions,\n  SetTenantIdOptions,\n  SignInResult,\n  SignInWithCustomTokenOptions,\n  SignInWithEmailAndPasswordOptions,\n  SignInWithEmailLinkOptions,\n  SignInWithGoogleOptions,\n  SignInWithOAuthOptions,\n  SignInWithOpenIdConnectOptions,\n  SignInWithPhoneNumberOptions,\n  UnlinkOptions,\n  UnlinkResult,\n  UpdateEmailOptions,\n  UpdatePasswordOptions,\n  UpdateProfileOptions,\n  UseEmulatorOptions,\n  User,\n  UserInfo,\n  UserMetadata,\n  VerifyBeforeUpdateEmailOptions,\n} from './definitions';\nimport { Persistence, ProviderId } from './definitions';\n\nexport class FirebaseAuthenticationWeb\n  extends WebPlugin\n  implements FirebaseAuthenticationPlugin\n{\n  public static readonly AUTH_STATE_CHANGE_EVENT = 'authStateChange';\n  public static readonly ID_TOKEN_CHANGE_EVENT = 'idTokenChange';\n  public static readonly PHONE_CODE_SENT_EVENT = 'phoneCodeSent';\n  public static readonly PHONE_VERIFICATION_FAILED_EVENT =\n    'phoneVerificationFailed';\n  public static readonly ERROR_NO_USER_SIGNED_IN = 'No user is signed in.';\n  public static readonly ERROR_PHONE_NUMBER_MISSING =\n    'phoneNumber must be provided.';\n  public static readonly ERROR_RECAPTCHA_VERIFIER_MISSING =\n    'recaptchaVerifier must be provided and must be an instance of RecaptchaVerifier.';\n  public static readonly ERROR_CONFIRMATION_RESULT_MISSING =\n    'No confirmation result with this verification id was found.';\n\n  private lastConfirmationResult: Map<string, ConfirmationResult> = new Map();\n\n  constructor() {\n    super();\n    const auth = getAuth();\n    auth.onAuthStateChanged(user => this.handleAuthStateChange(user));\n    auth.onIdTokenChanged(user => void this.handleIdTokenChange(user));\n  }\n\n  public async applyActionCode(options: ApplyActionCodeOptions): Promise<void> {\n    const auth = getAuth();\n    return applyActionCode(auth, options.oobCode);\n  }\n\n  public async createUserWithEmailAndPassword(\n    options: CreateUserWithEmailAndPasswordOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await createUserWithEmailAndPassword(\n      auth,\n      options.email,\n      options.password,\n    );\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async confirmPasswordReset(\n    options: ConfirmPasswordResetOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return confirmPasswordReset(auth, options.oobCode, options.newPassword);\n  }\n\n  public async confirmVerificationCode(\n    options: ConfirmVerificationCodeOptions,\n  ): Promise<SignInResult> {\n    const { verificationCode, verificationId } = options;\n    const confirmationResult = this.lastConfirmationResult.get(verificationId);\n    if (!confirmationResult) {\n      throw new Error(\n        FirebaseAuthenticationWeb.ERROR_CONFIRMATION_RESULT_MISSING,\n      );\n    }\n    const userCredential = await confirmationResult.confirm(verificationCode);\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async deleteUser(): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return deleteUser(currentUser);\n  }\n\n  public async fetchSignInMethodsForEmail(\n    options: FetchSignInMethodsForEmailOptions,\n  ): Promise<FetchSignInMethodsForEmailResult> {\n    const auth = getAuth();\n    const signInMethods = await fetchSignInMethodsForEmail(auth, options.email);\n    return {\n      signInMethods,\n    };\n  }\n\n  public async getPendingAuthResult(): Promise<SignInResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async getCurrentUser(): Promise<GetCurrentUserResult> {\n    const auth = getAuth();\n    const userResult = this.createUserResult(auth.currentUser);\n    const result: GetCurrentUserResult = {\n      user: userResult,\n    };\n    return result;\n  }\n\n  public async getIdToken(\n    options?: GetIdTokenOptions,\n  ): Promise<GetIdTokenResult> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    const idToken = await auth.currentUser.getIdToken(options?.forceRefresh);\n    const result: GetIdTokenResult = {\n      token: idToken || '',\n    };\n    return result;\n  }\n\n  public async getRedirectResult(): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await getRedirectResult(auth);\n    const authCredential = userCredential\n      ? OAuthProvider.credentialFromResult(userCredential)\n      : null;\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async getTenantId(): Promise<GetTenantIdResult> {\n    const auth = getAuth();\n    return {\n      tenantId: auth.tenantId,\n    };\n  }\n\n  public async isSignInWithEmailLink(\n    options: IsSignInWithEmailLinkOptions,\n  ): Promise<IsSignInWithEmailLinkResult> {\n    const auth = getAuth();\n    return {\n      isSignInWithEmailLink: isSignInWithEmailLink(auth, options.emailLink),\n    };\n  }\n\n  public async linkWithApple(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new OAuthProvider(ProviderId.APPLE);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithEmailAndPassword(\n    options: LinkWithEmailAndPasswordOptions,\n  ): Promise<LinkResult> {\n    const authCredential = EmailAuthProvider.credential(\n      options.email,\n      options.password,\n    );\n    const userCredential =\n      await this.linkCurrentUserWithCredential(authCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithEmailLink(\n    options: LinkWithEmailLinkOptions,\n  ): Promise<LinkResult> {\n    const authCredential = EmailAuthProvider.credentialWithLink(\n      options.email,\n      options.emailLink,\n    );\n    const userCredential =\n      await this.linkCurrentUserWithCredential(authCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithFacebook(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new FacebookAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      FacebookAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithGameCenter(): Promise<LinkResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async linkWithGithub(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new GithubAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GithubAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithGoogle(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new GoogleAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GoogleAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithMicrosoft(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new OAuthProvider(ProviderId.MICROSOFT);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithOpenIdConnect(\n    options: SignInWithOpenIdConnectOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(options.providerId);\n    this.applySignInOptions(options, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithPhoneNumber(\n    options: LinkWithPhoneNumberOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    if (!options.phoneNumber) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n    }\n    if (\n      !options.recaptchaVerifier ||\n      !(options.recaptchaVerifier instanceof RecaptchaVerifier)\n    ) {\n      throw new Error(\n        FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING,\n      );\n    }\n    try {\n      const confirmationResult = await linkWithPhoneNumber(\n        currentUser,\n        options.phoneNumber,\n        options.recaptchaVerifier,\n      );\n      const { verificationId } = confirmationResult;\n      this.lastConfirmationResult.set(verificationId, confirmationResult);\n      const event: PhoneCodeSentEvent = {\n        verificationId,\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT,\n        event,\n      );\n    } catch (error) {\n      const event: PhoneVerificationFailedEvent = {\n        message: this.getErrorMessage(error),\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT,\n        event,\n      );\n    }\n  }\n\n  public async linkWithPlayGames(): Promise<LinkResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async linkWithTwitter(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new TwitterAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      TwitterAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithYahoo(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new OAuthProvider(ProviderId.YAHOO);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async reload(): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return reload(currentUser);\n  }\n\n  public async revokeAccessToken(\n    options: RevokeAccessTokenOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return revokeAccessToken(auth, options.token);\n  }\n\n  public async sendEmailVerification(\n    options: SendEmailVerificationOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return sendEmailVerification(currentUser, options?.actionCodeSettings);\n  }\n\n  public async sendPasswordResetEmail(\n    options: SendPasswordResetEmailOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return sendPasswordResetEmail(\n      auth,\n      options.email,\n      options.actionCodeSettings,\n    );\n  }\n\n  public async sendSignInLinkToEmail(\n    options: SendSignInLinkToEmailOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return sendSignInLinkToEmail(\n      auth,\n      options.email,\n      options.actionCodeSettings,\n    );\n  }\n\n  public async setLanguageCode(options: SetLanguageCodeOptions): Promise<void> {\n    const auth = getAuth();\n    auth.languageCode = options.languageCode;\n  }\n\n  public async setPersistence(options: SetPersistenceOptions): Promise<void> {\n    const auth = getAuth();\n    switch (options.persistence) {\n      case Persistence.BrowserLocal:\n        await setPersistence(auth, browserLocalPersistence);\n        break;\n      case Persistence.BrowserSession:\n        await setPersistence(auth, browserSessionPersistence);\n        break;\n      case Persistence.IndexedDbLocal:\n        await setPersistence(auth, indexedDBLocalPersistence);\n        break;\n      case Persistence.InMemory:\n        await setPersistence(auth, inMemoryPersistence);\n        break;\n    }\n  }\n\n  public async setTenantId(options: SetTenantIdOptions): Promise<void> {\n    const auth = getAuth();\n    auth.tenantId = options.tenantId;\n  }\n\n  public async signInAnonymously(): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInAnonymously(auth);\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithApple(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(ProviderId.APPLE);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithCustomToken(\n    options: SignInWithCustomTokenOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInWithCustomToken(auth, options.token);\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithEmailAndPassword(\n    options: SignInWithEmailAndPasswordOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInWithEmailAndPassword(\n      auth,\n      options.email,\n      options.password,\n    );\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithEmailLink(\n    options: SignInWithEmailLinkOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInWithEmailLink(\n      auth,\n      options.email,\n      options.emailLink,\n    );\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithFacebook(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new FacebookAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      FacebookAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithGithub(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new GithubAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GithubAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithGoogle(\n    options?: SignInWithGoogleOptions,\n  ): Promise<SignInResult> {\n    const provider = new GoogleAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GoogleAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithMicrosoft(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(ProviderId.MICROSOFT);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithOpenIdConnect(\n    options: SignInWithOpenIdConnectOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(options.providerId);\n    this.applySignInOptions(options, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithPhoneNumber(\n    options: SignInWithPhoneNumberOptions,\n  ): Promise<void> {\n    if (!options.phoneNumber) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n    }\n    if (\n      !options.recaptchaVerifier ||\n      !(options.recaptchaVerifier instanceof RecaptchaVerifier)\n    ) {\n      throw new Error(\n        FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING,\n      );\n    }\n    const auth = getAuth();\n    try {\n      const confirmationResult = await signInWithPhoneNumber(\n        auth,\n        options.phoneNumber,\n        options.recaptchaVerifier,\n      );\n      const { verificationId } = confirmationResult;\n      this.lastConfirmationResult.set(verificationId, confirmationResult);\n      const event: PhoneCodeSentEvent = {\n        verificationId,\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT,\n        event,\n      );\n    } catch (error) {\n      const event: PhoneVerificationFailedEvent = {\n        message: this.getErrorMessage(error),\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT,\n        event,\n      );\n    }\n  }\n\n  public async signInWithPlayGames(): Promise<SignInResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async signInWithGameCenter(): Promise<SignInResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async signInWithTwitter(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new TwitterAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      TwitterAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithYahoo(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(ProviderId.YAHOO);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signOut(): Promise<void> {\n    const auth = getAuth();\n    await auth.signOut();\n  }\n\n  public async unlink(options: UnlinkOptions): Promise<UnlinkResult> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    const user = await unlink(auth.currentUser, options.providerId);\n    const userResult = this.createUserResult(user);\n    const result: UnlinkResult = {\n      user: userResult,\n    };\n    return result;\n  }\n\n  public async updateEmail(options: UpdateEmailOptions): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return updateEmail(currentUser, options.newEmail);\n  }\n\n  public async updatePassword(options: UpdatePasswordOptions): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return updatePassword(currentUser, options.newPassword);\n  }\n\n  public async updateProfile(options: UpdateProfileOptions): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return updateProfile(currentUser, {\n      displayName: options.displayName,\n      photoURL: options.photoUrl,\n    });\n  }\n\n  public async useAppLanguage(): Promise<void> {\n    const auth = getAuth();\n    auth.useDeviceLanguage();\n  }\n\n  public async useEmulator(options: UseEmulatorOptions): Promise<void> {\n    const auth = getAuth();\n    const port = options.port || 9099;\n    const scheme = options.scheme || 'http';\n    if (options.host.includes('://')) {\n      connectAuthEmulator(auth, `${options.host}:${port}`);\n    } else {\n      connectAuthEmulator(auth, `${scheme}://${options.host}:${port}`);\n    }\n  }\n\n  public async verifyBeforeUpdateEmail(\n    options: VerifyBeforeUpdateEmailOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return verifyBeforeUpdateEmail(\n      currentUser,\n      options?.newEmail,\n      options?.actionCodeSettings,\n    );\n  }\n\n  private handleAuthStateChange(user: FirebaseUser | null): void {\n    const userResult = this.createUserResult(user);\n    const change: AuthStateChange = {\n      user: userResult,\n    };\n    this.notifyListeners(\n      FirebaseAuthenticationWeb.AUTH_STATE_CHANGE_EVENT,\n      change,\n      true,\n    );\n  }\n\n  private async handleIdTokenChange(user: FirebaseUser | null): Promise<void> {\n    if (!user) {\n      return;\n    }\n    const idToken = await user.getIdToken(false);\n    const result: GetIdTokenResult = {\n      token: idToken,\n    };\n    this.notifyListeners(\n      FirebaseAuthenticationWeb.ID_TOKEN_CHANGE_EVENT,\n      result,\n      true,\n    );\n  }\n\n  private applySignInOptions(\n    options: SignInWithOAuthOptions,\n    provider: OAuthProvider | GoogleAuthProvider | FacebookAuthProvider,\n  ) {\n    if (options.customParameters) {\n      const customParameters: FirebaseCustomParameters = {};\n      options.customParameters.map(parameter => {\n        customParameters[parameter.key] = parameter.value;\n      });\n      provider.setCustomParameters(customParameters);\n    }\n    if (options.scopes) {\n      for (const scope of options.scopes) {\n        provider.addScope(scope);\n      }\n    }\n  }\n\n  public signInWithPopupOrRedirect(\n    provider: FirebaseAuthProvider,\n    mode?: 'popup' | 'redirect',\n  ): Promise<FirebaseUserCredential | never> {\n    const auth = getAuth();\n    if (mode === 'redirect') {\n      return signInWithRedirect(auth, provider);\n    } else {\n      return signInWithPopup(auth, provider);\n    }\n  }\n\n  public linkCurrentUserWithPopupOrRedirect(\n    provider: FirebaseAuthProvider,\n    mode?: 'popup' | 'redirect',\n  ): Promise<FirebaseUserCredential | never> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    if (mode === 'redirect') {\n      return linkWithRedirect(auth.currentUser, provider);\n    } else {\n      return linkWithPopup(auth.currentUser, provider);\n    }\n  }\n\n  public linkCurrentUserWithCredential(\n    credential: FirebaseAuthCredential,\n  ): Promise<FirebaseUserCredential> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return linkWithCredential(auth.currentUser, credential);\n  }\n\n  public requestAppTrackingTransparencyPermission(): Promise<RequestAppTrackingTransparencyPermissionResult> {\n    this.throwNotAvailableError();\n  }\n\n  public checkAppTrackingTransparencyPermission(): Promise<CheckAppTrackingTransparencyPermissionResult> {\n    this.throwNotAvailableError();\n  }\n\n  private createSignInResult(\n    userCredential: FirebaseUserCredential | null,\n    authCredential: FirebaseAuthCredential | null,\n  ): SignInResult {\n    const userResult = this.createUserResult(userCredential?.user || null);\n    const credentialResult = this.createCredentialResult(authCredential);\n    const additionalUserInfoResult =\n      this.createAdditionalUserInfoResult(userCredential);\n    const result: SignInResult = {\n      user: userResult,\n      credential: credentialResult,\n      additionalUserInfo: additionalUserInfoResult,\n    };\n    return result;\n  }\n\n  private createCredentialResult(\n    credential: FirebaseAuthCredential | null,\n  ): AuthCredential | null {\n    if (!credential) {\n      return null;\n    }\n    const result: AuthCredential = {\n      providerId: credential.providerId,\n    };\n    if (credential instanceof OAuthCredential) {\n      result.accessToken = credential.accessToken;\n      result.idToken = credential.idToken;\n      result.secret = credential.secret;\n    }\n    return result;\n  }\n\n  private createUserResult(user: FirebaseUser | null): User | null {\n    if (!user) {\n      return null;\n    }\n    const result: User = {\n      displayName: user.displayName,\n      email: user.email,\n      emailVerified: user.emailVerified,\n      isAnonymous: user.isAnonymous,\n      metadata: this.createUserMetadataResult(user.metadata),\n      phoneNumber: user.phoneNumber,\n      photoUrl: user.photoURL,\n      providerData: this.createUserProviderDataResult(user.providerData),\n      providerId: user.providerId,\n      tenantId: user.tenantId,\n      uid: user.uid,\n    };\n    return result;\n  }\n\n  private createUserMetadataResult(\n    metadata: FirebaseUserMeatdata,\n  ): UserMetadata {\n    const result: UserMetadata = {};\n    if (metadata.creationTime) {\n      result.creationTime = Date.parse(metadata.creationTime);\n    }\n    if (metadata.lastSignInTime) {\n      result.lastSignInTime = Date.parse(metadata.lastSignInTime);\n    }\n    return result;\n  }\n\n  private createUserProviderDataResult(\n    providerData: FirebaseUserInfo[],\n  ): UserInfo[] {\n    return providerData.map(data => ({\n      displayName: data.displayName,\n      email: data.email,\n      phoneNumber: data.phoneNumber,\n      photoUrl: data.photoURL,\n      providerId: data.providerId,\n      uid: data.uid,\n    }));\n  }\n\n  private createAdditionalUserInfoResult(\n    credential: FirebaseUserCredential | null,\n  ): AdditionalUserInfo | null {\n    if (!credential) {\n      return null;\n    }\n    const additionalUserInfo = getAdditionalUserInfo(credential);\n    if (!additionalUserInfo) {\n      return null;\n    }\n    const { isNewUser, profile, providerId, username } = additionalUserInfo;\n    const result: AdditionalUserInfo = {\n      isNewUser,\n    };\n    if (providerId !== null) {\n      result.providerId = providerId;\n    }\n    if (profile !== null) {\n      result.profile = profile as { [key: string]: unknown };\n    }\n    if (username !== null && username !== undefined) {\n      result.username = username;\n    }\n    return result;\n  }\n\n  private getErrorMessage(error: unknown): string {\n    if (\n      error instanceof Object &&\n      'message' in error &&\n      typeof error['message'] === 'string'\n    ) {\n      return error['message'];\n    }\n    return JSON.stringify(error);\n  }\n\n  private throwNotAvailableError(): never {\n    throw new Error('Not available on web.');\n  }\n}\n"]}