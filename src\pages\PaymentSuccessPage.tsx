import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useApp } from '../contexts/AppContext'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardHeader } from '../components/ui/card'
import { CheckCircle, Package, Home, ShoppingBag } from 'lucide-react'
import { container } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
`

const SuccessCard = styled(Card)`
  max-width: 32rem;
  text-align: center;
  box-shadow: ${({ theme }) => theme.shadows.xl};
`

const SuccessIcon = styled.div`
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[6]};
  color: white;
`

const SuccessTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const SuccessMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  line-height: 1.6;
`

const SessionInfo = styled.div`
  background: ${({ theme }) => theme.colors.gray[50]};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`

const SessionLabel = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const SessionId = styled.code`
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  background: ${({ theme }) => theme.colors.gray[100]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  color: ${({ theme }) => theme.colors.text.primary};
  word-break: break-all;
`

const ActionButtons = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
  
  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: center;
  }
`

const NextSteps = styled.div`
  text-align: left;
  margin-top: ${({ theme }) => theme.spacing[6]};
  padding-top: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`

const StepItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const StepIcon = styled.div`
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.primary[100]};
  color: ${({ theme }) => theme.colors.primary[600]};
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`

/**
 * Página de éxito después del pago
 */
export const PaymentSuccessPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { dispatch } = useApp()
  const [sessionId, setSessionId] = useState<string>('')

  useEffect(() => {
    const sessionIdParam = searchParams.get('session_id')
    if (sessionIdParam) {
      setSessionId(sessionIdParam)
      // Limpiar el carrito después del pago exitoso
      dispatch({ type: 'CLEAR_CART' })
    }
  }, [searchParams, dispatch])

  return (
    <PageContainer>
      <SuccessCard>
        <CardHeader>
          <SuccessIcon>
            <CheckCircle size={32} />
          </SuccessIcon>
          <SuccessTitle>¡Pago Exitoso!</SuccessTitle>
          <SuccessMessage>
            Tu pedido ha sido procesado correctamente. Recibirás un email de confirmación en breve.
          </SuccessMessage>
        </CardHeader>

        <CardContent>
          {sessionId && (
            <SessionInfo>
              <SessionLabel>ID de Sesión de Stripe:</SessionLabel>
              <SessionId>{sessionId}</SessionId>
            </SessionInfo>
          )}

          <ActionButtons>
            <Button 
              onClick={() => navigate('/')}
              variant="default"
            >
              <Home size={16} style={{ marginRight: '0.5rem' }} />
              Volver al Inicio
            </Button>
            <Button 
              onClick={() => navigate('/orders')}
              variant="outline"
            >
              <Package size={16} style={{ marginRight: '0.5rem' }} />
              Ver Mis Pedidos
            </Button>
          </ActionButtons>

          <NextSteps>
            <h3 style={{ 
              fontSize: '1rem', 
              fontWeight: '600', 
              marginBottom: '1rem',
              color: '#374151'
            }}>
              Próximos pasos:
            </h3>
            
            <StepItem>
              <StepIcon>1</StepIcon>
              <span>Recibirás un email de confirmación con los detalles de tu pedido</span>
            </StepItem>
            
            <StepItem>
              <StepIcon>2</StepIcon>
              <span>Procesaremos tu pedido en las próximas 24 horas</span>
            </StepItem>
            
            <StepItem>
              <StepIcon>3</StepIcon>
              <span>Te notificaremos cuando tu pedido esté en camino</span>
            </StepItem>
            
            <StepItem>
              <StepIcon>
                <ShoppingBag size={12} />
              </StepIcon>
              <span>Puedes seguir el estado de tu pedido en tu perfil</span>
            </StepItem>
          </NextSteps>
        </CardContent>
      </SuccessCard>
    </PageContainer>
  )
}
