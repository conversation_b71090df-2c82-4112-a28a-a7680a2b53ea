import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { ProductService } from '../services/productService'
import { Card, CardContent } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { Product } from '../../types'
import { Package, Plus, Edit, Trash2, Search, Eye, EyeOff } from 'lucide-react'
import { formatPrice } from '../lib/utils'
import { container, hoverLift } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  // padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: ${({ theme }) => theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.muted};
  width: 1rem;
  height: 1rem;
`

const SearchInput = styled(Input)`
  padding-left: ${({ theme }) => theme.spacing[10]};
`

const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing[6]};
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: 1280px) {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
`

const ProductCard = styled(Card)`
  ${hoverLift}
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  background: white;
`

const ProductImage = styled.img`
  width: 100%;
  height: 12rem;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0 0;
`

const ProductInfo = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};
`

const ProductName = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const ProductDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  line-height: 1.5;
`

const FormatsContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const FormatItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`

const FormatInfo = styled.div`
  flex: 1;
`

const FormatName = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const FormatDetails = styled.div`
  color: ${({ theme }) => theme.colors.text.muted};
  font-size: ${({ theme }) => theme.fontSizes.xs};
`

const ProductActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`

const StatusBadge = styled.span<{ $active: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ $active }) =>
    $active ? '#dcfce7' : '#fee2e2'};
  color: ${({ $active }) =>
    $active ? '#166534' : '#dc2626'};
`

/**
 * Página de gestión de productos para administradores
 */
export const ProductManagementPage: React.FC = () => {
  const navigate = useNavigate()
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadProducts()
  }, [])

  useEffect(() => {
    // Filtrar productos por búsqueda
    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.category && product.category.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    setFilteredProducts(filtered)
  }, [products, searchTerm])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const allProducts = await ProductService.getAllProducts()
      setProducts(allProducts)
    } catch (error) {
      console.error('Error cargando productos:', error)
    } finally {
      setLoading(false)
    }
  }





  const handleEdit = (product: Product) => {
    navigate(`/admin/products/edit/${product.id}`)
  }

  const handleCreate = () => {
    navigate('/admin/products/create')
  }

  const handleDelete = async (productId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este producto?')) return

    try {
      await ProductService.deleteProduct(productId)
      await loadProducts()
    } catch (error) {
      console.error('Error eliminando producto:', error)
    }
  }

  const handleToggleActive = async (product: Product) => {
    try {
      await ProductService.updateProduct(product.id, {
        isActive: !product.isActive
      })
      await loadProducts()
    } catch (error) {
      console.error('Error actualizando estado del producto:', error)
    }
  }



  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>
          <Package size={32} />
          Gestión de Productos
        </PageTitle>
        <Button onClick={handleCreate}>
          <Plus className="w-4 h-4 mr-2" />
          Nuevo Producto
        </Button>


      </PageHeader>

      {/* Búsqueda */}
      <SearchContainer>
        <SearchIcon />
        <SearchInput
          placeholder="Buscar productos por nombre, descripción o categoría..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>

      {loading ? (
        <LoadingSpinner size="lg" className="h-64" />
      ) : filteredProducts.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchTerm ? 'No se encontraron productos' : 'No hay productos'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm 
                ? 'Intenta con otros términos de búsqueda'
                : 'Comienza agregando tu primer producto'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <ProductsGrid>
          {filteredProducts.map(product => (
            <ProductCard key={product.id} style={{ opacity: !product.isActive ? 0.6 : 1 }}>
              <ProductImage
                src={product.imageUrl}
                alt={product.name}
              />

              <ProductInfo>
                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <ProductName>{product.name}</ProductName>
                  <StatusBadge $active={product.isActive}>
                    {product.isActive ? <Eye size={12} /> : <EyeOff size={12} />}
                    {product.isActive ? 'Activo' : 'Inactivo'}
                  </StatusBadge>
                </div>

                <ProductDescription>
                  {product.description}
                </ProductDescription>

                {/* Mostrar formatos */}
                <FormatsContainer>
                  {product.formats.map((format, index) => (
                    <FormatItem key={index}>
                      <FormatInfo>
                        <FormatName>{format.name}</FormatName>
                        <FormatDetails>
                          {format.description} - {formatPrice(format.price)}
                        </FormatDetails>
                      </FormatInfo>
                      <div style={{ fontSize: '0.75rem', color: format.stock === -1 ? '#10B981' : format.stock <= 5 ? '#EF4444' : '#6B7280' }}>
                        {format.stock === -1 ? '∞' : `${format.stock} unidades`}
                      </div>
                    </FormatItem>
                  ))}
                </FormatsContainer>
                
                {product.category && (
                  <div style={{ fontSize: '0.75rem', color: '#6B7280', marginBottom: '0.75rem' }}>
                    Categoría: {product.category}
                  </div>
                )}
              </ProductInfo>

              <ProductActions>
                  <Button variant="outline" size="sm" onClick={() => handleEdit(product)}>
                    <Edit className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleToggleActive(product)}
                  >
                    {product.isActive ? (
                      <>
                        <EyeOff className="w-4 h-4 mr-1" />
                        Ocultar
                      </>
                    ) : (
                      <>
                        <Eye className="w-4 h-4 mr-1" />
                        Mostrar
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDelete(product.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Eliminar
                  </Button>
                </ProductActions>
            </ProductCard>
          ))}
        </ProductsGrid>
      )}
    </PageContainer>
  )
}
