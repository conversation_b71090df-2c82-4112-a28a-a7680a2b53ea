import{W as N,g as s,a as S,c as C,b as y,d as O,f,e as v,O as a,i as U,P as l,E as g,F as d,G as h,h as R,R as w,l as _,T as I,r as P,j as k,s as T,k as A,m as W,n as E,o as m,p as G,q as L,t as D,u as V,v as M,w as H,x as F,y as B,z as x,A as p,B as $,C as Y,D as K,H as j,I as q,J,K as z,L as Q,M as b,N as X,Q as Z,S as ee}from"./index-T7GRMj5a.js";class i extends N{constructor(){super(),this.lastConfirmationResult=new Map;const e=s();e.onAuthStateChanged(t=>this.handleAuthStateChange(t)),e.onIdTokenChanged(t=>void this.handleIdTokenChange(t))}async applyActionCode(e){const t=s();return S(t,e.oobCode)}async createUserWithEmailAndPassword(e){const t=s(),r=await C(t,e.email,e.password);return this.createSignInResult(r,null)}async confirmPasswordReset(e){const t=s();return y(t,e.oobCode,e.newPassword)}async confirmVerificationCode(e){const{verificationCode:t,verificationId:r}=e,n=this.lastConfirmationResult.get(r);if(!n)throw new Error(i.ERROR_CONFIRMATION_RESULT_MISSING);const o=await n.confirm(t);return this.createSignInResult(o,null)}async deleteUser(){const t=s().currentUser;if(!t)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return O(t)}async fetchSignInMethodsForEmail(e){const t=s();return{signInMethods:await f(t,e.email)}}async getPendingAuthResult(){this.throwNotAvailableError()}async getCurrentUser(){const e=s();return{user:this.createUserResult(e.currentUser)}}async getIdToken(e){const t=s();if(!t.currentUser)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return{token:await t.currentUser.getIdToken(e?.forceRefresh)||""}}async getRedirectResult(){const e=s(),t=await v(e),r=t?a.credentialFromResult(t):null;return this.createSignInResult(t,r)}async getTenantId(){return{tenantId:s().tenantId}}async isSignInWithEmailLink(e){const t=s();return{isSignInWithEmailLink:U(t,e.emailLink)}}async linkWithApple(e){const t=new a(l.APPLE);this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithEmailAndPassword(e){const t=g.credential(e.email,e.password),r=await this.linkCurrentUserWithCredential(t);return this.createSignInResult(r,t)}async linkWithEmailLink(e){const t=g.credentialWithLink(e.email,e.emailLink),r=await this.linkCurrentUserWithCredential(t);return this.createSignInResult(r,t)}async linkWithFacebook(e){const t=new d;this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=d.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithGameCenter(){this.throwNotAvailableError()}async linkWithGithub(e){const t=new h;this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=h.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithGoogle(e){const t=new R;this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=R.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithMicrosoft(e){const t=new a(l.MICROSOFT);this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithOpenIdConnect(e){const t=new a(e.providerId);this.applySignInOptions(e,t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithPhoneNumber(e){const r=s().currentUser;if(!r)throw new Error(i.ERROR_NO_USER_SIGNED_IN);if(!e.phoneNumber)throw new Error(i.ERROR_PHONE_NUMBER_MISSING);if(!e.recaptchaVerifier||!(e.recaptchaVerifier instanceof w))throw new Error(i.ERROR_RECAPTCHA_VERIFIER_MISSING);try{const n=await _(r,e.phoneNumber,e.recaptchaVerifier),{verificationId:o}=n;this.lastConfirmationResult.set(o,n);const c={verificationId:o};this.notifyListeners(i.PHONE_CODE_SENT_EVENT,c)}catch(n){const o={message:this.getErrorMessage(n)};this.notifyListeners(i.PHONE_VERIFICATION_FAILED_EVENT,o)}}async linkWithPlayGames(){this.throwNotAvailableError()}async linkWithTwitter(e){const t=new I;this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=I.credentialFromResult(r);return this.createSignInResult(r,n)}async linkWithYahoo(e){const t=new a(l.YAHOO);this.applySignInOptions(e||{},t);const r=await this.linkCurrentUserWithPopupOrRedirect(t,e?.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async reload(){const t=s().currentUser;if(!t)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return P(t)}async revokeAccessToken(e){const t=s();return k(t,e.token)}async sendEmailVerification(e){const r=s().currentUser;if(!r)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return T(r,e?.actionCodeSettings)}async sendPasswordResetEmail(e){const t=s();return A(t,e.email,e.actionCodeSettings)}async sendSignInLinkToEmail(e){const t=s();return W(t,e.email,e.actionCodeSettings)}async setLanguageCode(e){const t=s();t.languageCode=e.languageCode}async setPersistence(e){const t=s();switch(e.persistence){case E.BrowserLocal:await m(t,ee);break;case E.BrowserSession:await m(t,Z);break;case E.IndexedDbLocal:await m(t,X);break;case E.InMemory:await m(t,b);break}}async setTenantId(e){const t=s();t.tenantId=e.tenantId}async signInAnonymously(){const e=s(),t=await G(e);return this.createSignInResult(t,null)}async signInWithApple(e){const t=new a(l.APPLE);this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithCustomToken(e){const t=s(),r=await L(t,e.token);return this.createSignInResult(r,null)}async signInWithEmailAndPassword(e){const t=s(),r=await D(t,e.email,e.password);return this.createSignInResult(r,null)}async signInWithEmailLink(e){const t=s(),r=await V(t,e.email,e.emailLink);return this.createSignInResult(r,null)}async signInWithFacebook(e){const t=new d;this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=d.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithGithub(e){const t=new h;this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=h.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithGoogle(e){const t=new R;this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=R.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithMicrosoft(e){const t=new a(l.MICROSOFT);this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithOpenIdConnect(e){const t=new a(e.providerId);this.applySignInOptions(e,t);const r=await this.signInWithPopupOrRedirect(t,e.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithPhoneNumber(e){if(!e.phoneNumber)throw new Error(i.ERROR_PHONE_NUMBER_MISSING);if(!e.recaptchaVerifier||!(e.recaptchaVerifier instanceof w))throw new Error(i.ERROR_RECAPTCHA_VERIFIER_MISSING);const t=s();try{const r=await M(t,e.phoneNumber,e.recaptchaVerifier),{verificationId:n}=r;this.lastConfirmationResult.set(n,r);const o={verificationId:n};this.notifyListeners(i.PHONE_CODE_SENT_EVENT,o)}catch(r){const n={message:this.getErrorMessage(r)};this.notifyListeners(i.PHONE_VERIFICATION_FAILED_EVENT,n)}}async signInWithPlayGames(){this.throwNotAvailableError()}async signInWithGameCenter(){this.throwNotAvailableError()}async signInWithTwitter(e){const t=new I;this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=I.credentialFromResult(r);return this.createSignInResult(r,n)}async signInWithYahoo(e){const t=new a(l.YAHOO);this.applySignInOptions(e||{},t);const r=await this.signInWithPopupOrRedirect(t,e?.mode),n=a.credentialFromResult(r);return this.createSignInResult(r,n)}async signOut(){await s().signOut()}async unlink(e){const t=s();if(!t.currentUser)throw new Error(i.ERROR_NO_USER_SIGNED_IN);const r=await H(t.currentUser,e.providerId);return{user:this.createUserResult(r)}}async updateEmail(e){const r=s().currentUser;if(!r)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return F(r,e.newEmail)}async updatePassword(e){const r=s().currentUser;if(!r)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return B(r,e.newPassword)}async updateProfile(e){const r=s().currentUser;if(!r)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return x(r,{displayName:e.displayName,photoURL:e.photoUrl})}async useAppLanguage(){s().useDeviceLanguage()}async useEmulator(e){const t=s(),r=e.port||9099,n=e.scheme||"http";e.host.includes("://")?p(t,`${e.host}:${r}`):p(t,`${n}://${e.host}:${r}`)}async verifyBeforeUpdateEmail(e){const r=s().currentUser;if(!r)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return $(r,e?.newEmail,e?.actionCodeSettings)}handleAuthStateChange(e){const r={user:this.createUserResult(e)};this.notifyListeners(i.AUTH_STATE_CHANGE_EVENT,r,!0)}async handleIdTokenChange(e){if(!e)return;const r={token:await e.getIdToken(!1)};this.notifyListeners(i.ID_TOKEN_CHANGE_EVENT,r,!0)}applySignInOptions(e,t){if(e.customParameters){const r={};e.customParameters.map(n=>{r[n.key]=n.value}),t.setCustomParameters(r)}if(e.scopes)for(const r of e.scopes)t.addScope(r)}signInWithPopupOrRedirect(e,t){const r=s();return t==="redirect"?Y(r,e):K(r,e)}linkCurrentUserWithPopupOrRedirect(e,t){const r=s();if(!r.currentUser)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return t==="redirect"?j(r.currentUser,e):q(r.currentUser,e)}linkCurrentUserWithCredential(e){const t=s();if(!t.currentUser)throw new Error(i.ERROR_NO_USER_SIGNED_IN);return J(t.currentUser,e)}requestAppTrackingTransparencyPermission(){this.throwNotAvailableError()}checkAppTrackingTransparencyPermission(){this.throwNotAvailableError()}createSignInResult(e,t){const r=this.createUserResult(e?.user||null),n=this.createCredentialResult(t),o=this.createAdditionalUserInfoResult(e);return{user:r,credential:n,additionalUserInfo:o}}createCredentialResult(e){if(!e)return null;const t={providerId:e.providerId};return e instanceof z&&(t.accessToken=e.accessToken,t.idToken=e.idToken,t.secret=e.secret),t}createUserResult(e){return e?{displayName:e.displayName,email:e.email,emailVerified:e.emailVerified,isAnonymous:e.isAnonymous,metadata:this.createUserMetadataResult(e.metadata),phoneNumber:e.phoneNumber,photoUrl:e.photoURL,providerData:this.createUserProviderDataResult(e.providerData),providerId:e.providerId,tenantId:e.tenantId,uid:e.uid}:null}createUserMetadataResult(e){const t={};return e.creationTime&&(t.creationTime=Date.parse(e.creationTime)),e.lastSignInTime&&(t.lastSignInTime=Date.parse(e.lastSignInTime)),t}createUserProviderDataResult(e){return e.map(t=>({displayName:t.displayName,email:t.email,phoneNumber:t.phoneNumber,photoUrl:t.photoURL,providerId:t.providerId,uid:t.uid}))}createAdditionalUserInfoResult(e){if(!e)return null;const t=Q(e);if(!t)return null;const{isNewUser:r,profile:n,providerId:o,username:c}=t,u={isNewUser:r};return o!==null&&(u.providerId=o),n!==null&&(u.profile=n),c!=null&&(u.username=c),u}getErrorMessage(e){return e instanceof Object&&"message"in e&&typeof e.message=="string"?e.message:JSON.stringify(e)}throwNotAvailableError(){throw new Error("Not available on web.")}}i.AUTH_STATE_CHANGE_EVENT="authStateChange";i.ID_TOKEN_CHANGE_EVENT="idTokenChange";i.PHONE_CODE_SENT_EVENT="phoneCodeSent";i.PHONE_VERIFICATION_FAILED_EVENT="phoneVerificationFailed";i.ERROR_NO_USER_SIGNED_IN="No user is signed in.";i.ERROR_PHONE_NUMBER_MISSING="phoneNumber must be provided.";i.ERROR_RECAPTCHA_VERIFIER_MISSING="recaptchaVerifier must be provided and must be an instance of RecaptchaVerifier.";i.ERROR_CONFIRMATION_RESULT_MISSING="No confirmation result with this verification id was found.";export{i as FirebaseAuthenticationWeb};
