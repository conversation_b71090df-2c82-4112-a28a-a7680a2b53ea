{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAkB5C,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AACvH,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAM/C,MAAM,OAAO,SAAU,SAAQ,SAAS;IActC,KAAK,CAAC,UAAU,CAAC,OAAoC;QACnD,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5F,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAE7C,IAAI,OAAO,CAAC,aAAa,EAAE;YACzB,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;SAC5C;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAiC;;QACxD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAChE,OAAO;SACR;QAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAA,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,0CAAE,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE;QAC/D,MAAM,cAAc,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;QAEzD,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAEvD,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SACtD;QAED,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,6BAA6B,CAAC;QAElE,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACzE,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS,CAAC;QACzC,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC;SAC7C;QAED,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,mBAAmB;QAGvB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,KAAK,EAAE,CAAC;SACnB;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAChD,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC5D,OAAO;gBACL,aAAa,EAAE,sBAAsB,CAAC,QAAQ;aAC/C,CAAC;SACH;QAED,MAAM,EACJ,MAAM,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GACtC,GAAG,KAEH,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC;YAC9C,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAE3B,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC1D,OAAO;gBACL,aAAa,EAAE,sBAAsB,CAAC,MAAM;aAC7C,CAAC;SACH;QAED,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE7D,OAAO;YACL,aAAa,EAAE,sBAAsB,CAAC,SAAS;SAChD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAgC;;QACtD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC/D,OAAO;SACR;QAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAA,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,0CAAE,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE;QAC/D,MAAM,cAAc,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;QAEzD,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAEvD,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SACtD;QAED,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,6BAA6B,CAAC;QAElE,iDAAiD;QACjD,IAAI,OAAO,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAE;YACvD,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB,CAAC;SAC1E;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,OAAO,CAAC;YACvC,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB,CAAC;SACxE;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC;SAC7C;QAED,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,UAAU,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,YAAY,CAAC;SAC7C;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC;SACvC;QAED,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,kBAAkB;QAGtB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,KAAK,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC3D,MAAM,IAAI,KAAK,EAAE,CAAC;SACnB;QAED,MAAM,EACJ,MAAM,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GACtC,GAAG,KAEH,CAAC;QAEF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAC9D,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;YACnD,MAAM,IAAI,KAAK,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACzB,IAAI,CAAC,qBAAqB,GAAG,iBAAiB,CAAC;QAE/C,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,OAAO,EAAE;YAClD,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;SAC7B,CAAC,CAAC;QACH,OAAO;YACL,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;SAC7B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB;QAGtB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACzE,MAAM,IAAI,KAAK,EAAE,CAAC;SACnB;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;YACvD,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI,CAAC,qBAAqB;SACjC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC1D;QAED,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAE3B,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC5D,OAAO;YACL,aAAa,EAAE,qBAAqB,CAAC,SAAS;SAC/C,CAAC;IACJ,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,oBAA0C;QAC7D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC/D,IAAI,CAAC,sBAAsB,GAAG,oBAAoB,CAAC;QACnD,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,eAAe;QAGb,OAAO,IAAI,CAAC,2BAA2B,CACrC,UAAU,EACV,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,sBAAsB,EAC3B,kBAAkB,CAGlB,CAAC;IACL,CAAC;IAED,oBAAoB;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,qBAA4C;QAChE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC7D,OAAO;SACR;QACD,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAChE,IAAI,CAAC,uBAAuB,GAAG,qBAAqB,CAAC;QACrD,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,gBAAgB;QAGd,OAAO,IAAI,CAAC,2BAA2B,CACrC,WAAW,EACX,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,uBAAuB,EAC5B,mBAAmB,CAGnB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAA8B;;QACtD,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAC;QAC9E,aAAa,CAAC,EAAE,GAAG,eAAe,IAAI,EAAE,CAAC;QACzC,MAAA,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,0CAAE,WAAW,CAAC,aAAa,EAAE;QAC3D,MAAM,cAAc,CAAC,WAAW,CAAC,+BAA+B,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;SACpD;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAClD;QAED,aAAa,CAAC,eAAe,GAAG,6BAA6B,CAAC;QAC9D,OAAO,MAAM,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IACrF,CAAC;IAEO,KAAK,CAAC,0BAA0B;;QACtC,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAC;QAC9E,MAAA,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,0CAAE,WAAW,CAAC,aAAa,EAAE;QAC3D,MAAM,cAAc,CAAC,WAAW,CAAC,+BAA+B,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;SACpD;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAClD;QAED,aAAa,CAAC,eAAe,GAAG,6BAA6B,CAAC;QAE9D,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,2BAA2B,CACvC,IAA8B,EAC9B,aAA8C,EAC9C,oBAA8E,EAC9E,UAAkE;QAIlE,qDAAqD;QACrD,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACnC,IAAI,aAAa,KAAK,SAAS,IAAI,oBAAoB,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;gBAC1G,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO,OAAO,CAAC;oBACb,aAAa,EAAE,UAAU,CAAC,MAAM;iBACjC,CAAC,CAAC;aACJ;YAED,MAAM,aAAa,CAAC,uBAAuB,CAAC;gBAC1C,OAAO,EAAE,oBAAoB,CAAC,WAAY,CAAC,WAAW,EAAE;gBACxD,QAAQ,EAAE,oBAAoB,CAAC,QAAS,CAAC,WAAW,EAAE;gBACtD,KAAK,EAAE,oBAAoB,CAAC,mBAAoB,CAAC,oBAAoB,CAAC,mBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;gBACtG,cAAc,EAAE,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC;gBAChG,gBAAgB,EAAE,IAAI;gBACtB,iBAAiB,EAAE,IAAI;aACxB,CAAC,CAAC;YAEH,sGAAsG;YACtG,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,yBAAyB,CAAC;YAC1E,MAAM,aAAa,CAAC,4BAA4B,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACvE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAC5E,kBAAkB,EAClB;oBACE,cAAc,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE;iBACvC,EACD,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC;gBACF,IAAI,YAAY,EAAE;oBAChB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBACvB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;oBACtD,OAAO,OAAO,CAAC;wBACb,aAAa,EAAE,UAAU,CAAC,MAAM;qBACjC,CAAC,CAAC;iBACJ;gBACD,IAAI,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,MAAK,iBAAiB,EAAE;oBAC/C,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;oBACpF,IAAI,YAAY,EAAE;wBAChB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;wBACvB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;wBACtD,OAAO,OAAO,CAAC;4BACb,aAAa,EAAE,UAAU,CAAC,MAAM;yBACjC,CAAC,CAAC;qBACJ;iBACF;gBACD,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAC1B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBACjD,OAAO,OAAO,CAAC;oBACb,aAAa,EAAE,UAAU,CAAC,SAAS;iBACpC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE;gBAClD,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,UAAU,EAAE,KAAK;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}