import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { useApp } from '../contexts/AppContext'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { MapPin, ArrowLeft, Save } from 'lucide-react'
import { container } from '../styles/utils'
import { AddressService } from '../services/addressService'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const FormCard = styled(Card)`
  max-width: 600px;
  margin: 0 auto;
`

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
`

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing[6]};
  padding-top: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};

  @media (max-width: 640px) {
    flex-direction: column;
  }
`

export const CreateAddressPage: React.FC = () => {
  const navigate = useNavigate()
  const { state } = useApp()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    street: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    isDefault: false
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!state.user) {
      alert('Debes estar autenticado para crear una dirección')
      return
    }

    if (!formData.name.trim() || !formData.street.trim() || !formData.city.trim()) {
      alert('Por favor completa todos los campos requeridos')
      return
    }

    setLoading(true)
    try {
      const newAddress = {
        // id: generateId(),
        uid: state.user.uid,
        ...formData
      }

      await AddressService.createAddress(newAddress)
      navigate('/addresses')
    } catch (error) {
      console.error('Error creando dirección:', error)
      alert('Error al crear la dirección')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    navigate('/addresses')
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>
          <MapPin size={32} />
          Nueva Dirección
        </PageTitle>
      </PageHeader>

      <FormCard>
        <CardHeader>
          <CardTitle>Información de la Dirección</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <FormGrid>
              <FormGroup style={{ gridColumn: '1 / -1' }}>
                <Label htmlFor="name">Nombre de la dirección *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Ej: Casa, Trabajo, etc."
                  required
                />
              </FormGroup>

              <FormGroup style={{ gridColumn: '1 / -1' }}>
                <Label htmlFor="street">Dirección *</Label>
                <Input
                  id="street"
                  name="street"
                  value={formData.street}
                  onChange={handleInputChange}
                  placeholder="Calle, número, apartamento"
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="city">Ciudad *</Label>
                <Input
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  placeholder="Ciudad"
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="state">Estado/Provincia</Label>
                <Input
                  id="state"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  placeholder="Estado o provincia"
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="country">País</Label>
                <Input
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="País"
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="postalCode">Código Postal</Label>
                <Input
                  id="postalCode"
                  name="postalCode"
                  value={formData.postalCode}
                  onChange={handleInputChange}
                  placeholder="Código postal"
                />
              </FormGroup>

              <FormGroup style={{ gridColumn: '1 / -1' }}>
                <Label>
                  <input
                    type="checkbox"
                    name="isDefault"
                    checked={formData.isDefault}
                    onChange={handleInputChange}
                    style={{ marginRight: '0.5rem' }}
                  />
                  Establecer como dirección predeterminada
                </Label>
              </FormGroup>
            </FormGrid>

            <ButtonGroup>
              <Button type="button" variant="outline" onClick={handleCancel}>
                <ArrowLeft size={16} />
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <Save size={16} />
                    Guardar Dirección
                  </>
                )}
              </Button>
            </ButtonGroup>
          </form>
        </CardContent>
      </FormCard>
    </PageContainer>
  )
}
