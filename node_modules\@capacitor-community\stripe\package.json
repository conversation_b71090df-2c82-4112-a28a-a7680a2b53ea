{"name": "@capacitor-community/stripe", "version": "7.2.0", "engines": {"node": ">=20.0.0"}, "description": "Stripe SDK bindings for Capacitor Applications", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "exports": {".": {"type": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js"}, "./react": {"type": "./dist/esm/react/provider.d.ts", "import": "./dist/esm/react/provider.js"}, "./*": "./*"}, "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorCommunityStripe.podspec"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/capacitor-community/stripe.git"}, "bugs": {"url": "https://github.com/capacitor-community/stripe/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "xcodebuild -scheme CapacitorCommunityStripe -destination generic/platform=iOS", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix", "eslint": "eslint src/. --ext ts", "prettier": "prettier \"{src/*.ts,**/*.java}\" --plugin=prettier-plugin-java", "swiftlint": "node-swiftlint", "docgen": "docgen --api StripePlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build", "update:elements": "npm i stripe-pwa-elements@latest && cd demo/angular && npm i stripe-pwa-elements@latest"}, "devDependencies": {"@capacitor/android": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/ios": "^7.0.0", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "^4.0.0", "@ionic/swiftlint-config": "^2.0.0", "@rdlabo/capacitor-docgen": "^0.4.1", "@types/react": "^16.14.3", "@typescript-eslint/eslint-plugin": "^5.27.1", "eslint": "^8.57.0", "prettier": "^3.4.2", "prettier-plugin-java": "^2.6.6", "rimraf": "^6.0.1", "rollup": "^4.30.1", "swiftlint": "^2.0.0", "typescript": "~4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0", "react": "^17.0.0 || ^18.0.0", "stripe-pwa-elements": "^2.1.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended", "rules": {"@typescript-eslint/no-explicit-any": "error"}}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}