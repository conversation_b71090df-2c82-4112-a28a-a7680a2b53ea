import {
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged
} from 'firebase/auth'
import type { User as FirebaseUser } from 'firebase/auth'
import { doc, setDoc, getDoc, collection, query, where, getDocs } from 'firebase/firestore'
import { auth, db } from '../lib/firebase'
import type { User, Admin } from '../../types'

/**
 * Servicio de autenticación para usuarios y administradores
 */
export class AuthService {
  
  /**
   * Registra un nuevo usuario
   */
  static async registerUser(email: string, password: string, displayName?: string): Promise<User> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      const firebaseUser = userCredential.user
      
      const newUser: User = {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: displayName || firebaseUser.displayName || undefined,
        createdAt: new Date().toISOString(),
        addresses: [],
        cart: {
          items: [],
          totalAmount: 0,
          updatedAt: new Date().toISOString()
        }
      }
      
      // Guardar usuario en Firestore
      await setDoc(doc(db, 'users', firebaseUser.uid), newUser)
      
      return newUser
    } catch (error) {
      console.error('Error registrando usuario:', error)
      throw error
    }
  }
  
  /**
   * Inicia sesión de usuario
   */
  static async loginUser(email: string, password: string): Promise<User> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const firebaseUser = userCredential.user
      
      // Obtener datos del usuario desde Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))
      
      if (!userDoc.exists()) {
        throw new Error('Usuario no encontrado en la base de datos')
      }
      
      return userDoc.data() as User
    } catch (error) {
      console.error('Error iniciando sesión:', error)
      throw error
    }
  }
  
  /**
   * Inicia sesión de administrador
   */
  static async loginAdmin(email: string, password: string): Promise<Admin> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const firebaseUser = userCredential.user
      
      // Verificar si es administrador
      const adminDoc = await getDoc(doc(db, 'admins', firebaseUser.uid))
      
      if (!adminDoc.exists()) {
        await signOut(auth)
        throw new Error('No tienes permisos de administrador')
      }
      
      return adminDoc.data() as Admin
    } catch (error) {
      console.error('Error iniciando sesión de admin:', error)
      throw error
    }
  }
  
  /**
   * Cierra sesión
   */
  static async logout(): Promise<void> {
    try {
      await signOut(auth)
    } catch (error) {
      console.error('Error cerrando sesión:', error)
      throw error
    }
  }
  
  /**
   * Observa cambios en el estado de autenticación
   */
  static onAuthStateChange(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback)
  }
  
  /**
   * Obtiene el usuario actual
   */
  static getCurrentUser(): FirebaseUser | null {
    return auth.currentUser
  }
}
