import React from 'react'
import { Product, ShoppingCartItem } from '../../types'
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { formatPrice } from '../lib/utils'
import { ShoppingCart, Plus, Minus } from 'lucide-react'

interface ProductCardProps {
  product: Product
  cartItem?: ShoppingCartItem
  onAddToCart: (product: Product) => void
  onUpdateQuantity: (productId: string, quantity: number) => void
}

/**
 * Componente para mostrar una tarjeta de producto
 */
export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  cartItem,
  onAddToCart,
  onUpdateQuantity
}) => {
  const handleAddToCart = () => {
    onAddToCart(product)
  }

  const handleIncreaseQuantity = () => {
    const newQuantity = (cartItem?.quantity || 0) + 1
    onUpdateQuantity(product.id, newQuantity)
  }

  const handleDecreaseQuantity = () => {
    const newQuantity = Math.max(0, (cartItem?.quantity || 0) - 1)
    onUpdateQuantity(product.id, newQuantity)
  }

  return (
    <Card className="w-full max-w-sm mx-auto hover:shadow-lg transition-shadow">
      <CardHeader className="p-0">
        <div className="aspect-square overflow-hidden rounded-t-lg">
          <img
            src={product.imageUrl}
            alt={product.name}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-4">
        <CardTitle className="text-lg mb-2 line-clamp-2">
          {product.name}
        </CardTitle>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
          {product.description}
        </p>
        <div className="flex items-center justify-between">
          <span className="text-2xl font-bold text-primary">
            {formatPrice(product.price)}
          </span>
          {product.category && (
            <span className="text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded-full">
              {product.category}
            </span>
          )}
        </div>
        {product.stock <= 5 && product.stock > 0 && (
          <p className="text-sm text-orange-600 mt-2">
            ¡Solo quedan {product.stock} unidades!
          </p>
        )}
        {product.stock === 0 && (
          <p className="text-sm text-red-600 mt-2">
            Agotado
          </p>
        )}
      </CardContent>
      
      <CardFooter className="p-4 pt-0">
        {!cartItem ? (
          <Button 
            onClick={handleAddToCart}
            disabled={product.stock === 0}
            className="w-full"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            Agregar al carrito
          </Button>
        ) : (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="icon"
                onClick={handleDecreaseQuantity}
                disabled={cartItem.quantity <= 1}
              >
                <Minus className="w-4 h-4" />
              </Button>
              <span className="font-medium min-w-[2rem] text-center">
                {cartItem.quantity}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={handleIncreaseQuantity}
                disabled={cartItem.quantity >= product.stock}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <span className="font-semibold">
              {formatPrice(cartItem.quantity * cartItem.unitPrice)}
            </span>
          </div>
        )}
      </CardFooter>
    </Card>
  )
}
