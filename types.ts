// types.ts

export interface Product {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  price: number; // USD currency by default
  stock: number;
  category?: string;
  color?: string; // Color personalizado para el producto (hex, rgb, etc.)
  isActive: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface UserAddress {
  id: string;
  uid: string;
  name: string; // Ej. "Casa", "Trabajo"
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  isDefault: boolean;
}

export interface ShoppingCartItem {
  productId: string;
  name: string;
  quantity: number;
  unitPrice: number;
  imageUrl: string;
}

export interface ShoppingCart {
  items: ShoppingCartItem[];
  totalAmount: number;
  updatedAt: string;
}

export interface User {
  uid: string;
  email: string;
  photoURL?: string;
  emailVerified: boolean;
  providerId: string;
  displayName?: string;
  phone?: string;
  createdAt: string;
  addresses?: UserAddress[];
  cart?: ShoppingCart;
}

export interface Admin {
  uid: string;
  email: string;
  displayName?: string;
  permissions: string[]; // Ej. ['manageOrders', 'manageProducts']
  createdAt: string;
}

export interface OrderProduct {
  productId: string;
  name: string;
  quantity: number;
  unitPrice: number;
}

export interface Order {
  id: string;
  userId: string;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  products: OrderProduct[];
  totalAmount: number;
  paymentId: string; // stripe payment id
  metadata?: Record<string, string>;
  createdAt: string;
  addressId: string; // user address id
}