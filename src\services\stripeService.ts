import { Stripe } from '@capacitor-community/stripe'
import { Capacitor } from '@capacitor/core'
import type { ShoppingCart } from '../../types'

// Configuración de Stripe
const STRIPE_CONFIG = {
  publishableKey: 'pk_test_51RlK4AH9jwukfsVqKDsyD2Xb6uaNX7SQGjgQZxq6GMEwBzG3KfQS5hV0lzbLX8Ki67qKGFI1Jn0UYNFZHI741joZ00fxXTUCL2',
  stripeAccount: '', // Opcional
}

// Inicializar Stripe
let isStripeInitialized = false

const initializeStripe = async () => {
  if (!isStripeInitialized) {
    await Stripe.initialize(STRIPE_CONFIG)
    isStripeInitialized = true
  }
}

/**
 * Servicio para integración con Stripe usando Capacitor Community
 */
export class StripeService {

  /**
   * Inicializa Stripe (llamar al inicio de la aplicación)
   */
  static async initialize(): Promise<void> {
    try {
      await initializeStripe()
      console.log('Stripe inicializado correctamente')
    } catch (error) {
      console.error('Error inicializando Stripe:', error)
      throw error
    }
  }

  /**
   * Crea una sesión de checkout
   */
  static async createCheckoutSession(
    cart: ShoppingCart,
    userId: string,
    addressId: string
  ): Promise<{ sessionId: string }> {
    try {
      await initializeStripe()

      // En una aplicación real, esto sería una llamada a tu backend
      // Para demostración, simulamos la creación de la sesión
      const lineItems = cart.items.map(item => ({
        price_data: {
          currency: 'usd',
          product_data: {
            name: item.name,
            description: `${item.formatName} - ${item.formatDescription}`,
            images: item.imageUrl ? [item.imageUrl] : [],
          },
          unit_amount: Math.round(item.unitPrice * 100), // Stripe usa centavos
        },
        quantity: item.quantity,
      }))

      // Datos de la sesión
      const sessionData = {
        payment_method_types: ['card'],
        line_items: lineItems,
        mode: 'payment',
        success_url: `${window.location.origin}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${window.location.origin}/checkout`,
        metadata: {
          userId,
          addressId,
          orderId: `order_${Date.now()}`,
        }
      }

      // Para demostración, generamos un ID de sesión simulado
      const sessionId = `cs_test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      console.log('Sesión de checkout creada:', { sessionId, sessionData })

      return { sessionId }
    } catch (error) {
      console.error('Error en createCheckoutSession:', error)
      throw error
    }
  }
  
  /**
   * Procesa el pago usando Capacitor Community Stripe
   */
  static async redirectToCheckout(sessionId: string): Promise<void> {
    try {
      await initializeStripe()

      // Detectar si estamos en web o móvil
      const isWeb = Capacitor.getPlatform() === 'web'

      if (isWeb) {
        // En web, usar el flujo de checkout estándar
        await this.processWebCheckout(sessionId)
      } else {
        // En móvil, usar el flujo nativo de Capacitor
        await this.processMobileCheckout(sessionId)
      }
    } catch (error) {
      console.error('Error procesando checkout:', error)
      throw error
    }
  }

  /**
   * Procesa el checkout en web
   */
  private static async processWebCheckout(sessionId: string): Promise<void> {
    try {
      // Para web, simular el flujo de Stripe Checkout
      const confirmed = window.confirm(
        `🎉 Stripe Checkout (Web)\n\n` +
        `Session ID: ${sessionId}\n\n` +
        `En producción, esto abriría Stripe Checkout en una nueva ventana.\n\n` +
        `¿Simular pago exitoso?`
      )

      if (!confirmed) {
        throw new Error('Pago cancelado por el usuario')
      }

      // Simular delay de procesamiento
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Simular posible error (5% de probabilidad)
      if (Math.random() < 0.05) {
        throw new Error('Error simulado: Tarjeta rechazada')
      }

      // Generar payment ID simulado
      const paymentId = `pi_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      // Redirigir a success
      window.location.href = `/payment-success?session_id=${sessionId}&payment_id=${paymentId}`
    } catch (error) {
      console.error('Error en processWebCheckout:', error)
      throw error
    }
  }

  /**
   * Procesa el checkout en móvil usando Capacitor
   */
  private static async processMobileCheckout(sessionId: string): Promise<void> {
    try {
      // Crear Payment Sheet para móvil
      await Stripe.createPaymentSheet({
        paymentIntentClientSecret: sessionId, // En producción, esto sería el client_secret real
        merchantDisplayName: 'AriFuxion',
        customerId: undefined, // Opcional
        customerEphemeralKeySecret: undefined, // Opcional
        setupIntentClientSecret: undefined, // Para pagos futuros
        countryCode: 'US',
        style: 'alwaysDark', // 'alwaysLight' | 'alwaysDark' | 'automatic'
      })

      // Presentar Payment Sheet
      const result = await Stripe.presentPaymentSheet()

      // Para simplificar, vamos a simular un pago exitoso
      // En producción, aquí verificarías el resultado real
      console.log('Resultado del Payment Sheet:', result)

      // Simular delay de procesamiento
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Simular posible error (5% de probabilidad)
      if (Math.random() < 0.05) {
        throw new Error('Error simulado: Pago rechazado')
      }

      // Generar payment ID simulado
      const paymentId = `pi_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      // Redirigir a success
      window.location.href = `/payment-success?session_id=${sessionId}&payment_id=${paymentId}`
    } catch (error) {
      console.error('Error en processMobileCheckout:', error)
      throw error
    }
  }
  
  /**
   * Procesa el pago completo (crear sesión y redirigir)
   */
  static async processPayment(
    cart: ShoppingCart,
    userId: string,
    addressId: string
  ): Promise<void> {
    try {
      const { sessionId } = await this.createCheckoutSession(cart, userId, addressId)
      await this.redirectToCheckout(sessionId)
    } catch (error) {
      console.error('Error procesando pago:', error)
      throw error
    }
  }
  
  /**
   * Verifica el estado de un pago
   */
  static async verifyPayment(sessionId: string): Promise<{ status: string; paymentId?: string }> {
    try {
      // En un entorno real, esto sería una llamada a tu backend
      const response = await fetch(`/api/verify-payment/${sessionId}`)
      
      if (!response.ok) {
        throw new Error('Error verificando pago')
      }
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error verificando pago:', error)
      throw error
    }
  }
}

export default StripeService
