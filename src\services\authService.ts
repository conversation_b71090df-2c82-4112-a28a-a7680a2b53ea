import {
  signOut,
  onAuthStateChanged
} from 'firebase/auth'
import type { User as FirebaseUser } from 'firebase/auth'
import { doc, setDoc, getDoc } from 'firebase/firestore'
import { FirebaseAuthentication } from '@capacitor-firebase/authentication'
import { auth, db } from '../lib/firebase'
import type { User, Admin } from '../../types'

/**
 * Servicio de autenticación para usuarios y administradores usando Google OAuth
 */
export class AuthService {
  private static isSigningIn = false

  /**
   * Inicia sesión con Google usando Capacitor Firebase Authentication
   */
  static async signInWithGoogle(): Promise<User> {
    // Prevenir múltiples ejecuciones simultáneas
    if (this.isSigningIn) {
      throw new Error('Ya hay un proceso de autenticación en curso')
    }
    try {
      this.isSigningIn = true

      // Iniciar sesión con Google
      const result = await FirebaseAuthentication.signInWithGoogle()

      if (!result.user) {
        throw new Error('No se pudo obtener información del usuario')
      }

      const firebaseUser = result.user

      // Crear objeto User
      const newUser: User = {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: firebaseUser.displayName || '',
        photoURL: firebaseUser.photoUrl || undefined,
        emailVerified: firebaseUser.emailVerified || false,
        providerId: 'google.com',
        createdAt: new Date().toISOString(),
        phone: '',
      }

      // Guardar o actualizar datos en Firestore
      await setDoc(doc(db, 'users', firebaseUser.uid), newUser, { merge: true })

      return newUser
    } catch (error: any) {
      console.error('Error iniciando sesión con Google:', error)
      throw new Error(error.message || 'Error en la autenticación con Google')
    } finally {
      this.isSigningIn = false
    }
  }

  /**
   * Método legacy para compatibilidad - ahora usa Google OAuth
   */
  static async registerUser(_email: string, _password: string, _displayName?: string): Promise<User> {
    // Redirigir a Google OAuth
    return this.signInWithGoogle()
  }

  /**
   * Método legacy para compatibilidad - ahora usa Google OAuth
   */
  static async loginUser(_email: string, _password: string): Promise<User> {
    // Redirigir a Google OAuth
    return this.signInWithGoogle()
  }

  /**
   * Inicia sesión de administrador con Google OAuth
   */
  static async loginAdmin(_email: string, _password: string): Promise<Admin> {
    try {
      // Usar Google OAuth para autenticación
      const user = await this.signInWithGoogle()

      // Verificar si es administrador
      const adminDoc = await getDoc(doc(db, 'admins', user.uid))

      if (!adminDoc.exists()) {
        await signOut(auth)
        throw new Error('No tienes permisos de administrador')
      }

      return adminDoc.data() as Admin
    } catch (error: any) {
      console.error('Error iniciando sesión de admin:', error)
      throw new Error(error.message || 'Error en la autenticación de administrador')
    }
  }
  
  /**
   * Cierra sesión
   */
  static async logout(): Promise<void> {
    try {
      await signOut(auth)
    } catch (error) {
      console.error('Error cerrando sesión:', error)
      throw error
    }
  }
  
  /**
   * Observa cambios en el estado de autenticación
   */
  static onAuthStateChange(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback)
  }
  
  /**
   * Obtiene el usuario actual
   */
  static getCurrentUser(): FirebaseUser | null {
    return auth.currentUser
  }
}
