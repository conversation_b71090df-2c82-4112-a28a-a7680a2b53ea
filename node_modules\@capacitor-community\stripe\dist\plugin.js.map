{"version": 3, "file": "plugin.js", "sources": ["esm/applepay/apple-pay-events.enum.js", "esm/googlepay/google-pay-events.enum.js", "esm/paymentflow/payment-flow-events.enum.js", "esm/paymentsheet/payment-sheet-events.enum.js", "esm/index.js", "esm/shared/platform.js", "esm/web.js"], "sourcesContent": ["export var ApplePayEventsEnum;\n(function (ApplePayEventsEnum) {\n    ApplePayEventsEnum[\"Loaded\"] = \"applePayLoaded\";\n    ApplePayEventsEnum[\"FailedToLoad\"] = \"applePayFailedToLoad\";\n    ApplePayEventsEnum[\"Completed\"] = \"applePayCompleted\";\n    ApplePayEventsEnum[\"Canceled\"] = \"applePayCanceled\";\n    ApplePayEventsEnum[\"Failed\"] = \"applePayFailed\";\n    ApplePayEventsEnum[\"DidSelectShippingContact\"] = \"applePayDidSelectShippingContact\";\n    ApplePayEventsEnum[\"DidCreatePaymentMethod\"] = \"applePayDidCreatePaymentMethod\";\n})(ApplePayEventsEnum || (ApplePayEventsEnum = {}));\n//# sourceMappingURL=apple-pay-events.enum.js.map", "export var GooglePayEventsEnum;\n(function (GooglePayEventsEnum) {\n    GooglePayEventsEnum[\"Loaded\"] = \"googlePayLoaded\";\n    GooglePayEventsEnum[\"FailedToLoad\"] = \"googlePayFailedToLoad\";\n    GooglePayEventsEnum[\"Completed\"] = \"googlePayCompleted\";\n    GooglePayEventsEnum[\"Canceled\"] = \"googlePayCanceled\";\n    GooglePayEventsEnum[\"Failed\"] = \"googlePayFailed\";\n})(GooglePayEventsEnum || (GooglePayEventsEnum = {}));\n//# sourceMappingURL=google-pay-events.enum.js.map", "export var PaymentFlowEventsEnum;\n(function (PaymentFlowEventsEnum) {\n    PaymentFlowEventsEnum[\"Loaded\"] = \"paymentFlowLoaded\";\n    PaymentFlowEventsEnum[\"FailedToLoad\"] = \"paymentFlowFailedToLoad\";\n    PaymentFlowEventsEnum[\"Opened\"] = \"paymentFlowOpened\";\n    PaymentFlowEventsEnum[\"Created\"] = \"paymentFlowCreated\";\n    PaymentFlowEventsEnum[\"Completed\"] = \"paymentFlowCompleted\";\n    PaymentFlowEventsEnum[\"Canceled\"] = \"paymentFlowCanceled\";\n    PaymentFlowEventsEnum[\"Failed\"] = \"paymentFlowFailed\";\n})(PaymentFlowEventsEnum || (PaymentFlowEventsEnum = {}));\n//# sourceMappingURL=payment-flow-events.enum.js.map", "export var PaymentSheetEventsEnum;\n(function (PaymentSheetEventsEnum) {\n    PaymentSheetEventsEnum[\"Loaded\"] = \"paymentSheetLoaded\";\n    PaymentSheetEventsEnum[\"FailedToLoad\"] = \"paymentSheetFailedToLoad\";\n    PaymentSheetEventsEnum[\"Completed\"] = \"paymentSheetCompleted\";\n    PaymentSheetEventsEnum[\"Canceled\"] = \"paymentSheetCanceled\";\n    PaymentSheetEventsEnum[\"Failed\"] = \"paymentSheetFailed\";\n})(PaymentSheetEventsEnum || (PaymentSheetEventsEnum = {}));\n//# sourceMappingURL=payment-sheet-events.enum.js.map", "import { registerPlugin } from '@capacitor/core';\nconst Stripe = registerPlugin('Stripe', {\n    web: () => import('./web').then((m) => new m.StripeWeb()),\n});\nexport * from './definitions';\nexport { Stripe };\n//# sourceMappingURL=index.js.map", "/**\n * @url https://github.com/ionic-team/ionic-framework/blob/main/core/src/utils/platform.ts\n * So `@typescript-eslint/no-explicit-any` `@typescript-eslint/no-non-null-assertion` is disabled here\n */\nexport const getPlatforms = (win) => setupPlatforms(win);\nexport const isPlatform = (winOrPlatform, platform) => {\n    if (typeof winOrPlatform === 'string') {\n        platform = winOrPlatform;\n        winOrPlatform = undefined;\n    }\n    return getPlatforms(winOrPlatform).includes(platform);\n};\nexport const setupPlatforms = (win = window) => {\n    if (typeof win === 'undefined') {\n        return [];\n    }\n    win.Ionic = win.Ionic || {};\n    let platforms = win.Ionic.platforms;\n    if (platforms == null) {\n        platforms = win.Ionic.platforms = detectPlatforms(win);\n        platforms.forEach(p => win.document.documentElement.classList.add(`plt-${p}`));\n    }\n    return platforms;\n};\nconst detectPlatforms = (win) => Object.keys(PLATFORMS_MAP).filter(p => PLATFORMS_MAP[p](win));\nconst isMobileWeb = (win) => isMobile(win) && !isHybrid(win);\nconst isIpad = (win) => {\n    // iOS 12 and below\n    if (testUserAgent(win, /iPad/i)) {\n        return true;\n    }\n    // iOS 13+\n    if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n        return true;\n    }\n    return false;\n};\nconst isIphone = (win) => testUserAgent(win, /iPhone/i);\nconst isIOS = (win) => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = (win) => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = (win) => {\n    return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = (win) => {\n    const width = win.innerWidth;\n    const height = win.innerHeight;\n    const smallest = Math.min(width, height);\n    const largest = Math.max(width, height);\n    return (smallest > 390 && smallest < 520) &&\n        (largest > 620 && largest < 800);\n};\nconst isTablet = (win) => {\n    const width = win.innerWidth;\n    const height = win.innerHeight;\n    const smallest = Math.min(width, height);\n    const largest = Math.max(width, height);\n    return (isIpad(win) ||\n        isAndroidTablet(win) ||\n        ((smallest > 460 && smallest < 820) &&\n            (largest > 780 && largest < 1400)));\n};\nconst isMobile = (win) => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = (win) => !isMobile(win);\nconst isHybrid = (win) => isCordova(win) || isCapacitorNative(win);\nconst isCordova = (win) => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = (win) => {\n    const capacitor = win['Capacitor'];\n    return !!(capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative);\n};\nconst isElectron = (win) => testUserAgent(win, /electron/i);\nconst isPWA = (win) => !!(win.matchMedia('(display-mode: standalone)').matches || win.navigator.standalone);\nexport const testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => win.matchMedia(query).matches;\nconst PLATFORMS_MAP = {\n    'ipad': isIpad,\n    'iphone': isIphone,\n    'ios': isIOS,\n    'android': isAndroid,\n    'phablet': isPhablet,\n    'tablet': isTablet,\n    'cordova': isCordova,\n    'capacitor': isCapacitorNative,\n    'electron': isElectron,\n    'pwa': isPWA,\n    'mobile': isMobile,\n    'mobileweb': isMobileWeb,\n    'desktop': isDesktop,\n    'hybrid': isHybrid\n};\n//# sourceMappingURL=platform.js.map", "import { WebPlugin } from '@capacitor/core';\nimport { ApplePayEventsEnum, GooglePayEventsEnum, PaymentFlowEventsEnum, PaymentSheetEventsEnum } from './definitions';\nimport { isPlatform } from './shared/platform';\nexport class StripeWeb extends WebPlugin {\n    async initialize(options) {\n        if (typeof options.publishableKey !== 'string' || options.publishableKey.trim().length === 0) {\n            throw new Error('you must provide a valid key');\n        }\n        this.publishableKey = options.publishableKey;\n        if (options.stripeAccount) {\n            this.stripeAccount = options.stripeAccount;\n        }\n    }\n    async createPaymentSheet(options) {\n        var _a;\n        if (!this.publishableKey) {\n            this.notifyListeners(PaymentSheetEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.paymentSheet = document.createElement('stripe-payment-sheet');\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(this.paymentSheet);\n        await customElements.whenDefined('stripe-payment-sheet');\n        this.paymentSheet.publishableKey = this.publishableKey;\n        if (this.stripeAccount) {\n            this.paymentSheet.stripeAccount = this.stripeAccount;\n        }\n        this.paymentSheet.applicationName = '@capacitor-community/stripe';\n        this.paymentSheet.intentClientSecret = options.paymentIntentClientSecret;\n        this.paymentSheet.intentType = 'payment';\n        if (options.withZipCode !== undefined) {\n            this.paymentSheet.zip = options.withZipCode;\n        }\n        this.notifyListeners(PaymentSheetEventsEnum.Loaded, null);\n    }\n    async presentPaymentSheet() {\n        if (!this.paymentSheet) {\n            throw new Error();\n        }\n        const props = await this.paymentSheet.present();\n        if (props === undefined) {\n            this.notifyListeners(PaymentSheetEventsEnum.Canceled, null);\n            return {\n                paymentResult: PaymentSheetEventsEnum.Canceled,\n            };\n        }\n        const { detail: { stripe, cardNumberElement }, } = props;\n        const result = await stripe.createPaymentMethod({\n            type: 'card',\n            card: cardNumberElement,\n        });\n        this.paymentSheet.updateProgress('success');\n        this.paymentSheet.remove();\n        if (result.error !== undefined) {\n            this.notifyListeners(PaymentSheetEventsEnum.Failed, null);\n            return {\n                paymentResult: PaymentSheetEventsEnum.Failed,\n            };\n        }\n        this.notifyListeners(PaymentSheetEventsEnum.Completed, null);\n        return {\n            paymentResult: PaymentSheetEventsEnum.Completed,\n        };\n    }\n    async createPaymentFlow(options) {\n        var _a;\n        if (!this.publishableKey) {\n            this.notifyListeners(PaymentFlowEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.paymentSheet = document.createElement('stripe-payment-sheet');\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(this.paymentSheet);\n        await customElements.whenDefined('stripe-payment-sheet');\n        this.paymentSheet.publishableKey = this.publishableKey;\n        if (this.stripeAccount) {\n            this.paymentSheet.stripeAccount = this.stripeAccount;\n        }\n        this.paymentSheet.applicationName = '@capacitor-community/stripe';\n        // eslint-disable-next-line no-prototype-builtins\n        if (options.hasOwnProperty('paymentIntentClientSecret')) {\n            this.paymentSheet.intentType = 'payment';\n            this.paymentSheet.intentClientSecret = options.paymentIntentClientSecret;\n        }\n        else {\n            this.paymentSheet.intentType = 'setup';\n            this.paymentSheet.intentClientSecret = options.setupIntentClientSecret;\n        }\n        if (options.withZipCode !== undefined) {\n            this.paymentSheet.zip = options.withZipCode;\n        }\n        if (isPlatform(window, 'ios')) {\n            this.paymentSheet.buttonLabel = 'Add card';\n            this.paymentSheet.sheetTitle = 'Add a card';\n        }\n        else {\n            this.paymentSheet.buttonLabel = 'Add';\n        }\n        this.notifyListeners(PaymentFlowEventsEnum.Loaded, null);\n    }\n    async presentPaymentFlow() {\n        if (!this.paymentSheet) {\n            throw new Error();\n        }\n        this.notifyListeners(PaymentFlowEventsEnum.Opened, null);\n        const props = await this.paymentSheet.present().catch(() => undefined);\n        if (props === undefined) {\n            this.notifyListeners(PaymentFlowEventsEnum.Canceled, null);\n            throw new Error();\n        }\n        const { detail: { stripe, cardNumberElement }, } = props;\n        const { token } = await stripe.createToken(cardNumberElement);\n        if (token === undefined || token.card === undefined) {\n            throw new Error();\n        }\n        this.flowStripe = stripe;\n        this.flowCardNumberElement = cardNumberElement;\n        this.notifyListeners(PaymentFlowEventsEnum.Created, {\n            cardNumber: token.card.last4,\n        });\n        return {\n            cardNumber: token.card.last4,\n        };\n    }\n    async confirmPaymentFlow() {\n        if (!this.paymentSheet || !this.flowStripe || !this.flowCardNumberElement) {\n            throw new Error();\n        }\n        const result = await this.flowStripe.createPaymentMethod({\n            type: 'card',\n            card: this.flowCardNumberElement,\n        });\n        if (result.error !== undefined) {\n            this.notifyListeners(PaymentFlowEventsEnum.Failed, null);\n        }\n        this.paymentSheet.updateProgress('success');\n        this.paymentSheet.remove();\n        this.notifyListeners(PaymentFlowEventsEnum.Completed, null);\n        return {\n            paymentResult: PaymentFlowEventsEnum.Completed,\n        };\n    }\n    isApplePayAvailable() {\n        return this.isAvailable('applePay');\n    }\n    async createApplePay(createApplePayOption) {\n        if (!this.publishableKey) {\n            this.notifyListeners(ApplePayEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.requestApplePay = await this.createPaymentRequestButton();\n        this.requestApplePayOptions = createApplePayOption;\n        this.notifyListeners(ApplePayEventsEnum.Loaded, null);\n    }\n    presentApplePay() {\n        return this.presentPaymentRequestButton('applePay', this.requestApplePay, this.requestApplePayOptions, ApplePayEventsEnum);\n    }\n    isGooglePayAvailable() {\n        return this.isAvailable('googlePay');\n    }\n    async createGooglePay(createGooglePayOption) {\n        if (!this.publishableKey) {\n            this.notifyListeners(GooglePayEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.requestGooglePay = await this.createPaymentRequestButton();\n        this.requestGooglePayOptions = createGooglePayOption;\n        this.notifyListeners(GooglePayEventsEnum.Loaded, null);\n    }\n    presentGooglePay() {\n        return this.presentPaymentRequestButton('googlePay', this.requestGooglePay, this.requestGooglePayOptions, GooglePayEventsEnum);\n    }\n    async isAvailable(type) {\n        var _a;\n        const requestButton = document.createElement('stripe-payment-request-button');\n        requestButton.id = `isAvailable-${type}`;\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(requestButton);\n        await customElements.whenDefined('stripe-payment-request-button');\n        if (this.publishableKey) {\n            requestButton.publishableKey = this.publishableKey;\n        }\n        if (this.stripeAccount) {\n            requestButton.stripeAccount = this.stripeAccount;\n        }\n        requestButton.applicationName = '@capacitor-community/stripe';\n        return await requestButton.isAvailable(type).finally(() => requestButton.remove());\n    }\n    async createPaymentRequestButton() {\n        var _a;\n        const requestButton = document.createElement('stripe-payment-request-button');\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(requestButton);\n        await customElements.whenDefined('stripe-payment-request-button');\n        if (this.publishableKey) {\n            requestButton.publishableKey = this.publishableKey;\n        }\n        if (this.stripeAccount) {\n            requestButton.stripeAccount = this.stripeAccount;\n        }\n        requestButton.applicationName = '@capacitor-community/stripe';\n        return requestButton;\n    }\n    async presentPaymentRequestButton(type, requestButton, requestButtonOptions, EventsEnum) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise(async (resolve) => {\n            if (requestButton === undefined || requestButtonOptions === undefined || this.publishableKey === undefined) {\n                this.notifyListeners(EventsEnum.Failed, null);\n                return resolve({\n                    paymentResult: EventsEnum.Failed,\n                });\n            }\n            await requestButton.setPaymentRequestOption({\n                country: requestButtonOptions.countryCode.toUpperCase(),\n                currency: requestButtonOptions.currency.toLowerCase(),\n                total: requestButtonOptions.paymentSummaryItems[requestButtonOptions.paymentSummaryItems.length - 1],\n                disableWallets: type === 'applePay' ? ['googlePay', 'browserCard'] : ['applePay', 'browserCard'],\n                requestPayerName: true,\n                requestPayerEmail: true,\n            });\n            // await this.requestButton.setPaymentRequestShippingAddressEventHandler(async (event, stripe) => {});\n            const intentClientSecret = requestButtonOptions.paymentIntentClientSecret;\n            await requestButton.setPaymentMethodEventHandler(async (event, stripe) => {\n                const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(intentClientSecret, {\n                    payment_method: event.paymentMethod.id,\n                }, { handleActions: false });\n                if (confirmError) {\n                    event.complete('fail');\n                    this.notifyListeners(EventsEnum.Failed, confirmError);\n                    return resolve({\n                        paymentResult: EventsEnum.Failed,\n                    });\n                }\n                if ((paymentIntent === null || paymentIntent === void 0 ? void 0 : paymentIntent.status) === 'requires_action') {\n                    const { error: confirmError } = await stripe.confirmCardPayment(intentClientSecret);\n                    if (confirmError) {\n                        event.complete('fail');\n                        this.notifyListeners(EventsEnum.Failed, confirmError);\n                        return resolve({\n                            paymentResult: EventsEnum.Failed,\n                        });\n                    }\n                }\n                event.complete('success');\n                this.notifyListeners(EventsEnum.Completed, null);\n                return resolve({\n                    paymentResult: EventsEnum.Completed,\n                });\n            });\n            await requestButton.initStripe(this.publishableKey, {\n                stripeAccount: this.stripeAccount,\n                showButton: false,\n            });\n        });\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["ApplePayEventsEnum", "GooglePayEventsEnum", "PaymentFlowEventsEnum", "PaymentSheetEventsEnum", "registerPlugin", "WebPlugin"], "mappings": ";;;AAAWA;IACX,CAAC,UAAU,kBAAkB,EAAE;IAC/B,IAAI,kBAAkB,CAAC,QAAQ,CAAC,GAAG,gBAAgB;IACnD,IAAI,kBAAkB,CAAC,cAAc,CAAC,GAAG,sBAAsB;IAC/D,IAAI,kBAAkB,CAAC,WAAW,CAAC,GAAG,mBAAmB;IACzD,IAAI,kBAAkB,CAAC,UAAU,CAAC,GAAG,kBAAkB;IACvD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,GAAG,gBAAgB;IACnD,IAAI,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,kCAAkC;IACvF,IAAI,kBAAkB,CAAC,wBAAwB,CAAC,GAAG,gCAAgC;IACnF,CAAC,EAAEA,0BAAkB,KAAKA,0BAAkB,GAAG,EAAE,CAAC,CAAC;;ACTxCC;IACX,CAAC,UAAU,mBAAmB,EAAE;IAChC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,GAAG,iBAAiB;IACrD,IAAI,mBAAmB,CAAC,cAAc,CAAC,GAAG,uBAAuB;IACjE,IAAI,mBAAmB,CAAC,WAAW,CAAC,GAAG,oBAAoB;IAC3D,IAAI,mBAAmB,CAAC,UAAU,CAAC,GAAG,mBAAmB;IACzD,IAAI,mBAAmB,CAAC,QAAQ,CAAC,GAAG,iBAAiB;IACrD,CAAC,EAAEA,2BAAmB,KAAKA,2BAAmB,GAAG,EAAE,CAAC,CAAC;;ACP1CC;IACX,CAAC,UAAU,qBAAqB,EAAE;IAClC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;IACzD,IAAI,qBAAqB,CAAC,cAAc,CAAC,GAAG,yBAAyB;IACrE,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;IACzD,IAAI,qBAAqB,CAAC,SAAS,CAAC,GAAG,oBAAoB;IAC3D,IAAI,qBAAqB,CAAC,WAAW,CAAC,GAAG,sBAAsB;IAC/D,IAAI,qBAAqB,CAAC,UAAU,CAAC,GAAG,qBAAqB;IAC7D,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;IACzD,CAAC,EAAEA,6BAAqB,KAAKA,6BAAqB,GAAG,EAAE,CAAC,CAAC;;ACT9CC;IACX,CAAC,UAAU,sBAAsB,EAAE;IACnC,IAAI,sBAAsB,CAAC,QAAQ,CAAC,GAAG,oBAAoB;IAC3D,IAAI,sBAAsB,CAAC,cAAc,CAAC,GAAG,0BAA0B;IACvE,IAAI,sBAAsB,CAAC,WAAW,CAAC,GAAG,uBAAuB;IACjE,IAAI,sBAAsB,CAAC,UAAU,CAAC,GAAG,sBAAsB;IAC/D,IAAI,sBAAsB,CAAC,QAAQ,CAAC,GAAG,oBAAoB;IAC3D,CAAC,EAAEA,8BAAsB,KAAKA,8BAAsB,GAAG,EAAE,CAAC,CAAC;;ACNtD,UAAC,MAAM,GAAGC,mBAAc,CAAC,QAAQ,EAAE;IACxC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;IAC7D,CAAC;;ICHD;IACA;IACA;IACA;IACO,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK,cAAc,CAAC,GAAG,CAAC;IACjD,MAAM,UAAU,GAAG,CAAC,aAAa,EAAE,QAAQ,KAAK;IACvD,IAAI,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;IAC3C,QAAQ,QAAQ,GAAG,aAAa;IAChC,QAAQ,aAAa,GAAG,SAAS;IACjC;IACA,IAAI,OAAO,YAAY,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;IACzD,CAAC;IACM,MAAM,cAAc,GAAG,CAAC,GAAG,GAAG,MAAM,KAAK;IAChD,IAAI,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;IACpC,QAAQ,OAAO,EAAE;IACjB;IACA,IAAI,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE;IAC/B,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS;IACvC,IAAI,IAAI,SAAS,IAAI,IAAI,EAAE;IAC3B,QAAQ,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC;IAC9D,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF;IACA,IAAI,OAAO,SAAS;IACpB,CAAC;IACD,MAAM,eAAe,GAAG,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9F,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC5D,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK;IACxB;IACA,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE;IACrC,QAAQ,OAAO,IAAI;IACnB;IACA;IACA,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC3D,QAAQ,OAAO,IAAI;IACnB;IACA,IAAI,OAAO,KAAK;IAChB,CAAC;IACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC;IACvD,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC;IACxE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,eAAe,CAAC;IAC9D,MAAM,eAAe,GAAG,CAAC,GAAG,KAAK;IACjC,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC;IAC3D,CAAC;IACD,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK;IAC3B,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU;IAChC,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW;IAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;IAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;IAC3C,IAAI,OAAO,CAAC,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG;IAC5C,SAAS,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,GAAG,CAAC;IACxC,CAAC;IACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;IAC1B,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU;IAChC,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW;IAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;IAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;IAC3C,IAAI,QAAQ,MAAM,CAAC,GAAG,CAAC;IACvB,QAAQ,eAAe,CAAC,GAAG,CAAC;IAC5B,SAAS,CAAC,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG;IAC1C,aAAa,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE,sBAAsB,CAAC;IACjE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;IACzC,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC;IAClE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IACnF,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;IACnC,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC;IACtC,IAAI,OAAO,CAAC,EAAE,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,SAAM,GAAG,SAAM,GAAG,SAAS,CAAC,QAAQ,CAAC;IACvF,CAAC;IACD,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC;IAC3D,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC;IACpG,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC;IAC9E,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO;IAChE,MAAM,aAAa,GAAG;IACtB,IAAI,MAAM,EAAE,MAAM;IAClB,IAAI,QAAQ,EAAE,QAAQ;IACtB,IAAI,KAAK,EAAE,KAAK;IAChB,IAAI,SAAS,EAAE,SAAS;IACxB,IAAI,SAAS,EAAE,SAAS;IACxB,IAAI,QAAQ,EAAE,QAAQ;IACtB,IAAI,SAAS,EAAE,SAAS;IACxB,IAAI,WAAW,EAAE,iBAAiB;IAClC,IAAI,UAAU,EAAE,UAAU;IAC1B,IAAI,KAAK,EAAE,KAAK;IAChB,IAAI,QAAQ,EAAE,QAAQ;IACtB,IAAI,WAAW,EAAE,WAAW;IAC5B,IAAI,SAAS,EAAE,SAAS;IACxB,IAAI,QAAQ,EAAE;IACd,CAAC;;ICrFM,MAAM,SAAS,SAASC,cAAS,CAAC;IACzC,IAAI,MAAM,UAAU,CAAC,OAAO,EAAE;IAC9B,QAAQ,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;IACtG,YAAY,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;IAC3D;IACA,QAAQ,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc;IACpD,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;IACnC,YAAY,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa;IACtD;IACA;IACA,IAAI,MAAM,kBAAkB,CAAC,OAAO,EAAE;IACtC,QAAQ,IAAI,EAAE;IACd,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;IAClC,YAAY,IAAI,CAAC,eAAe,CAACF,8BAAsB,CAAC,YAAY,EAAE,IAAI,CAAC;IAC3E,YAAY;IACZ;IACA,QAAQ,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC;IAC1E,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;IACpH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,sBAAsB,CAAC;IAChE,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;IAC9D,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;IAChC,YAAY,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;IAChE;IACA,QAAQ,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,6BAA6B;IACzE,QAAQ,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB;IAChF,QAAQ,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS;IAChD,QAAQ,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;IAC/C,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW;IACvD;IACA,QAAQ,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,MAAM,EAAE,IAAI,CAAC;IACjE;IACA,IAAI,MAAM,mBAAmB,GAAG;IAChC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;IAChC,YAAY,MAAM,IAAI,KAAK,EAAE;IAC7B;IACA,QAAQ,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;IACvD,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;IACjC,YAAY,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC;IACvE,YAAY,OAAO;IACnB,gBAAgB,aAAa,EAAEA,8BAAsB,CAAC,QAAQ;IAC9D,aAAa;IACb;IACA,QAAQ,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,GAAG,KAAK;IAChE,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC;IACxD,YAAY,IAAI,EAAE,MAAM;IACxB,YAAY,IAAI,EAAE,iBAAiB;IACnC,SAAS,CAAC;IACV,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;IACnD,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;IAClC,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;IACxC,YAAY,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,MAAM,EAAE,IAAI,CAAC;IACrE,YAAY,OAAO;IACnB,gBAAgB,aAAa,EAAEA,8BAAsB,CAAC,MAAM;IAC5D,aAAa;IACb;IACA,QAAQ,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,SAAS,EAAE,IAAI,CAAC;IACpE,QAAQ,OAAO;IACf,YAAY,aAAa,EAAEA,8BAAsB,CAAC,SAAS;IAC3D,SAAS;IACT;IACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;IACrC,QAAQ,IAAI,EAAE;IACd,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;IAClC,YAAY,IAAI,CAAC,eAAe,CAACD,6BAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;IAC1E,YAAY;IACZ;IACA,QAAQ,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC;IAC1E,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;IACpH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,sBAAsB,CAAC;IAChE,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;IAC9D,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;IAChC,YAAY,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;IAChE;IACA,QAAQ,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,6BAA6B;IACzE;IACA,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAE;IACjE,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS;IACpD,YAAY,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB;IACpF;IACA,aAAa;IACb,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,OAAO;IAClD,YAAY,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB;IAClF;IACA,QAAQ,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;IAC/C,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW;IACvD;IACA,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;IACvC,YAAY,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,UAAU;IACtD,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,YAAY;IACvD;IACA,aAAa;IACb,YAAY,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,KAAK;IACjD;IACA,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;IAChE;IACA,IAAI,MAAM,kBAAkB,GAAG;IAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;IAChC,YAAY,MAAM,IAAI,KAAK,EAAE;IAC7B;IACA,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;IAChE,QAAQ,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC;IAC9E,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;IACjC,YAAY,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC;IACtE,YAAY,MAAM,IAAI,KAAK,EAAE;IAC7B;IACA,QAAQ,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,GAAG,KAAK;IAChE,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC;IACrE,QAAQ,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;IAC7D,YAAY,MAAM,IAAI,KAAK,EAAE;IAC7B;IACA,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM;IAChC,QAAQ,IAAI,CAAC,qBAAqB,GAAG,iBAAiB;IACtD,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,OAAO,EAAE;IAC5D,YAAY,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;IACxC,SAAS,CAAC;IACV,QAAQ,OAAO;IACf,YAAY,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;IACxC,SAAS;IACT;IACA,IAAI,MAAM,kBAAkB,GAAG;IAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;IACnF,YAAY,MAAM,IAAI,KAAK,EAAE;IAC7B;IACA,QAAQ,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IACjE,YAAY,IAAI,EAAE,MAAM;IACxB,YAAY,IAAI,EAAE,IAAI,CAAC,qBAAqB;IAC5C,SAAS,CAAC;IACV,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;IACxC,YAAY,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;IACpE;IACA,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;IACnD,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;IAClC,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,SAAS,EAAE,IAAI,CAAC;IACnE,QAAQ,OAAO;IACf,YAAY,aAAa,EAAEA,6BAAqB,CAAC,SAAS;IAC1D,SAAS;IACT;IACA,IAAI,mBAAmB,GAAG;IAC1B,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IAC3C;IACA,IAAI,MAAM,cAAc,CAAC,oBAAoB,EAAE;IAC/C,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;IAClC,YAAY,IAAI,CAAC,eAAe,CAACF,0BAAkB,CAAC,YAAY,EAAE,IAAI,CAAC;IACvE,YAAY;IACZ;IACA,QAAQ,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE;IACtE,QAAQ,IAAI,CAAC,sBAAsB,GAAG,oBAAoB;IAC1D,QAAQ,IAAI,CAAC,eAAe,CAACA,0BAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;IAC7D;IACA,IAAI,eAAe,GAAG;IACtB,QAAQ,OAAO,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,sBAAsB,EAAEA,0BAAkB,CAAC;IAClI;IACA,IAAI,oBAAoB,GAAG;IAC3B,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;IAC5C;IACA,IAAI,MAAM,eAAe,CAAC,qBAAqB,EAAE;IACjD,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;IAClC,YAAY,IAAI,CAAC,eAAe,CAACC,2BAAmB,CAAC,YAAY,EAAE,IAAI,CAAC;IACxE,YAAY;IACZ;IACA,QAAQ,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE;IACvE,QAAQ,IAAI,CAAC,uBAAuB,GAAG,qBAAqB;IAC5D,QAAQ,IAAI,CAAC,eAAe,CAACA,2BAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;IAC9D;IACA,IAAI,gBAAgB,GAAG;IACvB,QAAQ,OAAO,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,EAAEA,2BAAmB,CAAC;IACtI;IACA,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE;IAC5B,QAAQ,IAAI,EAAE;IACd,QAAQ,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC;IACrF,QAAQ,aAAa,CAAC,EAAE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAChD,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;IAChH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,+BAA+B,CAAC;IACzE,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;IACjC,YAAY,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;IAC9D;IACA,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;IAChC,YAAY,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;IAC5D;IACA,QAAQ,aAAa,CAAC,eAAe,GAAG,6BAA6B;IACrE,QAAQ,OAAO,MAAM,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,aAAa,CAAC,MAAM,EAAE,CAAC;IAC1F;IACA,IAAI,MAAM,0BAA0B,GAAG;IACvC,QAAQ,IAAI,EAAE;IACd,QAAQ,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC;IACrF,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;IAChH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,+BAA+B,CAAC;IACzE,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;IACjC,YAAY,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;IAC9D;IACA,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;IAChC,YAAY,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;IAC5D;IACA,QAAQ,aAAa,CAAC,eAAe,GAAG,6BAA6B;IACrE,QAAQ,OAAO,aAAa;IAC5B;IACA,IAAI,MAAM,2BAA2B,CAAC,IAAI,EAAE,aAAa,EAAE,oBAAoB,EAAE,UAAU,EAAE;IAC7F;IACA,QAAQ,OAAO,IAAI,OAAO,CAAC,OAAO,OAAO,KAAK;IAC9C,YAAY,IAAI,aAAa,KAAK,SAAS,IAAI,oBAAoB,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;IACxH,gBAAgB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;IAC7D,gBAAgB,OAAO,OAAO,CAAC;IAC/B,oBAAoB,aAAa,EAAE,UAAU,CAAC,MAAM;IACpD,iBAAiB,CAAC;IAClB;IACA,YAAY,MAAM,aAAa,CAAC,uBAAuB,CAAC;IACxD,gBAAgB,OAAO,EAAE,oBAAoB,CAAC,WAAW,CAAC,WAAW,EAAE;IACvE,gBAAgB,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,CAAC,WAAW,EAAE;IACrE,gBAAgB,KAAK,EAAE,oBAAoB,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;IACpH,gBAAgB,cAAc,EAAE,IAAI,KAAK,UAAU,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC;IAChH,gBAAgB,gBAAgB,EAAE,IAAI;IACtC,gBAAgB,iBAAiB,EAAE,IAAI;IACvC,aAAa,CAAC;IACd;IACA,YAAY,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,yBAAyB;IACrF,YAAY,MAAM,aAAa,CAAC,4BAA4B,CAAC,OAAO,KAAK,EAAE,MAAM,KAAK;IACtF,gBAAgB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;IACnH,oBAAoB,cAAc,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE;IAC1D,iBAAiB,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAC5C,gBAAgB,IAAI,YAAY,EAAE;IAClC,oBAAoB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC1C,oBAAoB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC;IACzE,oBAAoB,OAAO,OAAO,CAAC;IACnC,wBAAwB,aAAa,EAAE,UAAU,CAAC,MAAM;IACxD,qBAAqB,CAAC;IACtB;IACA,gBAAgB,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,SAAM,GAAG,SAAM,GAAG,aAAa,CAAC,MAAM,MAAM,iBAAiB,EAAE;IAChI,oBAAoB,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;IACvG,oBAAoB,IAAI,YAAY,EAAE;IACtC,wBAAwB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC9C,wBAAwB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC;IAC7E,wBAAwB,OAAO,OAAO,CAAC;IACvC,4BAA4B,aAAa,EAAE,UAAU,CAAC,MAAM;IAC5D,yBAAyB,CAAC;IAC1B;IACA;IACA,gBAAgB,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;IACzC,gBAAgB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;IAChE,gBAAgB,OAAO,OAAO,CAAC;IAC/B,oBAAoB,aAAa,EAAE,UAAU,CAAC,SAAS;IACvD,iBAAiB,CAAC;IAClB,aAAa,CAAC;IACd,YAAY,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE;IAChE,gBAAgB,aAAa,EAAE,IAAI,CAAC,aAAa;IACjD,gBAAgB,UAAU,EAAE,KAAK;IACjC,aAAa,CAAC;IACd,SAAS,CAAC;IACV;IACA;;;;;;;;;;;;;;;"}