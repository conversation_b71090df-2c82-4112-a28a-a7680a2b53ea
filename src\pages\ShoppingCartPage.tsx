import React from 'react'
import styled from 'styled-components'
import { Link, useNavigate } from 'react-router-dom'
import { useApp } from '../contexts/AppContext'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { formatPrice } from '../lib/utils'
import { Plus, Minus, Trash2, ShoppingBag, ArrowLeft } from 'lucide-react'
import { container, media, transition } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
`

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const CartGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[8]};

  ${media.lg`
    grid-template-columns: 2fr 1fr;
  `}
`

const CartItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
`

const CartItem = styled(Card)`
  ${transition()}

  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.md};
  }
`

const ItemContent = styled.div`
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: ${({ theme }) => theme.spacing[4]};
  align-items: center;
`

const ItemImage = styled.img`
  width: 5rem;
  height: 5rem;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
`

const ItemInfo = styled.div`
  flex: 1;
`

const ItemName = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`

const ItemPrice = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.primary[600]};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`

const ItemActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
  align-items: flex-end;
`

const QuantityControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[1]};
`

const QuantityButton = styled(Button)`
  width: 2rem;
  height: 2rem;
  padding: 0;
`

const QuantityDisplay = styled.span`
  min-width: 2rem;
  text-align: center;
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`

const RemoveButton = styled(Button)`
  color: ${({ theme }) => theme.colors.error};

  &:hover {
    background: rgba(239, 68, 68, 0.1);
  }
`

const CartSummary = styled(Card)`
  height: fit-content;
  position: sticky;
  top: ${({ theme }) => theme.spacing[8]};
`

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};

  &:last-child {
    border-bottom: none;
    font-weight: ${({ theme }) => theme.fontWeights.semibold};
    font-size: ${({ theme }) => theme.fontSizes.lg};
  }
`

const EmptyCart = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[16]} 0;
`

const EmptyCartIcon = styled.div`
  width: 6rem;
  height: 6rem;
  background: ${({ theme }) => theme.colors.gray[100]};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.gray[400]};
`

const EmptyCartTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const EmptyCartText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`

/**
 * Página del carrito de compras
 */
export const ShoppingCartPage: React.FC = () => {
  const { state, dispatch } = useApp()
  const navigate = useNavigate()

  const handleUpdateQuantity = (productId: string, formatName: string, quantity: number) => {
    dispatch({ type: 'UPDATE_CART_ITEM', payload: { productId, formatName, quantity } })
  }

  const handleRemoveItem = (productId: string, formatName: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: { productId, formatName } })
  }

  const handleClearCart = () => {
    dispatch({ type: 'CLEAR_CART' })
  }

  const handleCheckout = () => {
    if (!state.user) {
      navigate('/login?redirect=/checkout')
      return
    }
    navigate('/checkout')
  }

  if (state.cart.items.length === 0) {
    return (
      <PageContainer>
        <EmptyCart>
          <EmptyCartIcon>
            <ShoppingBag size={48} />
          </EmptyCartIcon>
          <EmptyCartTitle>Tu carrito está vacío</EmptyCartTitle>
          <EmptyCartText>
            Parece que aún no has agregado ningún producto a tu carrito.
          </EmptyCartText>
          <Button asChild>
            <Link to="/">
              <ArrowLeft size={16} style={{ marginRight: '0.5rem' }} />
              Continuar comprando
            </Link>
          </Button>
        </EmptyCart>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <BackButton variant="ghost" size="icon" asChild>
          <Link to="/">
            <ArrowLeft size={20} />
          </Link>
        </BackButton>
        <PageTitle>Carrito de Compras</PageTitle>
      </PageHeader>

      <CartGrid>
        {/* Lista de productos */}
        <CartItems>
          {state.cart.items.map(item => (
            <CartItem key={item.productId}>
              <CardContent style={{ padding: '1.5rem' }}>
                <ItemContent>
                  {/* Imagen del producto */}
                  <ItemImage
                    src={item.imageUrl}
                    alt={item.name}
                  />

                  {/* Información del producto */}
                  <ItemInfo>
                    <ItemName>{item.name}</ItemName>
                    <div style={{
                      fontSize: '0.875rem',
                      color: '#6b7280',
                      marginBottom: '0.5rem'
                    }}>
                      {item.formatName} - {item.formatDescription}
                    </div>
                    <ItemPrice>
                      {formatPrice(item.unitPrice)} c/u
                    </ItemPrice>
                  </ItemInfo>

                  {/* Controles y acciones */}
                  <ItemActions>
                    <QuantityControls>
                      <QuantityButton
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUpdateQuantity(item.productId, item.formatName, item.quantity - 1)}
                        disabled={item.quantity <= 1}
                      >
                        <Minus size={16} />
                      </QuantityButton>
                      <QuantityDisplay>
                        {item.quantity}
                      </QuantityDisplay>
                      <QuantityButton
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUpdateQuantity(item.productId, item.formatName, item.quantity + 1)}
                      >
                        <Plus size={16} />
                      </QuantityButton>
                    </QuantityControls>

                    <div style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      marginBottom: '0.5rem'
                    }}>
                      {formatPrice(item.quantity * item.unitPrice)}
                    </div>

                    <RemoveButton
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveItem(item.productId, item.formatName)}
                    >
                      <Trash2 size={16} />
                    </RemoveButton>
                  </ItemActions>
                </ItemContent>
              </CardContent>
            </CartItem>
          ))}

          {/* Botón para limpiar carrito */}
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button variant="outline" onClick={handleClearCart}>
              <Trash2 size={16} style={{ marginRight: '0.5rem' }} />
              Vaciar carrito
            </Button>
          </div>
        </CartItems>

        {/* Resumen del pedido */}
        <CartSummary>
          <CardHeader>
            <CardTitle>Resumen del Pedido</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Desglose de precios */}
            <div>
              <SummaryRow>
                <span>Subtotal ({state.cart.items.reduce((total, item) => total + item.quantity, 0)} productos)</span>
                <span>{formatPrice(state.cart.totalAmount)}</span>
              </SummaryRow>
              <SummaryRow>
                <span>Envío</span>
                <span style={{ color: '#22c55e' }}>Gratis</span>
              </SummaryRow>
              <SummaryRow>
                <span>Total</span>
                <span>{formatPrice(state.cart.totalAmount)}</span>
              </SummaryRow>
            </div>

            {/* Botón de checkout */}
            <Button
              style={{ width: '100%', marginTop: '1.5rem' }}
              size="lg"
              onClick={handleCheckout}
            >
              Proceder al Pago
            </Button>

            {/* Información adicional */}
            <div style={{
              fontSize: '0.875rem',
              color: '#6b7280',
              marginTop: '1.5rem',
              display: 'flex',
              flexDirection: 'column',
              gap: '0.5rem'
            }}>
              <p>✓ Envío gratis en pedidos superiores a $50</p>
              <p>✓ Garantía de satisfacción 100%</p>
              <p>✓ Pago seguro con encriptación SSL</p>
            </div>
          </CardContent>
        </CartSummary>
      </CartGrid>
    </PageContainer>
  )
}
