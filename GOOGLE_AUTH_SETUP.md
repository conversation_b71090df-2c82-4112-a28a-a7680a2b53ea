# Configuración de Autenticación con Google - Fuxion

## 🔐 Autenticación Implementada

La aplicación Fuxion ahora usa **exclusivamente Google OAuth** para la autenticación tanto de usuarios como administradores, utilizando el plugin `@capacitor-firebase/authentication`.

## 📱 Características

- ✅ **Login único con Google** para usuarios y administradores
- ✅ **Compatible con web y móvil** (Capacitor)
- ✅ **Interfaz moderna** con Styled Components
- ✅ **Gestión automática de usuarios** en Firestore
- ✅ **Verificación de permisos de admin** automática

## 🛠️ Configuración Requerida

### 1. Firebase Console

1. **Habilitar Google Authentication:**
   - Ve a Firebase Console → Authentication → Sign-in method
   - Habilita "Google" como proveedor
   - Configura el nombre del proyecto y email de soporte

2. **Configurar dominios autorizados:**
   - Agrega tu dominio de producción
   - Para desarrollo: `localhost` ya está incluido

### 2. Google Cloud Console

1. **Configurar OAuth 2.0:**
   - Ve a Google Cloud Console → APIs & Services → Credentials
   - Configura la pantalla de consentimiento OAuth
   - Crea credenciales OAuth 2.0 para aplicación web

2. **Para aplicaciones móviles:**
   - Crea credenciales adicionales para iOS/Android
   - Configura los SHA-1/SHA-256 fingerprints para Android

### 3. Capacitor Configuration

El archivo `capacitor.config.ts` ya está configurado:

```typescript
plugins: {
  FirebaseAuthentication: {
    skipNativeAuth: false,
    providers: ["google.com"],
  },
}
```

### 4. Configuración de Administradores

Para que un usuario sea administrador, debe existir un documento en la colección `admins` de Firestore:

```javascript
// Documento en la colección 'admins'
{
  uid: "uid-del-usuario-google",
  email: "<EMAIL>",
  displayName: "Nombre del Admin",
  permissions: ["manageProducts", "manageOrders"],
  createdAt: "2024-01-01T00:00:00.000Z"
}
```

## 🔄 Flujo de Autenticación

### Para Usuarios Regulares:

1. Usuario hace clic en "Continuar con Google"
2. Se abre popup/modal de Google OAuth
3. Usuario autoriza la aplicación
4. Se crea/actualiza documento en colección `users`
5. Usuario es redirigido a la página solicitada

### Para Administradores:

1. Admin hace clic en "Acceder con Google"
2. Se ejecuta el mismo flujo de Google OAuth
3. Se verifica si existe documento en colección `admins`
4. Si es admin: acceso al panel de administración
5. Si no es admin: error y logout automático

## 📱 Comandos para Móvil

```bash
# Sincronizar cambios
npx cap sync

# Ejecutar en iOS
npx cap run ios

# Ejecutar en Android
npx cap run android
```

## 🔧 Configuración Adicional para Móvil

### iOS

1. **Configurar URL Schemes:**
   - Agregar en `ios/App/App/Info.plist`
   - URL scheme debe coincidir con el bundle ID

2. **Configurar GoogleService-Info.plist:**
   - Descargar desde Firebase Console
   - Colocar en `ios/App/App/`

### Android

1. **Configurar google-services.json:**
   - Descargar desde Firebase Console
   - Colocar en `android/app/`

2. **Configurar SHA fingerprints:**
   ```bash
   # Obtener SHA-1 para debug
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```

## 🎨 Interfaz de Usuario

### Página de Login de Usuarios (`/login`)
- Botón prominente "Continuar con Google"
- Diseño limpio y moderno
- Enlace a login de administradores

### Página de Login de Administradores (`/admin`)
- Botón "Acceder con Google"
- Información sobre permisos requeridos
- Enlace de regreso a la tienda

## 🔍 Debugging

### Errores Comunes:

1. **"Error en la autenticación con Google"**
   - Verificar configuración en Firebase Console
   - Revisar dominios autorizados
   - Comprobar credenciales OAuth

2. **"No tienes permisos de administrador"**
   - Verificar que existe documento en colección `admins`
   - Comprobar que el UID coincide

3. **Plugin no inicializado (móvil)**
   - Ejecutar `npx cap sync`
   - Verificar configuración nativa

### Logs útiles:

```javascript
// En el navegador
console.log('Usuario autenticado:', user)

// En dispositivo móvil
// Usar Safari Web Inspector (iOS) o Chrome DevTools (Android)
```

## 🚀 Despliegue

### Web
- Configurar dominios de producción en Firebase Console
- Actualizar credenciales OAuth para dominio de producción

### Móvil
- Configurar SHA fingerprints de producción
- Actualizar configuración nativa con credenciales de producción
- Probar en dispositivos reales

## 📞 Soporte

Si tienes problemas con la autenticación:

1. Verifica la configuración de Firebase
2. Revisa los logs del navegador/dispositivo
3. Confirma que las credenciales OAuth están correctas
4. Asegúrate de que los dominios están autorizados

---

**¡La autenticación con Google está lista para usar! 🎉**
