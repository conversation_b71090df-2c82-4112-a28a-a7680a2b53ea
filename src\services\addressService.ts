import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import type { UserAddress } from '../../types'

/**
 * Servicio para gestión de direcciones de usuario
 */
export class AddressService {

  /**
   * Obtiene una dirección por ID
   */
  static async getAddress(id: string): Promise<UserAddress | null> {
    try {
      const addresDoc = await getDoc(doc(db, 'userAddresses', id))
      if (addresDoc.exists()) {
        return { id: addresDoc.id, ...addresDoc.data() } as UserAddress
      }
      return null
    } catch (error) {
      console.error('Error obteniendo dirección:', error)
      throw error
    }
  }

  /**
   * Obtiene todas las direcciones de un usuario
   */
  static async getUserAddresses(uid: string): Promise<UserAddress[]> {
    try {
      const q = query(
        collection(db, 'userAddresses'),
        where('uid', '==', uid)
      )

      const querySnapshot = await getDocs(q)
      const addresses: UserAddress[] = []

      querySnapshot.forEach((doc) => {
        addresses.push({ id: doc.id, ...doc.data() } as UserAddress)
      })

      return addresses
    } catch (error) {
      console.error('Error obteniendo direcciones:', error)
      throw error
    }
  }

  /**
   * Crea una nueva dirección
   */
  static async createAddress(addressData: Omit<UserAddress, 'id'>): Promise<string> {
    try {
      // Si es la dirección por defecto, desactivar otras direcciones por defecto
      if (addressData.isDefault) {
        await this.unsetDefaultAddresses(addressData.uid)
      }

      const docRef = await addDoc(collection(db, 'userAddresses'), addressData)
      return docRef.id
    } catch (error) {
      console.error('Error creando dirección:', error)
      throw error
    }
  }

  /**
   * Actualiza una dirección
   */
  static async updateAddress(id: string, addressData: Partial<UserAddress>): Promise<void> {
    try {
      // Si se está estableciendo como dirección por defecto, desactivar otras
      if (addressData.isDefault && addressData.uid) {
        await this.unsetDefaultAddresses(addressData.uid)
      }

      const docRef = doc(db, 'userAddresses', id)
      await updateDoc(docRef, addressData)
    } catch (error) {
      console.error('Error actualizando dirección:', error)
      throw error
    }
  }

  /**
   * Elimina una dirección
   */
  static async deleteAddress(id: string): Promise<void> {
    try {
      const docRef = doc(db, 'userAddresses', id)
      await deleteDoc(docRef)
    } catch (error) {
      console.error('Error eliminando dirección:', error)
      throw error
    }
  }

  /**
   * Establece una dirección como por defecto
   */
  static async setDefaultAddress(id: string, uid: string): Promise<void> {
    try {
      // Primero desactivar todas las direcciones por defecto del usuario
      await this.unsetDefaultAddresses(uid)

      // Luego activar la dirección seleccionada
      const docRef = doc(db, 'userAddresses', id)
      await updateDoc(docRef, { isDefault: true })
    } catch (error) {
      console.error('Error estableciendo dirección por defecto:', error)
      throw error
    }
  }

  /**
   * Desactiva todas las direcciones por defecto de un usuario
   */
  private static async unsetDefaultAddresses(uid: string): Promise<void> {
    try {
      const addresses = await this.getUserAddresses(uid)
      const defaultAddresses = addresses.filter(addr => addr.isDefault)

      for (const address of defaultAddresses) {
        const docRef = doc(db, 'userAddresses', address.id)
        await updateDoc(docRef, { isDefault: false })
      }
    } catch (error) {
      console.error('Error desactivando direcciones por defecto:', error)
      throw error
    }
  }

  /**
   * Obtiene la dirección por defecto de un usuario
   */
  static async getDefaultAddress(uid: string): Promise<UserAddress | null> {
    try {
      const addresses = await this.getUserAddresses(uid)
      return addresses.find(addr => addr.isDefault) || null
    } catch (error) {
      console.error('Error obteniendo dirección por defecto:', error)
      throw error
    }
  }
}
