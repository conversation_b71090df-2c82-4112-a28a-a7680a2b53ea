import React, { useState } from 'react'
import styled from 'styled-components'
import { useApp } from '../contexts/AppContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { User, Settings, Mail, Calendar } from 'lucide-react'
import { formatDate } from '../lib/utils'
import { container, media } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
`

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const PageDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
`

const ProfileGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[6]};
  max-width: 4xl;
  margin: 0 auto;

  ${media.lg`
    grid-template-columns: 1fr 2fr;
  `}
`











/**
 * Página de perfil de usuario
 */
export const UserProfilePage: React.FC = () => {
  const { state, dispatch } = useApp()
  const [editing, setEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    displayName: state.user?.displayName || '',
    phone: state.user?.phone || ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSave = async () => {
    if (!state.user) return

    setLoading(true)
    try {
      // Aquí actualizarías el perfil en Firebase
      // Por ahora solo actualizamos el estado local
      const updatedUser = {
        ...state.user,
        displayName: formData.displayName,
        phone: formData.phone
      }
      
      dispatch({ type: 'SET_USER', payload: updatedUser })
      setEditing(false)
    } catch (error) {
      console.error('Error actualizando perfil:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      displayName: state.user?.displayName || '',
      phone: state.user?.phone || ''
    })
    setEditing(false)
  }

  if (!state.user) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center' }}>
          <p>No hay usuario autenticado</p>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Mi Perfil</PageTitle>
        <PageDescription>
          Gestiona tu información personal y preferencias de cuenta
        </PageDescription>
      </PageHeader>

      <ProfileGrid>
          {/* Información personal */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Información Personal
              </CardTitle>
              <CardDescription>
                Gestiona tu información personal y preferencias de cuenta
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {editing ? (
                <>
                  <div>
                    <label htmlFor="displayName" className="block text-sm font-medium mb-2">
                      Nombre completo
                    </label>
                    <Input
                      id="displayName"
                      name="displayName"
                      value={formData.displayName}
                      onChange={handleInputChange}
                      placeholder="Tu nombre completo"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium mb-2">
                      Teléfono
                    </label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Tu número de teléfono"
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button onClick={handleSave} disabled={loading}>
                      {loading ? <LoadingSpinner size="sm" /> : 'Guardar'}
                    </Button>
                    <Button variant="outline" onClick={handleCancel}>
                      Cancelar
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">
                        Nombre
                      </label>
                      <p className="text-sm">
                        {state.user.displayName || 'No especificado'}
                      </p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">
                        Teléfono
                      </label>
                      <p className="text-sm">
                        {state.user.phone || 'No especificado'}
                      </p>
                    </div>
                  </div>

                  <Button onClick={() => setEditing(true)}>
                    <Settings className="w-4 h-4 mr-2" />
                    Editar Información
                  </Button>
                </>
              )}
            </CardContent>
          </Card>

          {/* Información de cuenta */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Información de Cuenta
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Email
                  </label>
                  <p className="text-sm">{state.user.email}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Miembro desde
                  </label>
                  <p className="text-sm flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {formatDate(state.user.createdAt)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Estadísticas */}
          <Card>
            <CardHeader>
              <CardTitle>Estadísticas de Compras</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-primary">0</p>
                  <p className="text-sm text-muted-foreground">Órdenes totales</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-primary">$0.00</p>
                  <p className="text-sm text-muted-foreground">Total gastado</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-primary">0</p>
                  <p className="text-sm text-muted-foreground">Productos favoritos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Acciones de cuenta */}
          <Card>
            <CardHeader>
              <CardTitle className="text-destructive">Zona de Peligro</CardTitle>
              <CardDescription>
                Acciones irreversibles para tu cuenta
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="destructive" disabled>
                Eliminar Cuenta
              </Button>
              <p className="text-xs text-muted-foreground mt-2">
                Esta función estará disponible próximamente
              </p>
            </CardContent>
          </Card>
      </ProfileGrid>
    </PageContainer>
  )
}
