import { loadStripe, Stripe } from '@stripe/stripe-js'
import { ShoppingCart } from '../../types'

// Clave pública de Stripe - Reemplazar con tu clave real
const stripePublicKey = 'pk_test_your_stripe_public_key_here'

let stripePromise: Promise<Stripe | null>

/**
 * Obtiene la instancia de Stripe
 */
const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(stripePublicKey)
  }
  return stripePromise
}

/**
 * Servicio para integración con Stripe
 */
export class StripeService {
  
  /**
   * Crea una sesión de checkout
   */
  static async createCheckoutSession(
    cart: ShoppingCart,
    userId: string,
    addressId: string
  ): Promise<{ sessionId: string }> {
    try {
      // En un entorno real, esto sería una llamada a tu backend
      // Aquí simulamos la creación de la sesión
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: cart.items,
          totalAmount: cart.totalAmount,
          userId,
          addressId,
          metadata: {
            orderId: `order_${Date.now()}`,
            total: cart.totalAmount,
            userId
          }
        }),
      })
      
      if (!response.ok) {
        throw new Error('Error creando sesión de checkout')
      }
      
      const session = await response.json()
      return { sessionId: session.id }
    } catch (error) {
      console.error('Error en createCheckoutSession:', error)
      throw error
    }
  }
  
  /**
   * Redirige al checkout de Stripe
   */
  static async redirectToCheckout(sessionId: string): Promise<void> {
    try {
      const stripe = await getStripe()
      
      if (!stripe) {
        throw new Error('Stripe no se pudo cargar')
      }
      
      const { error } = await stripe.redirectToCheckout({
        sessionId
      })
      
      if (error) {
        throw error
      }
    } catch (error) {
      console.error('Error redirigiendo a checkout:', error)
      throw error
    }
  }
  
  /**
   * Procesa el pago completo (crear sesión y redirigir)
   */
  static async processPayment(
    cart: ShoppingCart,
    userId: string,
    addressId: string
  ): Promise<void> {
    try {
      const { sessionId } = await this.createCheckoutSession(cart, userId, addressId)
      await this.redirectToCheckout(sessionId)
    } catch (error) {
      console.error('Error procesando pago:', error)
      throw error
    }
  }
  
  /**
   * Verifica el estado de un pago
   */
  static async verifyPayment(sessionId: string): Promise<{ status: string; paymentId?: string }> {
    try {
      // En un entorno real, esto sería una llamada a tu backend
      const response = await fetch(`/api/verify-payment/${sessionId}`)
      
      if (!response.ok) {
        throw new Error('Error verificando pago')
      }
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error verificando pago:', error)
      throw error
    }
  }
}

export default StripeService
