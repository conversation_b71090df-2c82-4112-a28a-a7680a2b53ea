import { loadStripe } from '@stripe/stripe-js'
import type { Stripe } from '@stripe/stripe-js'
import type { ShoppingCart } from '../../types'

// Clave pública de Stripe - Reemplazar con tu clave real
// Para desarrollo, esta es una clave de ejemplo
const stripePublicKey = 'pk_test_51RlK4AH9jwukfsVqKDsyD2Xb6uaNX7SQGjgQZxq6GMEwBzG3KfQS5hV0lzbLX8Ki67qKGFI1Jn0UYNFZHI741joZ00fxXTUCL2'

let stripePromise: Promise<Stripe | null>

/**
 * Obtiene la instancia de Stripe
 */
const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(stripePublicKey)
  }
  return stripePromise
}

/**
 * Servicio para integración con Stripe
 */
export class StripeService {
  
  /**
   * Crea una sesión de checkout
   */
  static async createCheckoutSession(
    cart: ShoppingCart,
    userId: string,
    addressId: string
  ): Promise<{ sessionId: string }> {
    try {
      const stripe = await getStripe()

      if (!stripe) {
        throw new Error('Stripe no se pudo cargar')
      }

      // Para demostración, creamos una sesión de checkout directamente
      // En producción, esto se haría en el backend
      const lineItems = cart.items.map(item => ({
        price_data: {
          currency: 'usd',
          product_data: {
            name: item.name,
            images: [item.imageUrl],
          },
          unit_amount: Math.round(item.unitPrice * 100), // Stripe usa centavos
        },
        quantity: item.quantity,
      }))

      // Simulamos la creación de sesión (en producción esto sería una llamada al backend)
      const sessionData = {
        payment_method_types: ['card'],
        line_items: lineItems,
        mode: 'payment',
        success_url: `${window.location.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${window.location.origin}/checkout`,
        metadata: {
          userId,
          addressId,
          orderId: `order_${Date.now()}`,
        }
      }

      // Para demostración, generamos un ID de sesión simulado
      const sessionId = `cs_test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      console.log('Sesión de checkout creada:', { sessionId, sessionData })

      return { sessionId }
    } catch (error) {
      console.error('Error en createCheckoutSession:', error)
      throw error
    }
  }
  
  /**
   * Redirige al checkout de Stripe
   */
  static async redirectToCheckout(sessionId: string): Promise<void> {
    try {
      const stripe = await getStripe()

      if (!stripe) {
        throw new Error('Stripe no se pudo cargar')
      }

      // Simular un proceso de pago más realista
      const shouldSimulateError = Math.random() < 0.1 // 10% de probabilidad de error

      if (shouldSimulateError) {
        throw new Error('Error simulado en el procesamiento del pago')
      }

      // Simular delay de procesamiento
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Para demostración, redirigir directamente a success
      // En producción, usarías stripe.redirectToCheckout({ sessionId })
      window.location.href = `/payment-success?session_id=${sessionId}`

      // Código para producción (comentado para demostración):
      /*
      const { error } = await stripe.redirectToCheckout({
        sessionId
      })

      if (error) {
        throw error
      }
      */
    } catch (error) {
      console.error('Error redirigiendo a checkout:', error)
      throw error
    }
  }
  
  /**
   * Procesa el pago completo (crear sesión y redirigir)
   */
  static async processPayment(
    cart: ShoppingCart,
    userId: string,
    addressId: string
  ): Promise<void> {
    try {
      const { sessionId } = await this.createCheckoutSession(cart, userId, addressId)
      await this.redirectToCheckout(sessionId)
    } catch (error) {
      console.error('Error procesando pago:', error)
      throw error
    }
  }
  
  /**
   * Verifica el estado de un pago
   */
  static async verifyPayment(sessionId: string): Promise<{ status: string; paymentId?: string }> {
    try {
      // En un entorno real, esto sería una llamada a tu backend
      const response = await fetch(`/api/verify-payment/${sessionId}`)
      
      if (!response.ok) {
        throw new Error('Error verificando pago')
      }
      
      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error verificando pago:', error)
      throw error
    }
  }
}

export default StripeService
