import {
  Persistence,
  ProviderId,
  registerPlugin
} from "./chunk-P2KR6NVI.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@capacitor-firebase/authentication/dist/esm/index.js
var FirebaseAuthentication = registerPlugin("FirebaseAuthentication", {
  web: () => import("./web-V2SNBDAP.js").then((m) => new m.FirebaseAuthenticationWeb())
});
export {
  FirebaseAuthentication,
  Persistence,
  ProviderId
};
//# sourceMappingURL=@capacitor-firebase_authentication.js.map
