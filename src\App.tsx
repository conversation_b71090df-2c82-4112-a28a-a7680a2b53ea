import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { AppProvider, useApp } from './contexts/AppContext'
import { AuthService } from './services/authService'
import { LocalStorageService } from './services/localStorageService'
import { db } from './lib/firebase'
import { doc, getDoc } from 'firebase/firestore'
import { Header } from './components/Header'
import { ProtectedRoute } from './components/ProtectedRoute'
import { LoadingSpinner } from './components/LoadingSpinner'
// Páginas principales
import { HomePage } from './pages/HomePage'
import { ShoppingCartPage } from './pages/ShoppingCartPage'
import { LoginPage } from './pages/LoginPage'
import { CheckoutPage } from './pages/CheckoutPage'
import { PaymentSuccessPage } from './pages/PaymentSuccessPage'

// Páginas de usuario
import { UserProfilePage } from './pages/UserProfilePage'
import { AddressManagementPage } from './pages/AddressManagementPage'

// Páginas de administración
import { AdminLoginPage } from './pages/AdminLoginPage'
import { AdminDashboardPage } from './pages/AdminDashboardPage'
import { ProductManagementPage } from './pages/ProductManagementPage'
import { CreateProductPage } from './pages/CreateProductPage'
import { EditProductPage } from './pages/EditProductPage'
import { CreateAddressPage } from './pages/CreateAddressPage'
import { EditAddressPage } from './pages/EditAddressPage'
import { OrdersPage } from './pages/OrdersPage'
import { OrderManagementPage } from './pages/OrderManagementPage'

import type { User, Admin } from '../types'

const AppContainer = styled.div`
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.background};
`

const LoadingContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
`

/**
 * Componente principal de la aplicación con routing
 */
const AppContent: React.FC = () => {
  const { state, dispatch } = useApp()
  const [setAdmin] = useState<Admin | null>(null)
  const [authLoading, setAuthLoading] = useState(true)

  // Observar cambios en la autenticación
  useEffect(() => {
    const unsubscribe = AuthService.onAuthStateChange(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Primero intentar cargar desde localStorage
          const storedUser = LocalStorageService.getSessionUser()

          if (storedUser && storedUser.uid === firebaseUser.uid) {
            // Usuario ya está en localStorage con información completa (incluyendo isAdmin)
            dispatch({ type: 'SET_USER', payload: storedUser })
          } else {
            // Crear objeto User y verificar si es admin
            const user: User = {
              uid: firebaseUser.uid,
              email: firebaseUser.email!,
              displayName: firebaseUser.displayName || '',
              photoURL: firebaseUser.photoURL || undefined,
              emailVerified: firebaseUser.emailVerified || false,
              providerId: 'google.com',
              createdAt: new Date().toISOString(),
              phone: '',
            }

            // Verificar si es administrador
            try {
              const adminDoc = await getDoc(doc(db, 'admins', user.uid))
              if (adminDoc.exists()) {
                const userWithAdmin = { ...user, isAdmin: true }
                LocalStorageService.saveSessionUser(userWithAdmin)
                dispatch({ type: 'SET_USER', payload: userWithAdmin })
              } else {
                LocalStorageService.saveSessionUser(user)
                dispatch({ type: 'SET_USER', payload: user })
              }
            } catch (adminError) {
              console.log('Error verificando admin:', adminError)
              LocalStorageService.saveSessionUser(user)
              dispatch({ type: 'SET_USER', payload: user })
            }
          }
        } catch (error) {
          console.error('Error verificando usuario:', error)
        }
      } else {
        dispatch({ type: 'SET_USER', payload: null })
        setAdmin(null)
      }
      setAuthLoading(false)
    })

    return () => unsubscribe()
  }, [dispatch])

  const handleLogout = () => {
    dispatch({ type: 'SET_USER', payload: null })
    setAdmin(null)
  }

  if (authLoading) {
    return (
      <LoadingContainer>
        <LoadingSpinner size="lg" />
      </LoadingContainer>
    )
  }

  return (
    <AppContainer>
      <Header
        user={state.user}
        cart={state.cart}
        onLogout={handleLogout}
      />

      <main>
        <Routes>
          {/* Rutas públicas */}
          <Route path="/" element={<HomePage />} />
          <Route path="/cart" element={<ShoppingCartPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/success" element={<PaymentSuccessPage />} />

          {/* Rutas protegidas para usuarios */}
          <Route
            path="/checkout"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <CheckoutPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/profile"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <UserProfilePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/profile/address/create"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <CreateAddressPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/profile/address/edit/:id"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <EditAddressPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/orders"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <OrdersPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/addresses"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <AddressManagementPage />
              </ProtectedRoute>
            }
          />

          {/* Rutas de administración */}
          <Route path="/admin" element={<AdminLoginPage onAdminLogin={setAdmin} />} />
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <AdminDashboardPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/products"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <ProductManagementPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/products/create"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <CreateProductPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/products/edit/:id"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <EditProductPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/orders"
            element={
              <ProtectedRoute user={state.user} requireAuth>
                <OrderManagementPage />
              </ProtectedRoute>
            }
          />
        </Routes>
      </main>
    </AppContainer>
  )
}

/**
 * Componente raíz de la aplicación
 */
function App() {
  return (
    <AppProvider>
      <Router>
        <AppContent />
      </Router>
    </AppProvider>
  )
}

export default App
