import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useApp } from '../contexts/AppContext'
import { AddressService } from '../services/addressService'

import { PayPalCheckout } from '../components/PayPalCheckout'
import toast, { Toaster } from 'react-hot-toast'
import { OrderService } from '../services/orderService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { formatPrice } from '../lib/utils'
import type { UserAddress, Order } from '../../types'
import { CreditCard, MapPin, Package } from 'lucide-react'
import { container, media } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
`

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const CheckoutGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[8]};
  max-width: 6xl;
  margin: 0 auto;

  ${media.lg`
    grid-template-columns: 2fr 1fr;
  `}
`









const OrderSummary = styled(Card)`
  height: fit-content;
  position: sticky;
  top: ${({ theme }) => theme.spacing[8]};
`

const SummaryItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};

  &:last-child {
    border-bottom: none;
  }
`

const ItemImage = styled.img`
  width: 3rem;
  height: 3rem;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ItemInfo = styled.div`
  flex: 1;
  min-width: 0;
`

const ItemName = styled.h5`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`

const ItemDetails = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.text.muted};
`

const PriceRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};

  &:last-child {
    border-bottom: none;
    font-weight: ${({ theme }) => theme.fontWeights.semibold};
    font-size: ${({ theme }) => theme.fontSizes.lg};
  }
`


/**
 * Página de checkout para procesar el pago
 */
export const CheckoutPage: React.FC = () => {
  const { state, dispatch } = useApp()
  const navigate = useNavigate()
  
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [selectedAddress, setSelectedAddress] = useState<UserAddress | null>(null)
  const [loading, setLoading] = useState(true)

  const [error, setError] = useState('')

  // Redirigir si no hay usuario o carrito vacío
  useEffect(() => {
    if (!state.user) {
      navigate('/login?redirect=/checkout')
      return
    }
    
    if (state.cart.items.length === 0) {
      navigate('/cart')
      return
    }
    
    loadAddresses()
  }, [state.user, state.cart.items.length, navigate])

  const loadAddresses = async () => {
    if (!state.user) return
    
    try {
      setLoading(true)
      const userAddresses = await AddressService.getUserAddresses(state.user.uid)
      setAddresses(userAddresses)
      
      // Seleccionar dirección por defecto
      const defaultAddress = userAddresses.find(addr => addr.isDefault)
      if (defaultAddress) {
        setSelectedAddress(defaultAddress)
      } else if (userAddresses.length > 0) {
        setSelectedAddress(userAddresses[0])
      }
    } catch (error) {
      console.error('Error cargando direcciones:', error)
      setError('Error cargando direcciones')
    } finally {
      setLoading(false)
    }
  }

  // Manejar éxito del pago PayPal
  const handlePayPalSuccess = async (transactionId: string) => {
    try {
      // Crear la orden DESPUÉS del pago exitoso
      const orderData: Omit<Order, 'id'> = {
        userId: state.user!.uid,
        userName: state.user!.displayName || state.user!.email,
        userAddress: selectedAddress!,
        email: state.user!.email,
        status: 'paid', // Directamente como 'paid' porque ya se procesó el pago
        products: state.cart.items.map(item => ({
          productId: item.productId,
          name: item.name,
          formatName: item.formatName,
          quantity: item.quantity,
          unitPrice: item.unitPrice
        })),
        totalAmount: state.cart.totalAmount,
        paymentId: transactionId, // ID de transacción REAL de PayPal
        paymentMethod: 'paypal', // Método de pago
        addressId: selectedAddress!.id,
        createdAt: new Date().toISOString(),
        metadata: {
          cartUpdatedAt: state.cart.updatedAt,
          paypalTransactionId: transactionId
        }
      }

      // Crear orden en Firestore
      await OrderService.createOrder(orderData)
      console.log('Orden creada exitosamente con transactionId:', transactionId)

      // Mostrar toast de éxito
      toast.success('¡Pago exitoso! Orden creada. Redirigiendo...')

      // Limpiar carrito
      dispatch({ type: 'CLEAR_CART' })

      // Redirigir a página de éxito
      setTimeout(() => {
        navigate(`/payment-success?payment_id=${transactionId}&amount=${state.cart.totalAmount}&method=paypal`)
      }, 2000)

    } catch (error) {
      console.error('Error creando orden después del pago:', error)
      toast.error('Pago exitoso pero error creando la orden. Contacta soporte.')
    }
  }

  // Manejar error del pago PayPal
  const handlePayPalError = (error: string) => {
    console.error('Error en pago PayPal:', error)
    toast.error(`Fallo en el proceso de pago: ${error}`)
    setError(`Error en el pago: ${error}`)
  }

  // Manejar cancelación del pago PayPal
  const handlePayPalCancel = () => {
    console.log('Pago PayPal cancelado')
    toast('Pago cancelado por el usuario', { icon: '⚠️' })
    setError('')
  }



  if (loading) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '16rem' }}>
          <LoadingSpinner size="lg" />
        </div>
      </PageContainer>
    )
  }

  if (addresses.length === 0) {
    return (
      <PageContainer>
        <PageHeader>
          <PageTitle>Checkout</PageTitle>
          <p style={{
            color: '#6b7280',
            marginBottom: '2rem',
            fontSize: '1.125rem'
          }}>
            Necesitas agregar una dirección de envío antes de continuar.
          </p>
          <Button onClick={() => navigate('/addresses')}>
            Agregar Dirección
          </Button>
        </PageHeader>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Finalizar Compra</PageTitle>
      </PageHeader>

      <CheckoutGrid>
        {/* Información de envío */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Dirección de Envío
              </CardTitle>
            </CardHeader>
            <CardContent>
              {addresses.map(address => (
                <div
                  key={address.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedAddress?.id === address.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedAddress(address)}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium">{address.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {address.street}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {address.city}, {address.state} {address.postalCode}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {address.country}
                      </p>
                    </div>
                    {address.isDefault && (
                      <span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded">
                        Por defecto
                      </span>
                    )}
                  </div>
                </div>
              ))}
              
              <Button 
                variant="outline" 
                className="w-full mt-4"
                onClick={() => navigate('/addresses')}
              >
                Gestionar Direcciones
              </Button>
            </CardContent>
          </Card>

          {/* Información de productos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Productos ({state.cart.items.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {state.cart.items.map(item => (
                  <div key={item.productId} className="flex items-center gap-3">
                    <img
                      src={item.imageUrl}
                      alt={item.name}
                      className="w-12 h-12 rounded object-cover"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        Cantidad: {item.quantity}
                      </p>
                    </div>
                    <span className="font-medium text-sm">
                      {formatPrice(item.quantity * item.unitPrice)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Resumen y pago */}
        <div>
          <OrderSummary>
            <CardHeader>
              <CardTitle style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <CreditCard size={20} />
                Resumen del Pedido
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Productos en el carrito */}
              <div style={{ marginBottom: '1.5rem' }}>
                {state.cart.items.map(item => (
                  <SummaryItem key={item.productId}>
                    <ItemImage
                      src={item.imageUrl}
                      alt={item.name}
                    />
                    <ItemInfo>
                      <ItemName>{item.name}</ItemName>
                      <div style={{
                        fontSize: '0.75rem',
                        color: '#6b7280',
                        marginBottom: '0.25rem'
                      }}>
                        {item.formatName}
                      </div>
                      <ItemDetails>
                        Cantidad: {item.quantity} × {formatPrice(item.unitPrice)}
                      </ItemDetails>
                    </ItemInfo>
                    <div style={{
                      fontWeight: '600',
                      color: '#0072CE'
                    }}>
                      {formatPrice(item.quantity * item.unitPrice)}
                    </div>
                  </SummaryItem>
                ))}
              </div>

              {/* Desglose de precios */}
              <div style={{ marginBottom: '1.5rem' }}>
                <PriceRow>
                  <span>Subtotal</span>
                  <span>{formatPrice(state.cart.totalAmount)}</span>
                </PriceRow>
                <PriceRow>
                  <span>Envío</span>
                  <span style={{ color: '#22c55e', fontWeight: '600' }}>Gratis</span>
                </PriceRow>
                <PriceRow>
                  <span>Impuestos</span>
                  <span style={{ color: '#6b7280' }}>Incluidos</span>
                </PriceRow>
                <PriceRow style={{
                  borderTop: '2px solid #e5e7eb',
                  paddingTop: '1rem',
                  marginTop: '1rem'
                }}>
                  <span>Total</span>
                  <span>{formatPrice(state.cart.totalAmount)}</span>
                </PriceRow>
              </div>

              {/* Error */}
              {error && (
                <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                  {error}
                </div>
              )}

              {/* Componente de pago PayPal */}
              <PayPalCheckout
                amount={state.cart.totalAmount}
                description={`Compra en AriFuxion - ${state.cart.items.length} artículos`}
                onSuccess={handlePayPalSuccess}
                onError={handlePayPalError}
                onCancel={handlePayPalCancel}
                successUrl="/payment-success"
                disabled={!selectedAddress}
              />

              {/* Información de seguridad */}
              <div className="text-xs text-muted-foreground space-y-1">
                <p>🔒 Pago seguro con encriptación SSL</p>
                <p>💳 Aceptamos todas las tarjetas principales</p>
                <p>✅ Garantía de satisfacción 100%</p>
              </div>
            </CardContent>
          </OrderSummary>
        </div>
      </CheckoutGrid>

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </PageContainer>
  )
}
