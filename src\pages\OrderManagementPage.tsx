import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { OrderService } from '../services/orderService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { Order } from '../../types'
import { ShoppingCart, Search, Package, Truck, CheckCircle, XCircle, Clock } from 'lucide-react'
import { formatPrice, formatDate } from '../lib/utils'
import { container, hoverLift, media } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const FiltersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
`

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 100%;

  @media (min-width: 768px) {
    max-width: 24rem;
  }
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: ${({ theme }) => theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.muted};
  width: 1rem;
  height: 1rem;
`

const SearchInput = styled(Input)`
  padding-left: ${({ theme }) => theme.spacing[10]};
`

const OrdersGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};

  @media (min-width: 768px) {
    gap: ${({ theme }) => theme.spacing[6]};
  }
`

const OrderCard = styled(Card)`
  ${hoverLift}
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  background: white;
`

const OrderDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};

  ${media.md`
    grid-template-columns: 2fr 1fr;
  `}
`

const ProductsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`

const ProductItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
`

const ProductName = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ProductDetails = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const ProductPrice = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: ${({ theme }) => theme.spacing[1]};
`

const UnitPrice = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const TotalPrice = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const OrderSummary = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
`

const SummaryItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const SummaryLabel = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  min-width: 80px;
`

const SummaryValue = styled.span`
  color: ${({ theme }) => theme.colors.text.primary};
`

const TotalAmount = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: ${({ theme }) => theme.spacing[3]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ActionsSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing[4]};
  padding-top: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${({ theme }) => theme.spacing[4]};
  margin-top: ${({ theme }) => theme.spacing[8]};

  @media (min-width: 640px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(5, 1fr);
  }
`

/**
 * Página de gestión de pedidos para administradores
 */
export const OrderManagementPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<Order['status'] | 'all'>('all')

  useEffect(() => {
    loadOrders()
  }, [])

  useEffect(() => {
    // Filtrar pedidos por búsqueda y estado
    let filtered = orders

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.products.some(p => p.name.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    setFilteredOrders(filtered)
  }, [orders, searchTerm, statusFilter])

  const loadOrders = async () => {
    try {
      setLoading(true)
      const allOrders = await OrderService.getAllOrders()
      setOrders(allOrders)
    } catch (error) {
      console.error('Error cargando pedidos:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusChange = async (orderId: string, newStatus: Order['status']) => {
    try {
      await OrderService.updateOrderStatus(orderId, newStatus)
      await loadOrders()
    } catch (error) {
      console.error('Error actualizando estado de orden:', error)
    }
  }

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'paid':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'shipped':
        return <Truck className="w-4 h-4 text-blue-500" />
      case 'delivered':
        return <Package className="w-4 h-4 text-green-600" />
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'shipped':
        return 'bg-blue-100 text-blue-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const statusOptions: { value: Order['status'] | 'all'; label: string }[] = [
    { value: 'all', label: 'Todos' },
    { value: 'pending', label: 'Pendientes' },
    { value: 'paid', label: 'Pagadas' },
    { value: 'shipped', label: 'Enviadas' },
    { value: 'delivered', label: 'Entregadas' },
    { value: 'cancelled', label: 'Canceladas' }
  ]

  return (
    <PageContainer>
      <PageHeader>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <PageTitle>
            <Package size={32} />
            Gestión de Pedidos
          </PageTitle>
          <Button onClick={loadOrders}>
            Actualizar
          </Button>
        </div>
      </PageHeader>

      {/* Filtros */}
      <FiltersContainer>
        <SearchContainer>
          <SearchIcon />
          <SearchInput
            placeholder="Buscar por ID de orden, usuario o producto..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>

        <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
          {statusOptions.map(option => (
            <Button
              key={option.value}
              variant={statusFilter === option.value ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter(option.value)}
            >
              {option.label}
            </Button>
          ))}
        </div>
      </FiltersContainer>

      {loading ? (
        <LoadingSpinner size="lg" className="h-64" />
      ) : filteredOrders.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <ShoppingCart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchTerm || statusFilter !== 'all' ? 'No se encontraron pedidos' : 'No hay pedidos'}
            </h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== 'all'
                ? 'Intenta ajustar los filtros de búsqueda'
                : 'Las pedidos aparecerán aquí cuando los clientes realicen compras'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <OrdersGrid>
          {filteredOrders.map(order => (
            <OrderCard key={order.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="w-5 h-5" />
                    Orden #{order.id.slice(-8)}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(order.status)}
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <OrderDetails>
                  <ProductsList>
                    {order.products.map((product, index) => (
                      <ProductItem key={index}>
                        <ProductInfo>
                          <ProductName>{product.name}</ProductName>
                          <ProductDetails>
                            Formato: {product.formatName} | Cantidad: {product.quantity}
                          </ProductDetails>
                        </ProductInfo>
                        <ProductPrice>
                          <UnitPrice>{formatPrice(product.unitPrice)} c/u</UnitPrice>
                          <TotalPrice>{formatPrice(product.quantity * product.unitPrice)}</TotalPrice>
                        </ProductPrice>
                      </ProductItem>
                    ))}
                  </ProductsList>

                  <OrderSummary>
                    <SummaryItem>
                      <SummaryLabel>Cliente:</SummaryLabel>
                      <SummaryValue>{order.userName}</SummaryValue>
                    </SummaryItem>
                    <SummaryItem>
                      <SummaryLabel>Email:</SummaryLabel>
                      <SummaryValue>{order.email}</SummaryValue>
                    </SummaryItem>
                    <SummaryItem>
                      <SummaryLabel>Dirección:</SummaryLabel>
                      <SummaryValue>
                        {order.userAddress.street}, {order.userAddress.city}
                      </SummaryValue>
                    </SummaryItem>
                    <SummaryItem>
                      <SummaryLabel>Fecha:</SummaryLabel>
                      <SummaryValue>{formatDate(order.createdAt)}</SummaryValue>
                    </SummaryItem>
                    <SummaryItem>
                      <SummaryLabel>Artículos:</SummaryLabel>
                      <SummaryValue>{order.products.length} productos</SummaryValue>
                    </SummaryItem>
                    <TotalAmount>
                      <span>Total:</span>
                      <span>{formatPrice(order.totalAmount)}</span>
                    </TotalAmount>
                  </OrderSummary>
                </OrderDetails>

                <ActionsSection>
                  {order.status === 'pending' && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleStatusChange(order.id, 'paid')}
                      >
                        Marcar como Pagada
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleStatusChange(order.id, 'cancelled')}
                      >
                        Cancelar
                      </Button>
                    </>
                  )}

                  {order.status === 'paid' && (
                    <Button
                      size="sm"
                      onClick={() => handleStatusChange(order.id, 'shipped')}
                    >
                      Marcar como Enviada
                    </Button>
                  )}

                  {order.status === 'shipped' && (
                    <Button
                      size="sm"
                      onClick={() => handleStatusChange(order.id, 'delivered')}
                    >
                      Marcar como Entregada
                    </Button>
                  )}

                  {/* Información adicional */}
                  {order.paymentId && (
                    <div className="ml-auto text-xs text-muted-foreground">
                      ID de Pago: {order.paymentId}
                    </div>
                  )}
                </ActionsSection>
              </CardContent>
            </OrderCard>
          ))}
        </OrdersGrid>
      )}

      {/* Estadísticas rápidas */}
      <StatsGrid>
        {statusOptions.slice(1).map(option => {
          const count = orders.filter(order => order.status === option.value).length
          return (
            <Card key={option.value}>
              <CardContent className="p-4 text-center">
                <p className="text-2xl font-bold">{count}</p>
                <p className="text-xs text-muted-foreground">{option.label}</p>
              </CardContent>
            </Card>
          )
        })}
      </StatsGrid>
    </PageContainer>
  )
}
