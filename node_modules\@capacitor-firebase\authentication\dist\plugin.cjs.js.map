{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/definitions.js", "esm/index.js", "esm/web.js"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n/**\n * @since 5.2.0\n */\nexport var Persistence;\n(function (Persistence) {\n    /**\n     * Long term persistence using IndexedDB.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"IndexedDbLocal\"] = \"INDEXED_DB_LOCAL\";\n    /**\n     * No persistence.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"InMemory\"] = \"IN_MEMORY\";\n    /**\n     * Long term persistence using local storage.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"BrowserLocal\"] = \"BROWSER_LOCAL\";\n    /**\n     * Temporary persistence using session storage.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"BrowserSession\"] = \"BROWSER_SESSION\";\n})(Persistence || (Persistence = {}));\nexport var ProviderId;\n(function (ProviderId) {\n    ProviderId[\"APPLE\"] = \"apple.com\";\n    ProviderId[\"FACEBOOK\"] = \"facebook.com\";\n    ProviderId[\"GAME_CENTER\"] = \"gc.apple.com\";\n    ProviderId[\"GITHUB\"] = \"github.com\";\n    ProviderId[\"GOOGLE\"] = \"google.com\";\n    ProviderId[\"MICROSOFT\"] = \"microsoft.com\";\n    ProviderId[\"PLAY_GAMES\"] = \"playgames.google.com\";\n    ProviderId[\"TWITTER\"] = \"twitter.com\";\n    ProviderId[\"YAHOO\"] = \"yahoo.com\";\n    ProviderId[\"PASSWORD\"] = \"password\";\n    ProviderId[\"PHONE\"] = \"phone\";\n})(ProviderId || (ProviderId = {}));\n//# sourceMappingURL=definitions.js.map", "import { registerPlugin } from '@capacitor/core';\nconst FirebaseAuthentication = registerPlugin('FirebaseAuthentication', {\n    web: () => import('./web').then(m => new m.FirebaseAuthenticationWeb()),\n});\nexport * from './definitions';\nexport { FirebaseAuthentication };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nimport { EmailAuthProvider, FacebookAuthProvider, GithubAuthProvider, GoogleAuthProvider, OAuthCredential, OAuthProvider, RecaptchaVerifier, TwitterAuthProvider, applyActionCode, browserLocalPersistence, browserSessionPersistence, confirmPasswordReset, connectAuthEmulator, createUserWithEmailAndPassword, deleteUser, fetchSignInMethodsForEmail, getAdditionalUserInfo, getAuth, getRedirectResult, inMemoryPersistence, indexedDBLocalPersistence, isSignInWithEmailLink, linkWithCredential, linkWithPhoneNumber, linkWithPopup, linkWithRedirect, reload, revokeAccessToken, sendEmailVerification, sendPasswordResetEmail, sendSignInLinkToEmail, setPersistence, signInAnonymously, signInWithCustomToken, signInWithEmailAndPassword, signInWithEmailLink, signInWithPhoneNumber, signInWithPopup, signInWithRedirect, unlink, updateEmail, updatePassword, updateProfile, verifyBeforeUpdateEmail, } from 'firebase/auth';\nimport { Persistence, ProviderId } from './definitions';\nexport class FirebaseAuthenticationWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.lastConfirmationResult = new Map();\n        const auth = getAuth();\n        auth.onAuthStateChanged(user => this.handleAuthStateChange(user));\n        auth.onIdTokenChanged(user => void this.handleIdTokenChange(user));\n    }\n    async applyActionCode(options) {\n        const auth = getAuth();\n        return applyActionCode(auth, options.oobCode);\n    }\n    async createUserWithEmailAndPassword(options) {\n        const auth = getAuth();\n        const userCredential = await createUserWithEmailAndPassword(auth, options.email, options.password);\n        return this.createSignInResult(userCredential, null);\n    }\n    async confirmPasswordReset(options) {\n        const auth = getAuth();\n        return confirmPasswordReset(auth, options.oobCode, options.newPassword);\n    }\n    async confirmVerificationCode(options) {\n        const { verificationCode, verificationId } = options;\n        const confirmationResult = this.lastConfirmationResult.get(verificationId);\n        if (!confirmationResult) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_CONFIRMATION_RESULT_MISSING);\n        }\n        const userCredential = await confirmationResult.confirm(verificationCode);\n        return this.createSignInResult(userCredential, null);\n    }\n    async deleteUser() {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return deleteUser(currentUser);\n    }\n    async fetchSignInMethodsForEmail(options) {\n        const auth = getAuth();\n        const signInMethods = await fetchSignInMethodsForEmail(auth, options.email);\n        return {\n            signInMethods,\n        };\n    }\n    async getPendingAuthResult() {\n        this.throwNotAvailableError();\n    }\n    async getCurrentUser() {\n        const auth = getAuth();\n        const userResult = this.createUserResult(auth.currentUser);\n        const result = {\n            user: userResult,\n        };\n        return result;\n    }\n    async getIdToken(options) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        const idToken = await auth.currentUser.getIdToken(options === null || options === void 0 ? void 0 : options.forceRefresh);\n        const result = {\n            token: idToken || '',\n        };\n        return result;\n    }\n    async getRedirectResult() {\n        const auth = getAuth();\n        const userCredential = await getRedirectResult(auth);\n        const authCredential = userCredential\n            ? OAuthProvider.credentialFromResult(userCredential)\n            : null;\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async getTenantId() {\n        const auth = getAuth();\n        return {\n            tenantId: auth.tenantId,\n        };\n    }\n    async isSignInWithEmailLink(options) {\n        const auth = getAuth();\n        return {\n            isSignInWithEmailLink: isSignInWithEmailLink(auth, options.emailLink),\n        };\n    }\n    async linkWithApple(options) {\n        const provider = new OAuthProvider(ProviderId.APPLE);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithEmailAndPassword(options) {\n        const authCredential = EmailAuthProvider.credential(options.email, options.password);\n        const userCredential = await this.linkCurrentUserWithCredential(authCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithEmailLink(options) {\n        const authCredential = EmailAuthProvider.credentialWithLink(options.email, options.emailLink);\n        const userCredential = await this.linkCurrentUserWithCredential(authCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithFacebook(options) {\n        const provider = new FacebookAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = FacebookAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithGameCenter() {\n        this.throwNotAvailableError();\n    }\n    async linkWithGithub(options) {\n        const provider = new GithubAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GithubAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithGoogle(options) {\n        const provider = new GoogleAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GoogleAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithMicrosoft(options) {\n        const provider = new OAuthProvider(ProviderId.MICROSOFT);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithOpenIdConnect(options) {\n        const provider = new OAuthProvider(options.providerId);\n        this.applySignInOptions(options, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithPhoneNumber(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        if (!options.phoneNumber) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n        }\n        if (!options.recaptchaVerifier ||\n            !(options.recaptchaVerifier instanceof RecaptchaVerifier)) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING);\n        }\n        try {\n            const confirmationResult = await linkWithPhoneNumber(currentUser, options.phoneNumber, options.recaptchaVerifier);\n            const { verificationId } = confirmationResult;\n            this.lastConfirmationResult.set(verificationId, confirmationResult);\n            const event = {\n                verificationId,\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT, event);\n        }\n        catch (error) {\n            const event = {\n                message: this.getErrorMessage(error),\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT, event);\n        }\n    }\n    async linkWithPlayGames() {\n        this.throwNotAvailableError();\n    }\n    async linkWithTwitter(options) {\n        const provider = new TwitterAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = TwitterAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithYahoo(options) {\n        const provider = new OAuthProvider(ProviderId.YAHOO);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async reload() {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return reload(currentUser);\n    }\n    async revokeAccessToken(options) {\n        const auth = getAuth();\n        return revokeAccessToken(auth, options.token);\n    }\n    async sendEmailVerification(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return sendEmailVerification(currentUser, options === null || options === void 0 ? void 0 : options.actionCodeSettings);\n    }\n    async sendPasswordResetEmail(options) {\n        const auth = getAuth();\n        return sendPasswordResetEmail(auth, options.email, options.actionCodeSettings);\n    }\n    async sendSignInLinkToEmail(options) {\n        const auth = getAuth();\n        return sendSignInLinkToEmail(auth, options.email, options.actionCodeSettings);\n    }\n    async setLanguageCode(options) {\n        const auth = getAuth();\n        auth.languageCode = options.languageCode;\n    }\n    async setPersistence(options) {\n        const auth = getAuth();\n        switch (options.persistence) {\n            case Persistence.BrowserLocal:\n                await setPersistence(auth, browserLocalPersistence);\n                break;\n            case Persistence.BrowserSession:\n                await setPersistence(auth, browserSessionPersistence);\n                break;\n            case Persistence.IndexedDbLocal:\n                await setPersistence(auth, indexedDBLocalPersistence);\n                break;\n            case Persistence.InMemory:\n                await setPersistence(auth, inMemoryPersistence);\n                break;\n        }\n    }\n    async setTenantId(options) {\n        const auth = getAuth();\n        auth.tenantId = options.tenantId;\n    }\n    async signInAnonymously() {\n        const auth = getAuth();\n        const userCredential = await signInAnonymously(auth);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithApple(options) {\n        const provider = new OAuthProvider(ProviderId.APPLE);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithCustomToken(options) {\n        const auth = getAuth();\n        const userCredential = await signInWithCustomToken(auth, options.token);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithEmailAndPassword(options) {\n        const auth = getAuth();\n        const userCredential = await signInWithEmailAndPassword(auth, options.email, options.password);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithEmailLink(options) {\n        const auth = getAuth();\n        const userCredential = await signInWithEmailLink(auth, options.email, options.emailLink);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithFacebook(options) {\n        const provider = new FacebookAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = FacebookAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithGithub(options) {\n        const provider = new GithubAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GithubAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithGoogle(options) {\n        const provider = new GoogleAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GoogleAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithMicrosoft(options) {\n        const provider = new OAuthProvider(ProviderId.MICROSOFT);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithOpenIdConnect(options) {\n        const provider = new OAuthProvider(options.providerId);\n        this.applySignInOptions(options, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithPhoneNumber(options) {\n        if (!options.phoneNumber) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n        }\n        if (!options.recaptchaVerifier ||\n            !(options.recaptchaVerifier instanceof RecaptchaVerifier)) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING);\n        }\n        const auth = getAuth();\n        try {\n            const confirmationResult = await signInWithPhoneNumber(auth, options.phoneNumber, options.recaptchaVerifier);\n            const { verificationId } = confirmationResult;\n            this.lastConfirmationResult.set(verificationId, confirmationResult);\n            const event = {\n                verificationId,\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT, event);\n        }\n        catch (error) {\n            const event = {\n                message: this.getErrorMessage(error),\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT, event);\n        }\n    }\n    async signInWithPlayGames() {\n        this.throwNotAvailableError();\n    }\n    async signInWithGameCenter() {\n        this.throwNotAvailableError();\n    }\n    async signInWithTwitter(options) {\n        const provider = new TwitterAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = TwitterAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithYahoo(options) {\n        const provider = new OAuthProvider(ProviderId.YAHOO);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signOut() {\n        const auth = getAuth();\n        await auth.signOut();\n    }\n    async unlink(options) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        const user = await unlink(auth.currentUser, options.providerId);\n        const userResult = this.createUserResult(user);\n        const result = {\n            user: userResult,\n        };\n        return result;\n    }\n    async updateEmail(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return updateEmail(currentUser, options.newEmail);\n    }\n    async updatePassword(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return updatePassword(currentUser, options.newPassword);\n    }\n    async updateProfile(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return updateProfile(currentUser, {\n            displayName: options.displayName,\n            photoURL: options.photoUrl,\n        });\n    }\n    async useAppLanguage() {\n        const auth = getAuth();\n        auth.useDeviceLanguage();\n    }\n    async useEmulator(options) {\n        const auth = getAuth();\n        const port = options.port || 9099;\n        const scheme = options.scheme || 'http';\n        if (options.host.includes('://')) {\n            connectAuthEmulator(auth, `${options.host}:${port}`);\n        }\n        else {\n            connectAuthEmulator(auth, `${scheme}://${options.host}:${port}`);\n        }\n    }\n    async verifyBeforeUpdateEmail(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return verifyBeforeUpdateEmail(currentUser, options === null || options === void 0 ? void 0 : options.newEmail, options === null || options === void 0 ? void 0 : options.actionCodeSettings);\n    }\n    handleAuthStateChange(user) {\n        const userResult = this.createUserResult(user);\n        const change = {\n            user: userResult,\n        };\n        this.notifyListeners(FirebaseAuthenticationWeb.AUTH_STATE_CHANGE_EVENT, change, true);\n    }\n    async handleIdTokenChange(user) {\n        if (!user) {\n            return;\n        }\n        const idToken = await user.getIdToken(false);\n        const result = {\n            token: idToken,\n        };\n        this.notifyListeners(FirebaseAuthenticationWeb.ID_TOKEN_CHANGE_EVENT, result, true);\n    }\n    applySignInOptions(options, provider) {\n        if (options.customParameters) {\n            const customParameters = {};\n            options.customParameters.map(parameter => {\n                customParameters[parameter.key] = parameter.value;\n            });\n            provider.setCustomParameters(customParameters);\n        }\n        if (options.scopes) {\n            for (const scope of options.scopes) {\n                provider.addScope(scope);\n            }\n        }\n    }\n    signInWithPopupOrRedirect(provider, mode) {\n        const auth = getAuth();\n        if (mode === 'redirect') {\n            return signInWithRedirect(auth, provider);\n        }\n        else {\n            return signInWithPopup(auth, provider);\n        }\n    }\n    linkCurrentUserWithPopupOrRedirect(provider, mode) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        if (mode === 'redirect') {\n            return linkWithRedirect(auth.currentUser, provider);\n        }\n        else {\n            return linkWithPopup(auth.currentUser, provider);\n        }\n    }\n    linkCurrentUserWithCredential(credential) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return linkWithCredential(auth.currentUser, credential);\n    }\n    requestAppTrackingTransparencyPermission() {\n        this.throwNotAvailableError();\n    }\n    checkAppTrackingTransparencyPermission() {\n        this.throwNotAvailableError();\n    }\n    createSignInResult(userCredential, authCredential) {\n        const userResult = this.createUserResult((userCredential === null || userCredential === void 0 ? void 0 : userCredential.user) || null);\n        const credentialResult = this.createCredentialResult(authCredential);\n        const additionalUserInfoResult = this.createAdditionalUserInfoResult(userCredential);\n        const result = {\n            user: userResult,\n            credential: credentialResult,\n            additionalUserInfo: additionalUserInfoResult,\n        };\n        return result;\n    }\n    createCredentialResult(credential) {\n        if (!credential) {\n            return null;\n        }\n        const result = {\n            providerId: credential.providerId,\n        };\n        if (credential instanceof OAuthCredential) {\n            result.accessToken = credential.accessToken;\n            result.idToken = credential.idToken;\n            result.secret = credential.secret;\n        }\n        return result;\n    }\n    createUserResult(user) {\n        if (!user) {\n            return null;\n        }\n        const result = {\n            displayName: user.displayName,\n            email: user.email,\n            emailVerified: user.emailVerified,\n            isAnonymous: user.isAnonymous,\n            metadata: this.createUserMetadataResult(user.metadata),\n            phoneNumber: user.phoneNumber,\n            photoUrl: user.photoURL,\n            providerData: this.createUserProviderDataResult(user.providerData),\n            providerId: user.providerId,\n            tenantId: user.tenantId,\n            uid: user.uid,\n        };\n        return result;\n    }\n    createUserMetadataResult(metadata) {\n        const result = {};\n        if (metadata.creationTime) {\n            result.creationTime = Date.parse(metadata.creationTime);\n        }\n        if (metadata.lastSignInTime) {\n            result.lastSignInTime = Date.parse(metadata.lastSignInTime);\n        }\n        return result;\n    }\n    createUserProviderDataResult(providerData) {\n        return providerData.map(data => ({\n            displayName: data.displayName,\n            email: data.email,\n            phoneNumber: data.phoneNumber,\n            photoUrl: data.photoURL,\n            providerId: data.providerId,\n            uid: data.uid,\n        }));\n    }\n    createAdditionalUserInfoResult(credential) {\n        if (!credential) {\n            return null;\n        }\n        const additionalUserInfo = getAdditionalUserInfo(credential);\n        if (!additionalUserInfo) {\n            return null;\n        }\n        const { isNewUser, profile, providerId, username } = additionalUserInfo;\n        const result = {\n            isNewUser,\n        };\n        if (providerId !== null) {\n            result.providerId = providerId;\n        }\n        if (profile !== null) {\n            result.profile = profile;\n        }\n        if (username !== null && username !== undefined) {\n            result.username = username;\n        }\n        return result;\n    }\n    getErrorMessage(error) {\n        if (error instanceof Object &&\n            'message' in error &&\n            typeof error['message'] === 'string') {\n            return error['message'];\n        }\n        return JSON.stringify(error);\n    }\n    throwNotAvailableError() {\n        throw new Error('Not available on web.');\n    }\n}\nFirebaseAuthenticationWeb.AUTH_STATE_CHANGE_EVENT = 'authStateChange';\nFirebaseAuthenticationWeb.ID_TOKEN_CHANGE_EVENT = 'idTokenChange';\nFirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT = 'phoneCodeSent';\nFirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT = 'phoneVerificationFailed';\nFirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN = 'No user is signed in.';\nFirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING = 'phoneNumber must be provided.';\nFirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING = 'recaptchaVerifier must be provided and must be an instance of RecaptchaVerifier.';\nFirebaseAuthenticationWeb.ERROR_CONFIRMATION_RESULT_MISSING = 'No confirmation result with this verification id was found.';\n//# sourceMappingURL=web.js.map"], "names": ["Persistence", "ProviderId", "registerPlugin", "WebPlugin", "auth", "getAuth", "applyActionCode", "createUserWithEmailAndPassword", "confirmPasswordReset", "deleteUser", "fetchSignInMethodsForEmail", "getRedirectResult", "OAuth<PERSON><PERSON><PERSON>", "isSignInWithEmailLink", "EmailAuthProvider", "FacebookAuthProvider", "GithubAuth<PERSON>rovider", "GoogleAuthProvider", "RecaptchaVerifier", "linkWithPhoneNumber", "TwitterAuthProvider", "reload", "revokeAccessToken", "sendEmailVerification", "sendPasswordResetEmail", "sendSignInLinkToEmail", "setPersistence", "browserLocalPersistence", "browserSessionPersistence", "indexedDBLocalPersistence", "inMemoryPersistence", "signInAnonymously", "signInWithCustomToken", "signInWithEmailAndPassword", "signInWithEmailLink", "signInWithPhoneNumber", "unlink", "updateEmail", "updatePassword", "updateProfile", "connectAuthEmulator", "verifyBeforeUpdateEmail", "signInWithRedirect", "signInWithPopup", "linkWithRedirect", "linkWithPopup", "linkWithCredential", "OAuthCredential", "getAdditionalUserInfo"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACWA;AACX,CAAC,UAAU,WAAW,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,gBAAgB,CAAC,GAAG,kBAAkB;AACtD;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,WAAW;AACzC;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,cAAc,CAAC,GAAG,eAAe;AACjD;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,gBAAgB,CAAC,GAAG,iBAAiB;AACrD,CAAC,EAAEA,mBAAW,KAAKA,mBAAW,GAAG,EAAE,CAAC,CAAC;AAC1BC;AACX,CAAC,UAAU,UAAU,EAAE;AACvB,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;AACrC,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,cAAc;AAC3C,IAAI,UAAU,CAAC,aAAa,CAAC,GAAG,cAAc;AAC9C,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,YAAY;AACvC,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,YAAY;AACvC,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,eAAe;AAC7C,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,sBAAsB;AACrD,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,aAAa;AACzC,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;AACrC,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU;AACvC,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;AACjC,CAAC,EAAEA,kBAAU,KAAKA,kBAAU,GAAG,EAAE,CAAC,CAAC;;AC3C9B,MAAC,sBAAsB,GAAGC,mBAAc,CAAC,wBAAwB,EAAE;AACxE,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,yBAAyB,EAAE,CAAC;AAC3E,CAAC;;ACAM,MAAM,yBAAyB,SAASC,cAAS,CAAC;AACzD,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,EAAE;AACf,QAAQ,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE;AAC/C,QAAQ,MAAMC,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQD,MAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACzE,QAAQA,MAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC1E;AACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;AACnC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAOC,oBAAe,CAACF,MAAI,EAAE,OAAO,CAAC,OAAO,CAAC;AACrD;AACA,IAAI,MAAM,8BAA8B,CAAC,OAAO,EAAE;AAClD,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,cAAc,GAAG,MAAME,mCAA8B,CAACH,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;AAC1G,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;AAC5D;AACA,IAAI,MAAM,oBAAoB,CAAC,OAAO,EAAE;AACxC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAOG,yBAAoB,CAACJ,MAAI,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC;AAC/E;AACA,IAAI,MAAM,uBAAuB,CAAC,OAAO,EAAE;AAC3C,QAAQ,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,OAAO;AAC5D,QAAQ,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC;AAClF,QAAQ,IAAI,CAAC,kBAAkB,EAAE;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,iCAAiC,CAAC;AACxF;AACA,QAAQ,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;AAC5D;AACA,IAAI,MAAM,UAAU,GAAG;AACvB,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOK,eAAU,CAAC,WAAW,CAAC;AACtC;AACA,IAAI,MAAM,0BAA0B,CAAC,OAAO,EAAE;AAC9C,QAAQ,MAAML,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,aAAa,GAAG,MAAMK,+BAA0B,CAACN,MAAI,EAAE,OAAO,CAAC,KAAK,CAAC;AACnF,QAAQ,OAAO;AACf,YAAY,aAAa;AACzB,SAAS;AACT;AACA,IAAI,MAAM,oBAAoB,GAAG;AACjC,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,MAAM,cAAc,GAAG;AAC3B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAACD,MAAI,CAAC,WAAW,CAAC;AAClE,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,IAAI,EAAE,UAAU;AAC5B,SAAS;AACT,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,MAAM,UAAU,CAAC,OAAO,EAAE;AAC9B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,MAAM,OAAO,GAAG,MAAMA,MAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,YAAY,CAAC;AACjI,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,KAAK,EAAE,OAAO,IAAI,EAAE;AAChC,SAAS;AACT,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,MAAM,iBAAiB,GAAG;AAC9B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,cAAc,GAAG,MAAMM,sBAAiB,CAACP,MAAI,CAAC;AAC5D,QAAQ,MAAM,cAAc,GAAG;AAC/B,cAAcQ,kBAAa,CAAC,oBAAoB,CAAC,cAAc;AAC/D,cAAc,IAAI;AAClB,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,WAAW,GAAG;AACxB,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAO;AACf,YAAY,QAAQ,EAAED,MAAI,CAAC,QAAQ;AACnC,SAAS;AACT;AACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;AACzC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAO;AACf,YAAY,qBAAqB,EAAEQ,0BAAqB,CAACT,MAAI,EAAE,OAAO,CAAC,SAAS,CAAC;AACjF,SAAS;AACT;AACA,IAAI,MAAM,aAAa,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,QAAQ,GAAG,IAAIQ,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;AAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,wBAAwB,CAAC,OAAO,EAAE;AAC5C,QAAQ,MAAM,cAAc,GAAGE,sBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;AAC5F,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC;AACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACrC,QAAQ,MAAM,cAAc,GAAGA,sBAAiB,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC;AACrG,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC;AACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,gBAAgB,CAAC,OAAO,EAAE;AACpC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,yBAAoB,EAAE;AACnD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGA,yBAAoB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACxF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,kBAAkB,GAAG;AAC/B,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;AAClC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;AACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;AAClC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;AACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACrC,QAAQ,MAAM,QAAQ,GAAG,IAAIL,kBAAa,CAACX,kBAAU,CAAC,SAAS,CAAC;AAChE,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;AACzC,QAAQ,MAAM,QAAQ,GAAG,IAAIA,kBAAa,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC;AAClD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC;AACpG,QAAQ,MAAM,cAAc,GAAGA,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,mBAAmB,CAAC,OAAO,EAAE;AACvC,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AAClC,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,0BAA0B,CAAC;AACjF;AACA,QAAQ,IAAI,CAAC,OAAO,CAAC,iBAAiB;AACtC,YAAY,EAAE,OAAO,CAAC,iBAAiB,YAAYc,sBAAiB,CAAC,EAAE;AACvE,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,gCAAgC,CAAC;AACvF;AACA,QAAQ,IAAI;AACZ,YAAY,MAAM,kBAAkB,GAAG,MAAMC,wBAAmB,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,iBAAiB,CAAC;AAC7H,YAAY,MAAM,EAAE,cAAc,EAAE,GAAG,kBAAkB;AACzD,YAAY,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;AAC/E,YAAY,MAAM,KAAK,GAAG;AAC1B,gBAAgB,cAAc;AAC9B,aAAa;AACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,KAAK,CAAC;AACxF;AACA,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,MAAM,KAAK,GAAG;AAC1B,gBAAgB,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;AACpD,aAAa;AACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,+BAA+B,EAAE,KAAK,CAAC;AAClG;AACA;AACA,IAAI,MAAM,iBAAiB,GAAG;AAC9B,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;AACnC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,wBAAmB,EAAE;AAClD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGA,wBAAmB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,aAAa,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,QAAQ,GAAG,IAAIR,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;AAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACtJ,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,MAAM,GAAG;AACnB,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOiB,WAAM,CAAC,WAAW,CAAC;AAClC;AACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACrC,QAAQ,MAAMjB,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAOiB,sBAAiB,CAAClB,MAAI,EAAE,OAAO,CAAC,KAAK,CAAC;AACrD;AACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;AACzC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOmB,0BAAqB,CAAC,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC/H;AACA,IAAI,MAAM,sBAAsB,CAAC,OAAO,EAAE;AAC1C,QAAQ,MAAMnB,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAOmB,2BAAsB,CAACpB,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,kBAAkB,CAAC;AACtF;AACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;AACzC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,OAAOoB,0BAAqB,CAACrB,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,kBAAkB,CAAC;AACrF;AACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;AACnC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQD,MAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY;AAChD;AACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;AAClC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,QAAQ,OAAO,CAAC,WAAW;AACnC,YAAY,KAAKL,mBAAW,CAAC,YAAY;AACzC,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAEuB,4BAAuB,CAAC;AACnE,gBAAgB;AAChB,YAAY,KAAK3B,mBAAW,CAAC,cAAc;AAC3C,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAEwB,8BAAyB,CAAC;AACrE,gBAAgB;AAChB,YAAY,KAAK5B,mBAAW,CAAC,cAAc;AAC3C,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAEyB,8BAAyB,CAAC;AACrE,gBAAgB;AAChB,YAAY,KAAK7B,mBAAW,CAAC,QAAQ;AACrC,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAE0B,wBAAmB,CAAC;AAC/D,gBAAgB;AAChB;AACA;AACA,IAAI,MAAM,WAAW,CAAC,OAAO,EAAE;AAC/B,QAAQ,MAAM1B,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQD,MAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ;AACxC;AACA,IAAI,MAAM,iBAAiB,GAAG;AAC9B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM0B,sBAAiB,CAAC3B,MAAI,CAAC;AAC5D,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;AAC5D;AACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;AACnC,QAAQ,MAAM,QAAQ,GAAG,IAAIQ,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;AAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;AACzC,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM2B,0BAAqB,CAAC5B,MAAI,EAAE,OAAO,CAAC,KAAK,CAAC;AAC/E,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;AAC5D;AACA,IAAI,MAAM,0BAA0B,CAAC,OAAO,EAAE;AAC9C,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM4B,+BAA0B,CAAC7B,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;AACtG,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;AAC5D;AACA,IAAI,MAAM,mBAAmB,CAAC,OAAO,EAAE;AACvC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM6B,wBAAmB,CAAC9B,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC;AAChG,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;AAC5D;AACA,IAAI,MAAM,kBAAkB,CAAC,OAAO,EAAE;AACtC,QAAQ,MAAM,QAAQ,GAAG,IAAIW,yBAAoB,EAAE;AACnD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGA,yBAAoB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACxF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,gBAAgB,CAAC,OAAO,EAAE;AACpC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;AACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,gBAAgB,CAAC,OAAO,EAAE;AACpC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;AACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,mBAAmB,CAAC,OAAO,EAAE;AACvC,QAAQ,MAAM,QAAQ,GAAG,IAAIL,kBAAa,CAACX,kBAAU,CAAC,SAAS,CAAC;AAChE,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,uBAAuB,CAAC,OAAO,EAAE;AAC3C,QAAQ,MAAM,QAAQ,GAAG,IAAIA,kBAAa,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC;AAClD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC;AAC3F,QAAQ,MAAM,cAAc,GAAGA,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;AACzC,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AAClC,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,0BAA0B,CAAC;AACjF;AACA,QAAQ,IAAI,CAAC,OAAO,CAAC,iBAAiB;AACtC,YAAY,EAAE,OAAO,CAAC,iBAAiB,YAAYM,sBAAiB,CAAC,EAAE;AACvE,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,gCAAgC,CAAC;AACvF;AACA,QAAQ,MAAMd,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,IAAI;AACZ,YAAY,MAAM,kBAAkB,GAAG,MAAM8B,0BAAqB,CAAC/B,MAAI,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,iBAAiB,CAAC;AACxH,YAAY,MAAM,EAAE,cAAc,EAAE,GAAG,kBAAkB;AACzD,YAAY,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;AAC/E,YAAY,MAAM,KAAK,GAAG;AAC1B,gBAAgB,cAAc;AAC9B,aAAa;AACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,KAAK,CAAC;AACxF;AACA,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,MAAM,KAAK,GAAG;AAC1B,gBAAgB,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;AACpD,aAAa;AACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,+BAA+B,EAAE,KAAK,CAAC;AAClG;AACA;AACA,IAAI,MAAM,mBAAmB,GAAG;AAChC,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,MAAM,oBAAoB,GAAG;AACjC,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACrC,QAAQ,MAAM,QAAQ,GAAG,IAAIgB,wBAAmB,EAAE;AAClD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGA,wBAAmB,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;AACnC,QAAQ,MAAM,QAAQ,GAAG,IAAIR,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;AAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7I,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;AACtE;AACA,IAAI,MAAM,OAAO,GAAG;AACpB,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAMD,MAAI,CAAC,OAAO,EAAE;AAC5B;AACA,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,MAAM,IAAI,GAAG,MAAMgC,WAAM,CAAChC,MAAI,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC;AACvE,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACtD,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,IAAI,EAAE,UAAU;AAC5B,SAAS;AACT,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,MAAM,WAAW,CAAC,OAAO,EAAE;AAC/B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOiC,gBAAW,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC;AACzD;AACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;AAClC,QAAQ,MAAMjC,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOkC,mBAAc,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC;AAC/D;AACA,IAAI,MAAM,aAAa,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAMlC,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOmC,kBAAa,CAAC,WAAW,EAAE;AAC1C,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW;AAC5C,YAAY,QAAQ,EAAE,OAAO,CAAC,QAAQ;AACtC,SAAS,CAAC;AACV;AACA,IAAI,MAAM,cAAc,GAAG;AAC3B,QAAQ,MAAMnC,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQD,MAAI,CAAC,iBAAiB,EAAE;AAChC;AACA,IAAI,MAAM,WAAW,CAAC,OAAO,EAAE;AAC/B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI;AACzC,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM;AAC/C,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC1C,YAAYmC,wBAAmB,CAACpC,MAAI,EAAE,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAChE;AACA,aAAa;AACb,YAAYoC,wBAAmB,CAACpC,MAAI,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5E;AACA;AACA,IAAI,MAAM,uBAAuB,CAAC,OAAO,EAAE;AAC3C,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAOqC,4BAAuB,CAAC,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;AACrM;AACA,IAAI,qBAAqB,CAAC,IAAI,EAAE;AAChC,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACtD,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,IAAI,EAAE,UAAU;AAC5B,SAAS;AACT,QAAQ,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,MAAM,EAAE,IAAI,CAAC;AAC7F;AACA,IAAI,MAAM,mBAAmB,CAAC,IAAI,EAAE;AACpC,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY;AACZ;AACA,QAAQ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACpD,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,KAAK,EAAE,OAAO;AAC1B,SAAS;AACT,QAAQ,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC;AAC3F;AACA,IAAI,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE;AAC1C,QAAQ,IAAI,OAAO,CAAC,gBAAgB,EAAE;AACtC,YAAY,MAAM,gBAAgB,GAAG,EAAE;AACvC,YAAY,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,IAAI;AACtD,gBAAgB,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK;AACjE,aAAa,CAAC;AACd,YAAY,QAAQ,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;AAC1D;AACA,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5B,YAAY,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAChD,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;AACxC;AACA;AACA;AACA,IAAI,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC9C,QAAQ,MAAMrC,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;AACjC,YAAY,OAAOqC,uBAAkB,CAACtC,MAAI,EAAE,QAAQ,CAAC;AACrD;AACA,aAAa;AACb,YAAY,OAAOuC,oBAAe,CAACvC,MAAI,EAAE,QAAQ,CAAC;AAClD;AACA;AACA,IAAI,kCAAkC,CAAC,QAAQ,EAAE,IAAI,EAAE;AACvD,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;AACjC,YAAY,OAAOwC,qBAAgB,CAACxC,MAAI,CAAC,WAAW,EAAE,QAAQ,CAAC;AAC/D;AACA,aAAa;AACb,YAAY,OAAOyC,kBAAa,CAACzC,MAAI,CAAC,WAAW,EAAE,QAAQ,CAAC;AAC5D;AACA;AACA,IAAI,6BAA6B,CAAC,UAAU,EAAE;AAC9C,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;AAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;AAC9E;AACA,QAAQ,OAAO0C,uBAAkB,CAAC1C,MAAI,CAAC,WAAW,EAAE,UAAU,CAAC;AAC/D;AACA,IAAI,wCAAwC,GAAG;AAC/C,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,sCAAsC,GAAG;AAC7C,QAAQ,IAAI,CAAC,sBAAsB,EAAE;AACrC;AACA,IAAI,kBAAkB,CAAC,cAAc,EAAE,cAAc,EAAE;AACvD,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,cAAc,KAAK,IAAI,IAAI,cAAc,KAAK,SAAM,GAAG,SAAM,GAAG,cAAc,CAAC,IAAI,KAAK,IAAI,CAAC;AAC/I,QAAQ,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;AAC5E,QAAQ,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,CAAC,cAAc,CAAC;AAC5F,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,IAAI,EAAE,UAAU;AAC5B,YAAY,UAAU,EAAE,gBAAgB;AACxC,YAAY,kBAAkB,EAAE,wBAAwB;AACxD,SAAS;AACT,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,sBAAsB,CAAC,UAAU,EAAE;AACvC,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,UAAU,EAAE,UAAU,CAAC,UAAU;AAC7C,SAAS;AACT,QAAQ,IAAI,UAAU,YAAY2C,oBAAe,EAAE;AACnD,YAAY,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW;AACvD,YAAY,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO;AAC/C,YAAY,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM;AAC7C;AACA,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE;AAC3B,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;AACzC,YAAY,KAAK,EAAE,IAAI,CAAC,KAAK;AAC7B,YAAY,aAAa,EAAE,IAAI,CAAC,aAAa;AAC7C,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;AACzC,YAAY,QAAQ,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClE,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;AACzC,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACnC,YAAY,YAAY,EAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC;AAC9E,YAAY,UAAU,EAAE,IAAI,CAAC,UAAU;AACvC,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACnC,YAAY,GAAG,EAAE,IAAI,CAAC,GAAG;AACzB,SAAS;AACT,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,wBAAwB,CAAC,QAAQ,EAAE;AACvC,QAAQ,MAAM,MAAM,GAAG,EAAE;AACzB,QAAQ,IAAI,QAAQ,CAAC,YAAY,EAAE;AACnC,YAAY,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;AACnE;AACA,QAAQ,IAAI,QAAQ,CAAC,cAAc,EAAE;AACrC,YAAY,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC;AACvE;AACA,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,4BAA4B,CAAC,YAAY,EAAE;AAC/C,QAAQ,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,KAAK;AACzC,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;AACzC,YAAY,KAAK,EAAE,IAAI,CAAC,KAAK;AAC7B,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;AACzC,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACnC,YAAY,UAAU,EAAE,IAAI,CAAC,UAAU;AACvC,YAAY,GAAG,EAAE,IAAI,CAAC,GAAG;AACzB,SAAS,CAAC,CAAC;AACX;AACA,IAAI,8BAA8B,CAAC,UAAU,EAAE;AAC/C,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,MAAM,kBAAkB,GAAGC,0BAAqB,CAAC,UAAU,CAAC;AACpE,QAAQ,IAAI,CAAC,kBAAkB,EAAE;AACjC,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,kBAAkB;AAC/E,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,SAAS;AACrB,SAAS;AACT,QAAQ,IAAI,UAAU,KAAK,IAAI,EAAE;AACjC,YAAY,MAAM,CAAC,UAAU,GAAG,UAAU;AAC1C;AACA,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE;AAC9B,YAAY,MAAM,CAAC,OAAO,GAAG,OAAO;AACpC;AACA,QAAQ,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;AACzD,YAAY,MAAM,CAAC,QAAQ,GAAG,QAAQ;AACtC;AACA,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,eAAe,CAAC,KAAK,EAAE;AAC3B,QAAQ,IAAI,KAAK,YAAY,MAAM;AACnC,YAAY,SAAS,IAAI,KAAK;AAC9B,YAAY,OAAO,KAAK,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE;AAClD,YAAY,OAAO,KAAK,CAAC,SAAS,CAAC;AACnC;AACA,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AACpC;AACA,IAAI,sBAAsB,GAAG;AAC7B,QAAQ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC;AAChD;AACA;AACA,yBAAyB,CAAC,uBAAuB,GAAG,iBAAiB;AACrE,yBAAyB,CAAC,qBAAqB,GAAG,eAAe;AACjE,yBAAyB,CAAC,qBAAqB,GAAG,eAAe;AACjE,yBAAyB,CAAC,+BAA+B,GAAG,yBAAyB;AACrF,yBAAyB,CAAC,uBAAuB,GAAG,uBAAuB;AAC3E,yBAAyB,CAAC,0BAA0B,GAAG,+BAA+B;AACtF,yBAAyB,CAAC,gCAAgC,GAAG,kFAAkF;AAC/I,yBAAyB,CAAC,iCAAiC,GAAG,6DAA6D;;;;;;;;;"}