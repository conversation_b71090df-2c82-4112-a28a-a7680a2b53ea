import { db } from '../lib/firebase'
import { doc, getDoc, updateDoc } from 'firebase/firestore'
import type { User } from '../../types'

export class UserService {
  /**
   * Obtiene la información completa del usuario
   */
  static async getUser(uid: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid))
      if (userDoc.exists()) {
        return userDoc.data() as User
      }
      return null
    } catch (error) {
      console.error('Error obteniendo usuario:', error)
      throw new Error('Error al obtener la información del usuario')
    }
  }

  /**
   * Actualiza la información del usuario
   */
  static async updateUser(uid: string, userData: Partial<User>): Promise<void> {
    try {
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        ...userData,
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error actualizando usuario:', error)
      throw new Error('Error al actualizar la información del usuario')
    }
  }
}
