import React from 'react'
import styled from 'styled-components'
import type { Product, ShoppingCartItem } from '../../types'
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { formatPrice } from '../lib/utils'
import { ShoppingCart, Plus, Minus } from 'lucide-react'
import { hoverLift, lineClamp, transition } from '../styles/utils'

const StyledCard = styled(Card)<{ $accentColor?: string }>`
  width: 100%;
  max-width: 24rem;
  margin: 0 auto;
  position: relative;
  ${hoverLift}

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${({ $accentColor, theme }) => $accentColor || theme.colors.primary[500]};
    border-radius: ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0 0;
  }
`

const ImageContainer = styled.div`
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0 0;
`

const ProductImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  ${transition('transform')}

  &:hover {
    transform: scale(1.05);
  }
`

const ProductTitle = styled(CardTitle)`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  ${lineClamp(2)}
`

const ProductDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  ${lineClamp(3)}
`

const PriceContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const Price = styled.span<{ $accentColor?: string }>`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ $accentColor, theme }) => $accentColor || theme.colors.primary[500]};
`

const CategoryBadge = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  background: ${({ theme }) => theme.colors.secondary[100]};
  color: ${({ theme }) => theme.colors.secondary[800]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
`

const StockWarning = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};

  &.low-stock {
    color: #ea580c;
  }

  &.out-of-stock {
    color: ${({ theme }) => theme.colors.error};
  }
`

const QuantityControls = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`

const QuantityGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`

const QuantityDisplay = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  min-width: 2rem;
  text-align: center;
`

const TotalPrice = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
`

interface ProductCardProps {
  product: Product
  cartItem?: ShoppingCartItem
  onAddToCart: (product: Product) => void
  onUpdateQuantity: (productId: string, quantity: number) => void
}

/**
 * Componente para mostrar una tarjeta de producto
 */
export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  cartItem,
  onAddToCart,
  onUpdateQuantity
}) => {
  const handleAddToCart = () => {
    onAddToCart(product)
  }

  const handleIncreaseQuantity = () => {
    const newQuantity = (cartItem?.quantity || 0) + 1
    onUpdateQuantity(product.id, newQuantity)
  }

  const handleDecreaseQuantity = () => {
    const newQuantity = Math.max(0, (cartItem?.quantity || 0) - 1)
    onUpdateQuantity(product.id, newQuantity)
  }

  return (
    <StyledCard $accentColor={product.color}>
      <CardHeader style={{ padding: 0 }}>
        <ImageContainer>
          <ProductImage
            src={product.imageUrl}
            alt={product.name}
          />
        </ImageContainer>
      </CardHeader>

      <CardContent style={{ padding: '1rem' }}>
        <ProductTitle>
          {product.name}
        </ProductTitle>
        <ProductDescription>
          {product.description}
        </ProductDescription>
        <PriceContainer>
          <Price $accentColor={product.color}>
            {formatPrice(product.price)}
          </Price>
          {product.category && (
            <CategoryBadge>
              {product.category}
            </CategoryBadge>
          )}
        </PriceContainer>
        {product.stock <= 5 && product.stock > 0 && (
          <StockWarning className="low-stock">
            ¡Solo quedan {product.stock} unidades!
          </StockWarning>
        )}
        {product.stock === 0 && (
          <StockWarning className="out-of-stock">
            Agotado
          </StockWarning>
        )}
      </CardContent>

      <CardFooter style={{ padding: '1rem', paddingTop: 0 }}>
        {!cartItem ? (
          <Button
            onClick={handleAddToCart}
            disabled={product.stock === 0}
            style={{ width: '100%', backgroundColor: product.color, borderColor: product.color }}
          >
            <ShoppingCart size={16} style={{ marginRight: '0.5rem' }} />
            Agregar al carrito
          </Button>
        ) : (
          <QuantityControls>
            <QuantityGroup>
              <Button
                variant="outline"
                style={{ color: product.color, borderColor: product.color }}
                size="icon"
                onClick={handleDecreaseQuantity}
                disabled={cartItem.quantity <= 1}
              >
                <Minus size={16} />
              </Button>
              <QuantityDisplay>
                {cartItem.quantity}
              </QuantityDisplay>
              <Button
                variant="outline"
                style={{ color: product.color, borderColor: product.color }}
                size="icon"
                onClick={handleIncreaseQuantity}
                disabled={cartItem.quantity >= product.stock}
              >
                <Plus size={16} />
              </Button>
            </QuantityGroup>
            <TotalPrice>
              {formatPrice(cartItem.quantity * cartItem.unitPrice)}
            </TotalPrice>
          </QuantityControls>
        )}
      </CardFooter>
    </StyledCard>
  )
}
