{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/header.tsx", "../../src/components/loadingspinner.tsx", "../../src/components/paypalcheckout.tsx", "../../src/components/productcard.tsx", "../../src/components/protectedroute.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/input.tsx", "../../src/config/firebase.example.ts", "../../src/contexts/appcontext.tsx", "../../src/data/sampleproducts.ts", "../../src/lib/firebase.ts", "../../src/lib/utils.ts", "../../src/pages/addressmanagementpage.tsx", "../../src/pages/admindashboardpage.tsx", "../../src/pages/adminloginpage.tsx", "../../src/pages/checkoutpage.tsx", "../../src/pages/createaddresspage.tsx", "../../src/pages/createproductpage.tsx", "../../src/pages/editaddresspage.tsx", "../../src/pages/editproductpage.tsx", "../../src/pages/homepage.tsx", "../../src/pages/loginpage.tsx", "../../src/pages/ordermanagementpage.tsx", "../../src/pages/orderspage.tsx", "../../src/pages/paymentsuccesspage.tsx", "../../src/pages/productmanagementpage.tsx", "../../src/pages/shoppingcartpage.tsx", "../../src/pages/userprofilepage.tsx", "../../src/services/addressservice.ts", "../../src/services/authservice.ts", "../../src/services/localstorageservice.ts", "../../src/services/orderservice.ts", "../../src/services/paypalservice.ts", "../../src/services/productservice.ts", "../../src/services/stripeservice.ts", "../../src/services/userservice.ts", "../../src/styles/styled.d.ts", "../../src/styles/theme.ts", "../../src/styles/utils.ts"], "version": "5.8.3"}