import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { useApp } from '../contexts/AppContext'
import { AddressService } from '../services/addressService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'

import { LoadingSpinner } from '../components/LoadingSpinner'
import type { UserAddress } from '../../types'
import { MapPin, Plus, Edit, Trash2, Star } from 'lucide-react'

import { container } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
`

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const PageDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
`

/**
 * Página de gestión de direcciones de usuario
 */
export const AddressManagementPage: React.FC = () => {
  const navigate = useNavigate()
  const { state } = useApp()
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (state.user) {
      loadAddresses()
    }
  }, [state.user])

  const loadAddresses = async () => {
    if (!state.user) return

    try {
      setLoading(true)
      const userAddresses = await AddressService.getUserAddresses(state.user.uid)
      setAddresses(userAddresses)
    } catch (error) {
      console.error('Error cargando direcciones:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (address: UserAddress) => {
    navigate(`/profile/address/edit/${address.id}`)
  }

  const handleDelete = async (addressId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta dirección?')) return

    try {
      await AddressService.deleteAddress(addressId)
      await loadAddresses()
    } catch (error) {
      console.error('Error eliminando dirección:', error)
    }
  }

  const handleSetDefault = async (addressId: string) => {
    if (!state.user) return

    try {
      await AddressService.setDefaultAddress(addressId, state.user.uid)
      await loadAddresses()
    } catch (error) {
      console.error('Error estableciendo dirección por defecto:', error)
    }
  }

  if (!state.user) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center' }}>
          <p>No hay usuario autenticado</p>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Mis Direcciones</PageTitle>
        <PageDescription>
          Gestiona tus direcciones de envío
        </PageDescription>
      </PageHeader>

      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '2rem' }}>
        <Button onClick={() => navigate('/profile/address/create')}>
          <Plus className="w-4 h-4 mr-2" />
          Agregar Dirección
        </Button>
      </div>

      {loading ? (
        <LoadingSpinner size="lg" className="h-64" />
      ) : addresses.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tienes direcciones guardadas</h3>
            <p className="text-muted-foreground mb-4">
              Agrega una dirección para facilitar tus compras futuras
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {addresses.map(address => (
            <Card key={address.id} className={address.isDefault ? 'ring-2 ring-primary' : ''}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    {address.name}
                  </span>
                  {address.isDefault && (
                    <span className="flex items-center gap-1 text-xs bg-primary text-primary-foreground px-2 py-1 rounded">
                      <Star className="w-5 h-3" />
                      {/* Por defecto */}
                    </span>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1 text-sm text-muted-foreground mb-4">
                  <p>{address.street}</p>
                  <p>{address.city}, {address.state} {address.postalCode}</p>
                  <p>{address.country}</p>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleEdit(address)}>
                    <Edit className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                  {!address.isDefault && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetDefault(address.id)}
                    >
                      <Star className="w-4 h-4 mr-1" />
                      Por defecto
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(address.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Eliminar
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </PageContainer>
  )
}
