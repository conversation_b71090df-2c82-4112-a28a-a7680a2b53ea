import React from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Button } from './ui/button'
import { ShoppingCart, User, LogOut, Settings } from 'lucide-react'
import { User as UserType, ShoppingCart as ShoppingCartType } from '../../types'
import { AuthService } from '../services/authService'
import { LocalStorageService } from '../services/localStorageService'

interface HeaderProps {
  user: UserType | null
  cart: ShoppingCartType
  onLogout: () => void
}

/**
 * Componente de encabezado de la aplicación
 */
export const Header: React.FC<HeaderProps> = ({ user, cart, onLogout }) => {
  const navigate = useNavigate()

  const handleLogout = async () => {
    try {
      await AuthService.logout()
      LocalStorageService.clearAll()
      onLogout()
      navigate('/')
    } catch (error) {
      console.error('Error cerrando sesión:', error)
    }
  }

  const cartItemsCount = cart.items.reduce((total, item) => total + item.quantity, 0)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        {/* Logo */}
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">F</span>
          </div>
          <span className="font-bold text-xl text-primary">Fuxion</span>
        </Link>

        {/* Navegación central */}
        <nav className="hidden md:flex items-center space-x-6">
          <Link 
            to="/" 
            className="text-sm font-medium hover:text-primary transition-colors"
          >
            Productos
          </Link>
          {user && (
            <>
              <Link 
                to="/profile" 
                className="text-sm font-medium hover:text-primary transition-colors"
              >
                Mi Perfil
              </Link>
              <Link 
                to="/addresses" 
                className="text-sm font-medium hover:text-primary transition-colors"
              >
                Direcciones
              </Link>
            </>
          )}
        </nav>

        {/* Acciones del usuario */}
        <div className="flex items-center space-x-2">
          {/* Carrito */}
          <Button variant="ghost" size="icon" asChild>
            <Link to="/cart" className="relative">
              <ShoppingCart className="w-5 h-5" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>
          </Button>

          {user ? (
            <>
              {/* Usuario logueado */}
              <div className="hidden sm:flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  Hola, {user.displayName || user.email}
                </span>
              </div>
              
              <Button variant="ghost" size="icon" asChild>
                <Link to="/profile">
                  <User className="w-5 h-5" />
                </Link>
              </Button>
              
              <Button variant="ghost" size="icon" onClick={handleLogout}>
                <LogOut className="w-5 h-5" />
              </Button>
            </>
          ) : (
            <>
              {/* Usuario no logueado */}
              <Button variant="ghost" asChild>
                <Link to="/login">Iniciar Sesión</Link>
              </Button>
            </>
          )}

          {/* Enlace a admin (solo si es admin) */}
          <Button variant="ghost" size="icon" asChild>
            <Link to="/admin">
              <Settings className="w-5 h-5" />
            </Link>
          </Button>
        </div>
      </div>
    </header>
  )
}
