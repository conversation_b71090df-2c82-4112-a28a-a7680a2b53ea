import React, { useState, useCallback } from 'react'
import styled from 'styled-components'
import { PayPalButtons, PayPalScriptProvider } from '@paypal/react-paypal-js'
import { Browser } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import { LoadingSpinner } from './LoadingSpinner'
import { Button } from './ui/button'
import { ExternalLink } from 'lucide-react'

// Configuración de PayPal
const PAYPAL_CONFIG = {
  clientId: 'AYGKb-LV8LHkfS7-AfAfCqOGZXZG98WpjhIDPfRKOz4f44Q3lFPIe6w1kaHiz74WYvb71Xdv9JjoxT7SXNkMspp7g61Fpvlr',
  currency: 'USD',
  intent: 'capture',
  components: 'buttons',
  'disable-funding': 'credit,card', // Opcional: deshabilitar métodos de pago específicos
}

const PayPalContainer = styled.div`
  width: 100%;
  min-height: 50px;
  position: relative;
`

const ErrorMessage = styled.div`
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const SuccessMessage = styled.div`
  color: #16a34a;
  background: rgba(22, 163, 74, 0.1);
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const MobilePaymentButton = styled(Button)`
  width: 100%;
  background: #0070ba;
  border-color: #0070ba;
  
  &:hover {
    background: #005ea6;
    border-color: #005ea6;
  }
`

export interface PayPalCheckoutProps {
  amount: number
  description?: string
  onSuccess?: (transactionId: string) => void
  onError?: (error: string) => void
  onCancel?: () => void
  successUrl?: string
  disabled?: boolean
}

/**
 * Componente de checkout de PayPal compatible con web y móvil
 */
export const PayPalCheckout: React.FC<PayPalCheckoutProps> = ({
  amount,
  description = 'Pago',
  onSuccess,
  onError,
  onCancel,
  successUrl,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')
  const [isMobile] = useState(Capacitor.getPlatform() !== 'web')

  // Crear orden de PayPal
  const createOrder = useCallback(async () => {
    try {
      setError('')
      setIsLoading(true)

      // En producción, esto sería una llamada a tu backend
      const orderData = {
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: amount.toFixed(2),
            },
            description: description,
          },
        ],
        application_context: {
          brand_name: 'AriFuxion',
          landing_page: 'NO_PREFERENCE',
          user_action: 'PAY_NOW',
          return_url: successUrl || `${window.location.origin}/payment-success`,
          cancel_url: `${window.location.origin}/checkout`,
        },
      }

      console.log('Creando orden PayPal:', orderData)

      // Simular creación de orden (en producción sería una llamada al backend)
      const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      
      return orderId
    } catch (error) {
      console.error('Error creando orden:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [amount, description, successUrl])

  // Capturar pago
  const onApprove = useCallback(async (data: any) => {
    try {
      setIsLoading(true)
      setError('')

      console.log('Pago aprobado:', data)

      // En producción, aquí capturarías el pago en tu backend
      const captureData = {
        orderID: data.orderID,
        payerID: data.payerID,
      }

      // Simular captura de pago
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Simular posible error (5% de probabilidad)
      if (Math.random() < 0.05) {
        throw new Error('Error simulado: Pago rechazado por PayPal')
      }

      // Generar transaction ID simulado
      const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      console.log('Pago capturado exitosamente:', { transactionId, captureData })
      
      setSuccess(`Pago exitoso. ID: ${transactionId}`)
      
      // Llamar callback de éxito
      if (onSuccess) {
        onSuccess(transactionId)
      }

      // Redirigir a página de éxito si se especifica
      if (successUrl) {
        setTimeout(() => {
          window.location.href = `${successUrl}?transaction_id=${transactionId}&amount=${amount}`
        }, 2000)
      }

    } catch (error: any) {
      console.error('Error capturando pago:', error)
      const errorMessage = error.message || 'Error procesando el pago'
      setError(errorMessage)
      
      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }, [amount, onSuccess, onError, successUrl])

  // Manejar errores
  const onErrorHandler = useCallback((error: any) => {
    console.error('Error en PayPal:', error)
    const errorMessage = 'Error en el sistema de pagos. Por favor, intenta de nuevo.'
    setError(errorMessage)
    
    if (onError) {
      onError(errorMessage)
    }
  }, [onError])

  // Manejar cancelación
  const onCancelHandler = useCallback(() => {
    console.log('Pago cancelado por el usuario')
    setError('Pago cancelado')
    
    if (onCancel) {
      onCancel()
    }
  }, [onCancel])

  // Abrir PayPal en navegador externo para móvil
  const openPayPalInBrowser = useCallback(async () => {
    try {
      setIsLoading(true)
      setError('')

      // Crear orden
      const orderId = await createOrder()

      // URL de PayPal para móvil
      const paypalUrl = `https://www.paypal.com/checkoutnow?token=${orderId}`

      if (Capacitor.getPlatform() !== 'web') {
        // Abrir en navegador externo en móvil
        await Browser.open({
          url: paypalUrl,
          windowName: '_system',
          presentationStyle: 'popover'
        })

        // Simular éxito después de un delay (en producción manejarías esto con deep links)
        setTimeout(() => {
          const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          console.log('Pago móvil simulado exitoso:', transactionId)
          
          if (onSuccess) {
            onSuccess(transactionId)
          }
        }, 5000)
      } else {
        // En web, abrir en nueva ventana
        window.open(paypalUrl, '_blank')
      }

    } catch (error: any) {
      console.error('Error abriendo PayPal:', error)
      const errorMessage = error.message || 'Error abriendo PayPal'
      setError(errorMessage)
      
      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }, [createOrder, onSuccess, onError])

  // Renderizar para móvil
  if (isMobile) {
    return (
      <PayPalContainer>
        <MobilePaymentButton
          onClick={openPayPalInBrowser}
          disabled={disabled || isLoading}
        >
          {isLoading ? (
            <LoadingSpinner size="sm" />
          ) : (
            <>
              <ExternalLink size={16} style={{ marginRight: '0.5rem' }} />
              Pagar con PayPal ${amount.toFixed(2)}
            </>
          )}
        </MobilePaymentButton>

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
      </PayPalContainer>
    )
  }

  // Renderizar para web
  return (
    <PayPalScriptProvider options={{
      clientId: PAYPAL_CONFIG.clientId,
      currency: PAYPAL_CONFIG.currency,
      intent: PAYPAL_CONFIG.intent,
      components: PAYPAL_CONFIG.components,
    }}>
      <PayPalContainer>
        {isLoading && (
          <div style={{ 
            position: 'absolute', 
            top: '50%', 
            left: '50%', 
            transform: 'translate(-50%, -50%)',
            zIndex: 10
          }}>
            <LoadingSpinner size="sm" />
          </div>
        )}

        <PayPalButtons
          style={{
            layout: 'vertical',
            color: 'blue',
            shape: 'rect',
            label: 'paypal',
          }}
          disabled={disabled || isLoading}
          createOrder={createOrder}
          onApprove={onApprove}
          onError={onErrorHandler}
          onCancel={onCancelHandler}
        />

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
      </PayPalContainer>
    </PayPalScriptProvider>
  )
}

export default PayPalCheckout
