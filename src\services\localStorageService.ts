import { User, ShoppingCart } from '../../types'

/**
 * Servicio para gestión de localStorage
 */
export class LocalStorageService {
  
  // Claves para localStorage
  private static readonly SESSION_USER_KEY = 'sessionUser'
  private static readonly SHOPPING_CART_KEY = 'shoppingCart'
  
  /**
   * Guarda el usuario en sesión
   */
  static saveSessionUser(user: User): void {
    try {
      localStorage.setItem(this.SESSION_USER_KEY, JSON.stringify(user))
    } catch (error) {
      console.error('Error guardando usuario en localStorage:', error)
    }
  }
  
  /**
   * Obtiene el usuario en sesión
   */
  static getSessionUser(): User | null {
    try {
      const userJson = localStorage.getItem(this.SESSION_USER_KEY)
      return userJson ? JSON.parse(userJson) : null
    } catch (error) {
      console.error('Error obteniendo usuario de localStorage:', error)
      return null
    }
  }
  
  /**
   * Elimina el usuario en sesión
   */
  static removeSessionUser(): void {
    try {
      localStorage.removeItem(this.SESSION_USER_KEY)
    } catch (error) {
      console.error('Error eliminando usuario de localStorage:', error)
    }
  }
  
  /**
   * Guarda el carrito de compras
   */
  static saveShoppingCart(cart: ShoppingCart): void {
    try {
      localStorage.setItem(this.SHOPPING_CART_KEY, JSON.stringify(cart))
    } catch (error) {
      console.error('Error guardando carrito en localStorage:', error)
    }
  }
  
  /**
   * Obtiene el carrito de compras
   */
  static getShoppingCart(): ShoppingCart | null {
    try {
      const cartJson = localStorage.getItem(this.SHOPPING_CART_KEY)
      return cartJson ? JSON.parse(cartJson) : null
    } catch (error) {
      console.error('Error obteniendo carrito de localStorage:', error)
      return null
    }
  }
  
  /**
   * Elimina el carrito de compras
   */
  static removeShoppingCart(): void {
    try {
      localStorage.removeItem(this.SHOPPING_CART_KEY)
    } catch (error) {
      console.error('Error eliminando carrito de localStorage:', error)
    }
  }
  
  /**
   * Obtiene el carrito por defecto vacío
   */
  static getDefaultCart(): ShoppingCart {
    return {
      items: [],
      totalAmount: 0,
      updatedAt: new Date().toISOString()
    }
  }
  
  /**
   * Limpia todos los datos de localStorage
   */
  static clearAll(): void {
    try {
      this.removeSessionUser()
      this.removeShoppingCart()
    } catch (error) {
      console.error('Error limpiando localStorage:', error)
    }
  }
  
  /**
   * Verifica si hay datos en localStorage
   */
  static hasStoredData(): boolean {
    return !!(this.getSessionUser() || this.getShoppingCart())
  }
}
