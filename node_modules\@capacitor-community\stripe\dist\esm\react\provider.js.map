{"version": 3, "file": "provider.js", "sourceRoot": "", "sources": ["../../../src/react/provider.tsx"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,KAAK,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAC9E,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAGlE,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,MAAM,aAAa,GAAG,aAAa,CAAyB;IACxD,MAAM,EAAE,SAAoC;IAC5C,mBAAmB,EAAE,KAAK;IAC1B,oBAAoB,EAAE,KAAK;CAC9B,CAAC,CAAA;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,GAA2B,EAAE;IAC3D,OAAO,UAAU,CAAC,aAAa,CAAC,CAAA;AACpC,CAAC,CAAA;AAKD,MAAM,CAAC,MAAM,uBAAuB,GAAqC,CAAC,EAIzE,EAAE,EAAE;QAJqE,EACtE,QAAQ,EACR,QAAQ,OAEX,EADM,iBAAiB,cAHkD,wBAIzE,CADuB;IAEpB,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,EAAgB,CAAA;IACpD,MAAM,CAAC,mBAAmB,EAAE,0BAA0B,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzE,MAAM,CAAC,oBAAoB,EAAE,2BAA2B,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IAC3E,SAAS,CAAC,GAAG,EAAE;QACX,MAAM,IAAI,GAAG,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAA;QACtF,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,IAAI,CAAC,iBAAiB,CAAC,cAAc;gBAAE,OAAM;YAC7C,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC;iBAC/B,IAAI,CAAC,GAAG,EAAE;gBACP,OAAO,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC1C,0BAA0B,CAAC,IAAI,CAAC,CAAA;gBACpC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;oBACV,0BAA0B,CAAC,KAAK,CAAC,CAAA;gBACrC,CAAC,CAAC,CAAA;YACN,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,EAAE;gBACP,OAAO,MAAM,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC3C,2BAA2B,CAAC,IAAI,CAAC,CAAA;gBACrC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;oBACV,2BAA2B,CAAC,KAAK,CAAC,CAAA;gBACtC,CAAC,CAAC,CAAA;YACN,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,EAAE;gBACP,SAAS,CAAC,MAAM,CAAC,CAAA;YACrB,CAAC,CAAC,CAAA;QACV,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAC,CAAA;IACnD,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,QAAQ;YAAE,OAAO,0CAAG,QAAQ,CAAI,CAAA;QACpC,OAAO,IAAI,CAAC;KACf;IACD,OAAO,CACH,oBAAC,aAAa,CAAC,QAAQ,IAAC,KAAK,EAAE;YAC3B,MAAM;YACN,oBAAoB;YACpB,mBAAmB;SACtB,IACI,QAAQ,CACY,CAC5B,CAAA;AACL,CAAC,CAAA"}