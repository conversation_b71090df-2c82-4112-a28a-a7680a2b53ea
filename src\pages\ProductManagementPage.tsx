import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { ProductService } from '../services/productService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { Product, ProductFormat } from '../../types'
import { Package, Plus, Edit, Trash2, Search, Eye, EyeOff, X } from 'lucide-react'
import { formatPrice, generateId } from '../lib/utils'
import { container, hoverLift } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: ${({ theme }) => theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.muted};
  width: 1rem;
  height: 1rem;
`

const SearchInput = styled(Input)`
  padding-left: ${({ theme }) => theme.spacing[10]};
`

const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
`

const ProductCard = styled(Card)`
  ${hoverLift}
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  background: white;
`

const ProductImage = styled.img`
  width: 100%;
  height: 12rem;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0 0;
`

const ProductInfo = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};
`

const ProductName = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const ProductDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  line-height: 1.5;
`

const FormatsContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const FormatItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`

const FormatInfo = styled.div`
  flex: 1;
`

const FormatName = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const FormatDetails = styled.div`
  color: ${({ theme }) => theme.colors.text.muted};
  font-size: ${({ theme }) => theme.fontSizes.xs};
`

const ProductActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`

const StatusBadge = styled.span<{ $active: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ $active, theme }) =>
    $active ? theme.colors.green[100] : theme.colors.red[100]};
  color: ${({ $active, theme }) =>
    $active ? theme.colors.green[800] : theme.colors.red[800]};
`

/**
 * Página de gestión de productos para administradores
 */
export const ProductManagementPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    imageUrl: '',
    category: '',
    isActive: true
  })

  const [formats, setFormats] = useState<ProductFormat[]>([
    { name: '', description: '', price: 0, stock: 0 }
  ])

  useEffect(() => {
    loadProducts()
  }, [])

  useEffect(() => {
    // Filtrar productos por búsqueda
    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.category && product.category.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    setFilteredProducts(filtered)
  }, [products, searchTerm])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const allProducts = await ProductService.getAllProducts()
      setProducts(allProducts)
    } catch (error) {
      console.error('Error cargando productos:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const productData = {
        name: formData.name,
        description: formData.description,
        imageUrl: formData.imageUrl,
        formats: formats.filter(f => f.name.trim() !== ''),
        category: formData.category || undefined,
        isActive: formData.isActive
      }

      if (editingProduct) {
        await ProductService.updateProduct(editingProduct.id, productData)
      } else {
        await ProductService.createProduct({
          ...productData,
          id: generateId(),
          createdAt: new Date().toISOString()
        })
      }

      await loadProducts()
      handleCloseDialog()
    } catch (error) {
      console.error('Error guardando producto:', error)
    }
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    setFormData({
      name: product.name,
      description: product.description,
      imageUrl: product.imageUrl,
      category: product.category || '',
      isActive: product.isActive
    })
    setFormats(product.formats.length > 0 ? product.formats : [
      { name: '', description: '', price: 0, stock: 0 }
    ])
    setDialogOpen(true)
  }

  const handleDelete = async (productId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este producto?')) return

    try {
      await ProductService.deleteProduct(productId)
      await loadProducts()
    } catch (error) {
      console.error('Error eliminando producto:', error)
    }
  }

  const handleToggleActive = async (product: Product) => {
    try {
      await ProductService.updateProduct(product.id, {
        isActive: !product.isActive
      })
      await loadProducts()
    } catch (error) {
      console.error('Error actualizando estado del producto:', error)
    }
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setEditingProduct(null)
    setFormData({
      name: '',
      description: '',
      imageUrl: '',
      category: '',
      isActive: true
    })
    setFormats([{ name: '', description: '', price: 0, stock: 0 }])
  }

  const addFormat = () => {
    setFormats([...formats, { name: '', description: '', price: 0, stock: 0 }])
  }

  const removeFormat = (index: number) => {
    if (formats.length > 1) {
      setFormats(formats.filter((_, i) => i !== index))
    }
  }

  const updateFormat = (index: number, field: keyof ProductFormat, value: string | number) => {
    const updatedFormats = [...formats]
    updatedFormats[index] = { ...updatedFormats[index], [field]: value }
    setFormats(updatedFormats)
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>
          <Package size={32} />
          Gestión de Productos
        </PageTitle>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Producto
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingProduct ? 'Editar Producto' : 'Nuevo Producto'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-2">
                    Nombre del producto *
                  </label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium mb-2">
                    Categoría
                  </label>
                  <Input
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    placeholder="Ej: Proteínas, Vitaminas"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium mb-2">
                  Descripción *
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full min-h-[100px] px-3 py-2 border border-input rounded-md"
                  required
                />
              </div>

              <div>
                <label htmlFor="imageUrl" className="block text-sm font-medium mb-2">
                  URL de la imagen *
                </label>
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  type="url"
                  value={formData.imageUrl}
                  onChange={handleInputChange}
                  placeholder="https://ejemplo.com/imagen.jpg"
                  required
                />
              </div>

              {/* Sección de Formatos */}
              <div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
                  <label className="block text-sm font-medium">
                    Formatos del Producto *
                  </label>
                  <Button type="button" variant="outline" size="sm" onClick={addFormat}>
                    <Plus size={16} style={{ marginRight: '0.5rem' }} />
                    Agregar Formato
                  </Button>
                </div>

                <div style={{ maxHeight: '300px', overflowY: 'auto', border: '1px solid #e5e7eb', borderRadius: '0.5rem', padding: '1rem' }}>
                  {formats.map((format, index) => (
                    <div key={index} style={{
                      border: '1px solid #e5e7eb',
                      borderRadius: '0.5rem',
                      padding: '1rem',
                      marginBottom: '1rem',
                      background: '#f9fafb'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.75rem' }}>
                        <h4 style={{ fontSize: '0.875rem', fontWeight: '600' }}>
                          Formato {index + 1}
                        </h4>
                        {formats.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeFormat(index)}
                            style={{ color: '#ef4444', borderColor: '#ef4444' }}
                          >
                            <X size={16} />
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium mb-1">
                            Nombre del formato *
                          </label>
                          <Input
                            type="text"
                            value={format.name}
                            onChange={(e) => updateFormat(index, 'name', e.target.value)}
                            placeholder="ej: 500ml, 1kg, Caja x12"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium mb-1">
                            Precio (USD) *
                          </label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={format.price}
                            onChange={(e) => updateFormat(index, 'price', parseFloat(e.target.value) || 0)}
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium mb-1">
                            Stock (-1 = infinito) *
                          </label>
                          <Input
                            type="number"
                            min="-1"
                            value={format.stock}
                            onChange={(e) => updateFormat(index, 'stock', parseInt(e.target.value) || 0)}
                            placeholder="-1 para stock infinito"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium mb-1">
                            Descripción
                          </label>
                          <Input
                            type="text"
                            value={format.description}
                            onChange={(e) => updateFormat(index, 'description', e.target.value)}
                            placeholder="Descripción adicional"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="rounded"
                />
                <label htmlFor="isActive" className="text-sm">
                  Producto activo (visible en la tienda)
                </label>
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  {editingProduct ? 'Actualizar' : 'Crear'} Producto
                </Button>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Cancelar
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </PageHeader>

      {/* Búsqueda */}
      <SearchContainer>
        <SearchIcon />
        <SearchInput
          placeholder="Buscar productos por nombre, descripción o categoría..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>

      {loading ? (
        <LoadingSpinner size="lg" className="h-64" />
      ) : filteredProducts.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchTerm ? 'No se encontraron productos' : 'No hay productos'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm 
                ? 'Intenta con otros términos de búsqueda'
                : 'Comienza agregando tu primer producto'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <ProductsGrid>
          {filteredProducts.map(product => (
            <ProductCard key={product.id} style={{ opacity: !product.isActive ? 0.6 : 1 }}>
              <ProductImage
                src={product.imageUrl}
                alt={product.name}
              />

              <ProductInfo>
                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <ProductName>{product.name}</ProductName>
                  <StatusBadge $active={product.isActive}>
                    {product.isActive ? <Eye size={12} /> : <EyeOff size={12} />}
                    {product.isActive ? 'Activo' : 'Inactivo'}
                  </StatusBadge>
                </div>

                <ProductDescription>
                  {product.description}
                </ProductDescription>

                {/* Mostrar formatos */}
                <FormatsContainer>
                  {product.formats.map((format, index) => (
                    <FormatItem key={index}>
                      <FormatInfo>
                        <FormatName>{format.name}</FormatName>
                        <FormatDetails>
                          {format.description} - {formatPrice(format.price)}
                        </FormatDetails>
                      </FormatInfo>
                      <div style={{ fontSize: '0.75rem', color: format.stock === -1 ? '#10B981' : format.stock <= 5 ? '#EF4444' : '#6B7280' }}>
                        {format.stock === -1 ? '∞' : `${format.stock} unidades`}
                      </div>
                    </FormatItem>
                  ))}
                </FormatsContainer>
                
                {product.category && (
                  <div style={{ fontSize: '0.75rem', color: '#6B7280', marginBottom: '0.75rem' }}>
                    Categoría: {product.category}
                  </div>
                )}
              </ProductInfo>

              <ProductActions>
                  <Button variant="outline" size="sm" onClick={() => handleEdit(product)}>
                    <Edit className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleToggleActive(product)}
                  >
                    {product.isActive ? (
                      <>
                        <EyeOff className="w-4 h-4 mr-1" />
                        Ocultar
                      </>
                    ) : (
                      <>
                        <Eye className="w-4 h-4 mr-1" />
                        Mostrar
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDelete(product.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Eliminar
                  </Button>
                </ProductActions>
            </ProductCard>
          ))}
        </ProductsGrid>
      )}
    </PageContainer>
  )
}
