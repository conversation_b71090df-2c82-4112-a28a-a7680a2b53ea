import React, { useState } from 'react'
import styled from 'styled-components'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { useApp } from '../contexts/AppContext'
import { AuthService } from '../services/authService'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { container } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
`

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 28rem;
  box-shadow: ${({ theme }) => theme.shadows.lg};
`

const GoogleButton = styled(But<PERSON>)`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  font-size: ${({ theme }) => theme.fontSizes.base};
  background: #4285f4;
  border-color: #4285f4;

  &:hover:not(:disabled) {
    background: #3367d6;
    border-color: #3367d6;
  }
`

const GoogleIcon = styled.div`
  width: 1.25rem;
  height: 1.25rem;
  background: white;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #4285f4;
  font-size: 0.875rem;
`

const InfoText = styled.p`
  text-align: center;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-top: ${({ theme }) => theme.spacing[4]};
`

const AdminLink = styled(Link)`
  display: block;
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing[4]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  text-decoration: none;

  &:hover {
    color: ${({ theme }) => theme.colors.primary[500]};
  }
`

/**
 * Página de inicio de sesión para usuarios con Google OAuth
 */
export const LoginPage: React.FC = () => {
  const { dispatch } = useApp()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const redirectTo = searchParams.get('redirect') || '/'

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleGoogleSignIn = async () => {
    setLoading(true)
    setError('')

    try {
      const user = await AuthService.signInWithGoogle()
      dispatch({ type: 'SET_USER', payload: user })
      navigate(redirectTo)
    } catch (error: any) {
      console.error('Error en autenticación con Google:', error)
      setError(error.message || 'Error en la autenticación con Google')
    } finally {
      setLoading(false)
    }
  }

  return (
    <PageContainer>
      <LoginCard>
        <CardHeader style={{ textAlign: 'center' }}>
          <CardTitle style={{ fontSize: '1.5rem' }}>
            Iniciar Sesión
          </CardTitle>
          <CardDescription>
            Usa tu cuenta de Google para acceder a Fuxion
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* Error */}
          {error && (
            <div style={{
              fontSize: '0.875rem',
              color: '#ef4444',
              background: 'rgba(239, 68, 68, 0.1)',
              padding: '0.75rem',
              borderRadius: '0.375rem',
              marginBottom: '1rem'
            }}>
              {error}
            </div>
          )}

          {/* Botón de Google */}
          <GoogleButton onClick={handleGoogleSignIn} disabled={loading}>
            {loading ? (
              <LoadingSpinner size="sm" />
            ) : (
              <>
                <GoogleIcon>G</GoogleIcon>
                Continuar con Google
              </>
            )}
          </GoogleButton>

          <InfoText>
            Al continuar, aceptas nuestros términos de servicio y política de privacidad.
          </InfoText>

          {/* Enlace a admin */}
          <AdminLink to="/admin">
            ¿Eres administrador? Ingresa aquí
          </AdminLink>
        </CardContent>
      </LoginCard>
    </PageContainer>
  )
}
