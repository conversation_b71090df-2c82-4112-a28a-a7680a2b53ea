{"version": 3, "file": "platform.js", "sourceRoot": "", "sources": ["../../../src/shared/platform.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAYH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,GAAS,EAAgB,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AAE7E,MAAM,CAAC,MAAM,UAAU,GAAwB,CAAC,aAA6C,EAAE,QAAoB,EAAE,EAAE;IACrH,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,QAAQ,GAAG,aAAa,CAAC;QACzB,aAAa,GAAG,SAAS,CAAC;KAC3B;IACD,OAAO,YAAY,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAS,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,MAAW,MAAM,EAAc,EAAE;IAC9D,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;QAAE,OAAO,EAAE,CAAC;KAAE;IAE9C,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;IAE5B,IAAI,SAAS,GAAmC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC;IACpE,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;QACvD,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;KAChF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE,CACrC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEjF,MAAM,WAAW,GAAG,CAAC,GAAW,EAAW,EAAE,CAC3C,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAElC,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,EAAE;IAC7B,mBAAmB;IACnB,IAAI,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC;KACb;IAED,UAAU;IACV,IAAI,aAAa,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrD,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE,CAC/B,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAEhC,MAAM,KAAK,GAAG,CAAC,GAAW,EAAE,EAAE,CAC5B,aAAa,CAAC,GAAG,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AAEpD,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE,CAChC,aAAa,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;AAEtC,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;IACtC,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAC1D,CAAC,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE;IAChC,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC;IAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC;IAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACzC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAExC,OAAO,CAAC,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC;QACvC,CAAC,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE;IAC/B,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC;IAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC;IAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACzC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAExC,OAAO,CACL,MAAM,CAAC,GAAG,CAAC;QACX,eAAe,CAAC,GAAG,CAAC;QACpB,CACE,CAAC,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC;YAClC,CAAC,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,IAAI,CAAC,CAClC,CACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;AAE1C,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE,CAChC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAEjB,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE,CAC/B,SAAS,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAE3C,MAAM,SAAS,GAAG,CAAC,GAAQ,EAAW,EAAE,CACtC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AAE3D,MAAM,iBAAiB,GAAG,CAAC,GAAQ,EAAW,EAAE;IAC9C,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;IACnC,OAAO,CAAC,CAAC,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,QAAQ,CAAC,CAAC;AACjC,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,GAAW,EAAW,EAAE,CAC1C,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAElC,MAAM,KAAK,GAAG,CAAC,GAAW,EAAW,EAAE,CACrC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC,OAAO,IAAK,GAAG,CAAC,SAAiB,CAAC,UAAU,CAAC,CAAC;AAEhG,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,IAAY,EAAW,EAAE,CAClE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAErC,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,KAAa,EAAW,EAAE,CACzD,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AAEhC,MAAM,aAAa,GAAG;IACpB,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,QAAQ;IAClB,KAAK,EAAE,KAAK;IACZ,SAAS,EAAE,SAAS;IACpB,SAAS,EAAE,SAAS;IACpB,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,SAAS;IACpB,WAAW,EAAE,iBAAiB;IAC9B,UAAU,EAAE,UAAU;IACtB,KAAK,EAAE,KAAK;IACZ,QAAQ,EAAE,QAAQ;IAClB,WAAW,EAAE,WAAW;IACxB,SAAS,EAAE,SAAS;IACpB,QAAQ,EAAE,QAAQ;CACnB,CAAC"}