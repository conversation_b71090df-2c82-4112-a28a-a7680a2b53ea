{"version": 3, "file": "plugin.js", "sources": ["esm/definitions.js", "esm/index.js", "esm/web.js"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n/**\n * @since 5.2.0\n */\nexport var Persistence;\n(function (Persistence) {\n    /**\n     * Long term persistence using IndexedDB.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"IndexedDbLocal\"] = \"INDEXED_DB_LOCAL\";\n    /**\n     * No persistence.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"InMemory\"] = \"IN_MEMORY\";\n    /**\n     * Long term persistence using local storage.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"BrowserLocal\"] = \"BROWSER_LOCAL\";\n    /**\n     * Temporary persistence using session storage.\n     *\n     * @since 5.2.0\n     */\n    Persistence[\"BrowserSession\"] = \"BROWSER_SESSION\";\n})(Persistence || (Persistence = {}));\nexport var ProviderId;\n(function (ProviderId) {\n    ProviderId[\"APPLE\"] = \"apple.com\";\n    ProviderId[\"FACEBOOK\"] = \"facebook.com\";\n    ProviderId[\"GAME_CENTER\"] = \"gc.apple.com\";\n    ProviderId[\"GITHUB\"] = \"github.com\";\n    ProviderId[\"GOOGLE\"] = \"google.com\";\n    ProviderId[\"MICROSOFT\"] = \"microsoft.com\";\n    ProviderId[\"PLAY_GAMES\"] = \"playgames.google.com\";\n    ProviderId[\"TWITTER\"] = \"twitter.com\";\n    ProviderId[\"YAHOO\"] = \"yahoo.com\";\n    ProviderId[\"PASSWORD\"] = \"password\";\n    ProviderId[\"PHONE\"] = \"phone\";\n})(ProviderId || (ProviderId = {}));\n//# sourceMappingURL=definitions.js.map", "import { registerPlugin } from '@capacitor/core';\nconst FirebaseAuthentication = registerPlugin('FirebaseAuthentication', {\n    web: () => import('./web').then(m => new m.FirebaseAuthenticationWeb()),\n});\nexport * from './definitions';\nexport { FirebaseAuthentication };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nimport { EmailAuthProvider, FacebookAuthProvider, GithubAuthProvider, GoogleAuthProvider, OAuthCredential, OAuthProvider, RecaptchaVerifier, TwitterAuthProvider, applyActionCode, browserLocalPersistence, browserSessionPersistence, confirmPasswordReset, connectAuthEmulator, createUserWithEmailAndPassword, deleteUser, fetchSignInMethodsForEmail, getAdditionalUserInfo, getAuth, getRedirectResult, inMemoryPersistence, indexedDBLocalPersistence, isSignInWithEmailLink, linkWithCredential, linkWithPhoneNumber, linkWithPopup, linkWithRedirect, reload, revokeAccessToken, sendEmailVerification, sendPasswordResetEmail, sendSignInLinkToEmail, setPersistence, signInAnonymously, signInWithCustomToken, signInWithEmailAndPassword, signInWithEmailLink, signInWithPhoneNumber, signInWithPopup, signInWithRedirect, unlink, updateEmail, updatePassword, updateProfile, verifyBeforeUpdateEmail, } from 'firebase/auth';\nimport { Persistence, ProviderId } from './definitions';\nexport class FirebaseAuthenticationWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.lastConfirmationResult = new Map();\n        const auth = getAuth();\n        auth.onAuthStateChanged(user => this.handleAuthStateChange(user));\n        auth.onIdTokenChanged(user => void this.handleIdTokenChange(user));\n    }\n    async applyActionCode(options) {\n        const auth = getAuth();\n        return applyActionCode(auth, options.oobCode);\n    }\n    async createUserWithEmailAndPassword(options) {\n        const auth = getAuth();\n        const userCredential = await createUserWithEmailAndPassword(auth, options.email, options.password);\n        return this.createSignInResult(userCredential, null);\n    }\n    async confirmPasswordReset(options) {\n        const auth = getAuth();\n        return confirmPasswordReset(auth, options.oobCode, options.newPassword);\n    }\n    async confirmVerificationCode(options) {\n        const { verificationCode, verificationId } = options;\n        const confirmationResult = this.lastConfirmationResult.get(verificationId);\n        if (!confirmationResult) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_CONFIRMATION_RESULT_MISSING);\n        }\n        const userCredential = await confirmationResult.confirm(verificationCode);\n        return this.createSignInResult(userCredential, null);\n    }\n    async deleteUser() {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return deleteUser(currentUser);\n    }\n    async fetchSignInMethodsForEmail(options) {\n        const auth = getAuth();\n        const signInMethods = await fetchSignInMethodsForEmail(auth, options.email);\n        return {\n            signInMethods,\n        };\n    }\n    async getPendingAuthResult() {\n        this.throwNotAvailableError();\n    }\n    async getCurrentUser() {\n        const auth = getAuth();\n        const userResult = this.createUserResult(auth.currentUser);\n        const result = {\n            user: userResult,\n        };\n        return result;\n    }\n    async getIdToken(options) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        const idToken = await auth.currentUser.getIdToken(options === null || options === void 0 ? void 0 : options.forceRefresh);\n        const result = {\n            token: idToken || '',\n        };\n        return result;\n    }\n    async getRedirectResult() {\n        const auth = getAuth();\n        const userCredential = await getRedirectResult(auth);\n        const authCredential = userCredential\n            ? OAuthProvider.credentialFromResult(userCredential)\n            : null;\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async getTenantId() {\n        const auth = getAuth();\n        return {\n            tenantId: auth.tenantId,\n        };\n    }\n    async isSignInWithEmailLink(options) {\n        const auth = getAuth();\n        return {\n            isSignInWithEmailLink: isSignInWithEmailLink(auth, options.emailLink),\n        };\n    }\n    async linkWithApple(options) {\n        const provider = new OAuthProvider(ProviderId.APPLE);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithEmailAndPassword(options) {\n        const authCredential = EmailAuthProvider.credential(options.email, options.password);\n        const userCredential = await this.linkCurrentUserWithCredential(authCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithEmailLink(options) {\n        const authCredential = EmailAuthProvider.credentialWithLink(options.email, options.emailLink);\n        const userCredential = await this.linkCurrentUserWithCredential(authCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithFacebook(options) {\n        const provider = new FacebookAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = FacebookAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithGameCenter() {\n        this.throwNotAvailableError();\n    }\n    async linkWithGithub(options) {\n        const provider = new GithubAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GithubAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithGoogle(options) {\n        const provider = new GoogleAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GoogleAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithMicrosoft(options) {\n        const provider = new OAuthProvider(ProviderId.MICROSOFT);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithOpenIdConnect(options) {\n        const provider = new OAuthProvider(options.providerId);\n        this.applySignInOptions(options, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithPhoneNumber(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        if (!options.phoneNumber) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n        }\n        if (!options.recaptchaVerifier ||\n            !(options.recaptchaVerifier instanceof RecaptchaVerifier)) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING);\n        }\n        try {\n            const confirmationResult = await linkWithPhoneNumber(currentUser, options.phoneNumber, options.recaptchaVerifier);\n            const { verificationId } = confirmationResult;\n            this.lastConfirmationResult.set(verificationId, confirmationResult);\n            const event = {\n                verificationId,\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT, event);\n        }\n        catch (error) {\n            const event = {\n                message: this.getErrorMessage(error),\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT, event);\n        }\n    }\n    async linkWithPlayGames() {\n        this.throwNotAvailableError();\n    }\n    async linkWithTwitter(options) {\n        const provider = new TwitterAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = TwitterAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async linkWithYahoo(options) {\n        const provider = new OAuthProvider(ProviderId.YAHOO);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.linkCurrentUserWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async reload() {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return reload(currentUser);\n    }\n    async revokeAccessToken(options) {\n        const auth = getAuth();\n        return revokeAccessToken(auth, options.token);\n    }\n    async sendEmailVerification(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return sendEmailVerification(currentUser, options === null || options === void 0 ? void 0 : options.actionCodeSettings);\n    }\n    async sendPasswordResetEmail(options) {\n        const auth = getAuth();\n        return sendPasswordResetEmail(auth, options.email, options.actionCodeSettings);\n    }\n    async sendSignInLinkToEmail(options) {\n        const auth = getAuth();\n        return sendSignInLinkToEmail(auth, options.email, options.actionCodeSettings);\n    }\n    async setLanguageCode(options) {\n        const auth = getAuth();\n        auth.languageCode = options.languageCode;\n    }\n    async setPersistence(options) {\n        const auth = getAuth();\n        switch (options.persistence) {\n            case Persistence.BrowserLocal:\n                await setPersistence(auth, browserLocalPersistence);\n                break;\n            case Persistence.BrowserSession:\n                await setPersistence(auth, browserSessionPersistence);\n                break;\n            case Persistence.IndexedDbLocal:\n                await setPersistence(auth, indexedDBLocalPersistence);\n                break;\n            case Persistence.InMemory:\n                await setPersistence(auth, inMemoryPersistence);\n                break;\n        }\n    }\n    async setTenantId(options) {\n        const auth = getAuth();\n        auth.tenantId = options.tenantId;\n    }\n    async signInAnonymously() {\n        const auth = getAuth();\n        const userCredential = await signInAnonymously(auth);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithApple(options) {\n        const provider = new OAuthProvider(ProviderId.APPLE);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithCustomToken(options) {\n        const auth = getAuth();\n        const userCredential = await signInWithCustomToken(auth, options.token);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithEmailAndPassword(options) {\n        const auth = getAuth();\n        const userCredential = await signInWithEmailAndPassword(auth, options.email, options.password);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithEmailLink(options) {\n        const auth = getAuth();\n        const userCredential = await signInWithEmailLink(auth, options.email, options.emailLink);\n        return this.createSignInResult(userCredential, null);\n    }\n    async signInWithFacebook(options) {\n        const provider = new FacebookAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = FacebookAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithGithub(options) {\n        const provider = new GithubAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GithubAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithGoogle(options) {\n        const provider = new GoogleAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = GoogleAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithMicrosoft(options) {\n        const provider = new OAuthProvider(ProviderId.MICROSOFT);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithOpenIdConnect(options) {\n        const provider = new OAuthProvider(options.providerId);\n        this.applySignInOptions(options, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithPhoneNumber(options) {\n        if (!options.phoneNumber) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n        }\n        if (!options.recaptchaVerifier ||\n            !(options.recaptchaVerifier instanceof RecaptchaVerifier)) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING);\n        }\n        const auth = getAuth();\n        try {\n            const confirmationResult = await signInWithPhoneNumber(auth, options.phoneNumber, options.recaptchaVerifier);\n            const { verificationId } = confirmationResult;\n            this.lastConfirmationResult.set(verificationId, confirmationResult);\n            const event = {\n                verificationId,\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT, event);\n        }\n        catch (error) {\n            const event = {\n                message: this.getErrorMessage(error),\n            };\n            this.notifyListeners(FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT, event);\n        }\n    }\n    async signInWithPlayGames() {\n        this.throwNotAvailableError();\n    }\n    async signInWithGameCenter() {\n        this.throwNotAvailableError();\n    }\n    async signInWithTwitter(options) {\n        const provider = new TwitterAuthProvider();\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = TwitterAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signInWithYahoo(options) {\n        const provider = new OAuthProvider(ProviderId.YAHOO);\n        this.applySignInOptions(options || {}, provider);\n        const userCredential = await this.signInWithPopupOrRedirect(provider, options === null || options === void 0 ? void 0 : options.mode);\n        const authCredential = OAuthProvider.credentialFromResult(userCredential);\n        return this.createSignInResult(userCredential, authCredential);\n    }\n    async signOut() {\n        const auth = getAuth();\n        await auth.signOut();\n    }\n    async unlink(options) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        const user = await unlink(auth.currentUser, options.providerId);\n        const userResult = this.createUserResult(user);\n        const result = {\n            user: userResult,\n        };\n        return result;\n    }\n    async updateEmail(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return updateEmail(currentUser, options.newEmail);\n    }\n    async updatePassword(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return updatePassword(currentUser, options.newPassword);\n    }\n    async updateProfile(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return updateProfile(currentUser, {\n            displayName: options.displayName,\n            photoURL: options.photoUrl,\n        });\n    }\n    async useAppLanguage() {\n        const auth = getAuth();\n        auth.useDeviceLanguage();\n    }\n    async useEmulator(options) {\n        const auth = getAuth();\n        const port = options.port || 9099;\n        const scheme = options.scheme || 'http';\n        if (options.host.includes('://')) {\n            connectAuthEmulator(auth, `${options.host}:${port}`);\n        }\n        else {\n            connectAuthEmulator(auth, `${scheme}://${options.host}:${port}`);\n        }\n    }\n    async verifyBeforeUpdateEmail(options) {\n        const auth = getAuth();\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return verifyBeforeUpdateEmail(currentUser, options === null || options === void 0 ? void 0 : options.newEmail, options === null || options === void 0 ? void 0 : options.actionCodeSettings);\n    }\n    handleAuthStateChange(user) {\n        const userResult = this.createUserResult(user);\n        const change = {\n            user: userResult,\n        };\n        this.notifyListeners(FirebaseAuthenticationWeb.AUTH_STATE_CHANGE_EVENT, change, true);\n    }\n    async handleIdTokenChange(user) {\n        if (!user) {\n            return;\n        }\n        const idToken = await user.getIdToken(false);\n        const result = {\n            token: idToken,\n        };\n        this.notifyListeners(FirebaseAuthenticationWeb.ID_TOKEN_CHANGE_EVENT, result, true);\n    }\n    applySignInOptions(options, provider) {\n        if (options.customParameters) {\n            const customParameters = {};\n            options.customParameters.map(parameter => {\n                customParameters[parameter.key] = parameter.value;\n            });\n            provider.setCustomParameters(customParameters);\n        }\n        if (options.scopes) {\n            for (const scope of options.scopes) {\n                provider.addScope(scope);\n            }\n        }\n    }\n    signInWithPopupOrRedirect(provider, mode) {\n        const auth = getAuth();\n        if (mode === 'redirect') {\n            return signInWithRedirect(auth, provider);\n        }\n        else {\n            return signInWithPopup(auth, provider);\n        }\n    }\n    linkCurrentUserWithPopupOrRedirect(provider, mode) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        if (mode === 'redirect') {\n            return linkWithRedirect(auth.currentUser, provider);\n        }\n        else {\n            return linkWithPopup(auth.currentUser, provider);\n        }\n    }\n    linkCurrentUserWithCredential(credential) {\n        const auth = getAuth();\n        if (!auth.currentUser) {\n            throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n        }\n        return linkWithCredential(auth.currentUser, credential);\n    }\n    requestAppTrackingTransparencyPermission() {\n        this.throwNotAvailableError();\n    }\n    checkAppTrackingTransparencyPermission() {\n        this.throwNotAvailableError();\n    }\n    createSignInResult(userCredential, authCredential) {\n        const userResult = this.createUserResult((userCredential === null || userCredential === void 0 ? void 0 : userCredential.user) || null);\n        const credentialResult = this.createCredentialResult(authCredential);\n        const additionalUserInfoResult = this.createAdditionalUserInfoResult(userCredential);\n        const result = {\n            user: userResult,\n            credential: credentialResult,\n            additionalUserInfo: additionalUserInfoResult,\n        };\n        return result;\n    }\n    createCredentialResult(credential) {\n        if (!credential) {\n            return null;\n        }\n        const result = {\n            providerId: credential.providerId,\n        };\n        if (credential instanceof OAuthCredential) {\n            result.accessToken = credential.accessToken;\n            result.idToken = credential.idToken;\n            result.secret = credential.secret;\n        }\n        return result;\n    }\n    createUserResult(user) {\n        if (!user) {\n            return null;\n        }\n        const result = {\n            displayName: user.displayName,\n            email: user.email,\n            emailVerified: user.emailVerified,\n            isAnonymous: user.isAnonymous,\n            metadata: this.createUserMetadataResult(user.metadata),\n            phoneNumber: user.phoneNumber,\n            photoUrl: user.photoURL,\n            providerData: this.createUserProviderDataResult(user.providerData),\n            providerId: user.providerId,\n            tenantId: user.tenantId,\n            uid: user.uid,\n        };\n        return result;\n    }\n    createUserMetadataResult(metadata) {\n        const result = {};\n        if (metadata.creationTime) {\n            result.creationTime = Date.parse(metadata.creationTime);\n        }\n        if (metadata.lastSignInTime) {\n            result.lastSignInTime = Date.parse(metadata.lastSignInTime);\n        }\n        return result;\n    }\n    createUserProviderDataResult(providerData) {\n        return providerData.map(data => ({\n            displayName: data.displayName,\n            email: data.email,\n            phoneNumber: data.phoneNumber,\n            photoUrl: data.photoURL,\n            providerId: data.providerId,\n            uid: data.uid,\n        }));\n    }\n    createAdditionalUserInfoResult(credential) {\n        if (!credential) {\n            return null;\n        }\n        const additionalUserInfo = getAdditionalUserInfo(credential);\n        if (!additionalUserInfo) {\n            return null;\n        }\n        const { isNewUser, profile, providerId, username } = additionalUserInfo;\n        const result = {\n            isNewUser,\n        };\n        if (providerId !== null) {\n            result.providerId = providerId;\n        }\n        if (profile !== null) {\n            result.profile = profile;\n        }\n        if (username !== null && username !== undefined) {\n            result.username = username;\n        }\n        return result;\n    }\n    getErrorMessage(error) {\n        if (error instanceof Object &&\n            'message' in error &&\n            typeof error['message'] === 'string') {\n            return error['message'];\n        }\n        return JSON.stringify(error);\n    }\n    throwNotAvailableError() {\n        throw new Error('Not available on web.');\n    }\n}\nFirebaseAuthenticationWeb.AUTH_STATE_CHANGE_EVENT = 'authStateChange';\nFirebaseAuthenticationWeb.ID_TOKEN_CHANGE_EVENT = 'idTokenChange';\nFirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT = 'phoneCodeSent';\nFirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT = 'phoneVerificationFailed';\nFirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN = 'No user is signed in.';\nFirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING = 'phoneNumber must be provided.';\nFirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING = 'recaptchaVerifier must be provided and must be an instance of RecaptchaVerifier.';\nFirebaseAuthenticationWeb.ERROR_CONFIRMATION_RESULT_MISSING = 'No confirmation result with this verification id was found.';\n//# sourceMappingURL=web.js.map"], "names": ["Persistence", "ProviderId", "registerPlugin", "WebPlugin", "auth", "getAuth", "applyActionCode", "createUserWithEmailAndPassword", "confirmPasswordReset", "deleteUser", "fetchSignInMethodsForEmail", "getRedirectResult", "OAuth<PERSON><PERSON><PERSON>", "isSignInWithEmailLink", "EmailAuthProvider", "FacebookAuthProvider", "GithubAuth<PERSON>rovider", "GoogleAuthProvider", "RecaptchaVerifier", "linkWithPhoneNumber", "TwitterAuthProvider", "reload", "revokeAccessToken", "sendEmailVerification", "sendPasswordResetEmail", "sendSignInLinkToEmail", "setPersistence", "browserLocalPersistence", "browserSessionPersistence", "indexedDBLocalPersistence", "inMemoryPersistence", "signInAnonymously", "signInWithCustomToken", "signInWithEmailAndPassword", "signInWithEmailLink", "signInWithPhoneNumber", "unlink", "updateEmail", "updatePassword", "updateProfile", "connectAuthEmulator", "verifyBeforeUpdateEmail", "signInWithRedirect", "signInWithPopup", "linkWithRedirect", "linkWithPopup", "linkWithCredential", "OAuthCredential", "getAdditionalUserInfo"], "mappings": ";;;IAAA;IACA;IACA;IACA;AACWA;IACX,CAAC,UAAU,WAAW,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,gBAAgB,CAAC,GAAG,kBAAkB;IACtD;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,WAAW;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,cAAc,CAAC,GAAG,eAAe;IACjD;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,gBAAgB,CAAC,GAAG,iBAAiB;IACrD,CAAC,EAAEA,mBAAW,KAAKA,mBAAW,GAAG,EAAE,CAAC,CAAC;AAC1BC;IACX,CAAC,UAAU,UAAU,EAAE;IACvB,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;IACrC,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,cAAc;IAC3C,IAAI,UAAU,CAAC,aAAa,CAAC,GAAG,cAAc;IAC9C,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,YAAY;IACvC,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,YAAY;IACvC,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,eAAe;IAC7C,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,sBAAsB;IACrD,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,aAAa;IACzC,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;IACrC,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU;IACvC,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;IACjC,CAAC,EAAEA,kBAAU,KAAKA,kBAAU,GAAG,EAAE,CAAC,CAAC;;AC3C9B,UAAC,sBAAsB,GAAGC,mBAAc,CAAC,wBAAwB,EAAE;IACxE,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,yBAAyB,EAAE,CAAC;IAC3E,CAAC;;ICAM,MAAM,yBAAyB,SAASC,cAAS,CAAC;IACzD,IAAI,WAAW,GAAG;IAClB,QAAQ,KAAK,EAAE;IACf,QAAQ,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE;IAC/C,QAAQ,MAAMC,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQD,MAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACzE,QAAQA,MAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC1E;IACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;IACnC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAOC,oBAAe,CAACF,MAAI,EAAE,OAAO,CAAC,OAAO,CAAC;IACrD;IACA,IAAI,MAAM,8BAA8B,CAAC,OAAO,EAAE;IAClD,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,cAAc,GAAG,MAAME,mCAA8B,CAACH,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;IAC1G,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D;IACA,IAAI,MAAM,oBAAoB,CAAC,OAAO,EAAE;IACxC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAOG,yBAAoB,CAACJ,MAAI,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC;IAC/E;IACA,IAAI,MAAM,uBAAuB,CAAC,OAAO,EAAE;IAC3C,QAAQ,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,OAAO;IAC5D,QAAQ,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC;IAClF,QAAQ,IAAI,CAAC,kBAAkB,EAAE;IACjC,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,iCAAiC,CAAC;IACxF;IACA,QAAQ,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D;IACA,IAAI,MAAM,UAAU,GAAG;IACvB,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOK,eAAU,CAAC,WAAW,CAAC;IACtC;IACA,IAAI,MAAM,0BAA0B,CAAC,OAAO,EAAE;IAC9C,QAAQ,MAAML,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,aAAa,GAAG,MAAMK,+BAA0B,CAACN,MAAI,EAAE,OAAO,CAAC,KAAK,CAAC;IACnF,QAAQ,OAAO;IACf,YAAY,aAAa;IACzB,SAAS;IACT;IACA,IAAI,MAAM,oBAAoB,GAAG;IACjC,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,MAAM,cAAc,GAAG;IAC3B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAACD,MAAI,CAAC,WAAW,CAAC;IAClE,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,IAAI,EAAE,UAAU;IAC5B,SAAS;IACT,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,MAAM,UAAU,CAAC,OAAO,EAAE;IAC9B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;IAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,MAAM,OAAO,GAAG,MAAMA,MAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,YAAY,CAAC;IACjI,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,KAAK,EAAE,OAAO,IAAI,EAAE;IAChC,SAAS;IACT,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,MAAM,iBAAiB,GAAG;IAC9B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,cAAc,GAAG,MAAMM,sBAAiB,CAACP,MAAI,CAAC;IAC5D,QAAQ,MAAM,cAAc,GAAG;IAC/B,cAAcQ,kBAAa,CAAC,oBAAoB,CAAC,cAAc;IAC/D,cAAc,IAAI;IAClB,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,WAAW,GAAG;IACxB,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAO;IACf,YAAY,QAAQ,EAAED,MAAI,CAAC,QAAQ;IACnC,SAAS;IACT;IACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;IACzC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAO;IACf,YAAY,qBAAqB,EAAEQ,0BAAqB,CAACT,MAAI,EAAE,OAAO,CAAC,SAAS,CAAC;IACjF,SAAS;IACT;IACA,IAAI,MAAM,aAAa,CAAC,OAAO,EAAE;IACjC,QAAQ,MAAM,QAAQ,GAAG,IAAIQ,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;IAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,wBAAwB,CAAC,OAAO,EAAE;IAC5C,QAAQ,MAAM,cAAc,GAAGE,sBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;IAC5F,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC;IACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;IACrC,QAAQ,MAAM,cAAc,GAAGA,sBAAiB,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC;IACrG,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC;IACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,gBAAgB,CAAC,OAAO,EAAE;IACpC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,yBAAoB,EAAE;IACnD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGA,yBAAoB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACxF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,kBAAkB,GAAG;IAC/B,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;IAClC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;IACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;IAClC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;IACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;IACrC,QAAQ,MAAM,QAAQ,GAAG,IAAIL,kBAAa,CAACX,kBAAU,CAAC,SAAS,CAAC;IAChE,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;IACzC,QAAQ,MAAM,QAAQ,GAAG,IAAIA,kBAAa,CAAC,OAAO,CAAC,UAAU,CAAC;IAC9D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC;IAClD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC;IACpG,QAAQ,MAAM,cAAc,GAAGA,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,mBAAmB,CAAC,OAAO,EAAE;IACvC,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;IAClC,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,0BAA0B,CAAC;IACjF;IACA,QAAQ,IAAI,CAAC,OAAO,CAAC,iBAAiB;IACtC,YAAY,EAAE,OAAO,CAAC,iBAAiB,YAAYc,sBAAiB,CAAC,EAAE;IACvE,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,gCAAgC,CAAC;IACvF;IACA,QAAQ,IAAI;IACZ,YAAY,MAAM,kBAAkB,GAAG,MAAMC,wBAAmB,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,iBAAiB,CAAC;IAC7H,YAAY,MAAM,EAAE,cAAc,EAAE,GAAG,kBAAkB;IACzD,YAAY,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC/E,YAAY,MAAM,KAAK,GAAG;IAC1B,gBAAgB,cAAc;IAC9B,aAAa;IACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,KAAK,CAAC;IACxF;IACA,QAAQ,OAAO,KAAK,EAAE;IACtB,YAAY,MAAM,KAAK,GAAG;IAC1B,gBAAgB,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;IACpD,aAAa;IACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,+BAA+B,EAAE,KAAK,CAAC;IAClG;IACA;IACA,IAAI,MAAM,iBAAiB,GAAG;IAC9B,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;IACnC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,wBAAmB,EAAE;IAClD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGA,wBAAmB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,aAAa,CAAC,OAAO,EAAE;IACjC,QAAQ,MAAM,QAAQ,GAAG,IAAIR,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;IAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACtJ,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,MAAM,GAAG;IACnB,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOiB,WAAM,CAAC,WAAW,CAAC;IAClC;IACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;IACrC,QAAQ,MAAMjB,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAOiB,sBAAiB,CAAClB,MAAI,EAAE,OAAO,CAAC,KAAK,CAAC;IACrD;IACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;IACzC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOmB,0BAAqB,CAAC,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAC/H;IACA,IAAI,MAAM,sBAAsB,CAAC,OAAO,EAAE;IAC1C,QAAQ,MAAMnB,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAOmB,2BAAsB,CAACpB,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,kBAAkB,CAAC;IACtF;IACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;IACzC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,OAAOoB,0BAAqB,CAACrB,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,kBAAkB,CAAC;IACrF;IACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;IACnC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQD,MAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY;IAChD;IACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;IAClC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,QAAQ,OAAO,CAAC,WAAW;IACnC,YAAY,KAAKL,mBAAW,CAAC,YAAY;IACzC,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAEuB,4BAAuB,CAAC;IACnE,gBAAgB;IAChB,YAAY,KAAK3B,mBAAW,CAAC,cAAc;IAC3C,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAEwB,8BAAyB,CAAC;IACrE,gBAAgB;IAChB,YAAY,KAAK5B,mBAAW,CAAC,cAAc;IAC3C,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAEyB,8BAAyB,CAAC;IACrE,gBAAgB;IAChB,YAAY,KAAK7B,mBAAW,CAAC,QAAQ;IACrC,gBAAgB,MAAM0B,mBAAc,CAACtB,MAAI,EAAE0B,wBAAmB,CAAC;IAC/D,gBAAgB;IAChB;IACA;IACA,IAAI,MAAM,WAAW,CAAC,OAAO,EAAE;IAC/B,QAAQ,MAAM1B,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQD,MAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ;IACxC;IACA,IAAI,MAAM,iBAAiB,GAAG;IAC9B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM0B,sBAAiB,CAAC3B,MAAI,CAAC;IAC5D,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D;IACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;IACnC,QAAQ,MAAM,QAAQ,GAAG,IAAIQ,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;IAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;IACzC,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM2B,0BAAqB,CAAC5B,MAAI,EAAE,OAAO,CAAC,KAAK,CAAC;IAC/E,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D;IACA,IAAI,MAAM,0BAA0B,CAAC,OAAO,EAAE;IAC9C,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM4B,+BAA0B,CAAC7B,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;IACtG,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D;IACA,IAAI,MAAM,mBAAmB,CAAC,OAAO,EAAE;IACvC,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,cAAc,GAAG,MAAM6B,wBAAmB,CAAC9B,MAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC;IAChG,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D;IACA,IAAI,MAAM,kBAAkB,CAAC,OAAO,EAAE;IACtC,QAAQ,MAAM,QAAQ,GAAG,IAAIW,yBAAoB,EAAE;IACnD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGA,yBAAoB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACxF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,gBAAgB,CAAC,OAAO,EAAE;IACpC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;IACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,gBAAgB,CAAC,OAAO,EAAE;IACpC,QAAQ,MAAM,QAAQ,GAAG,IAAIC,uBAAkB,EAAE;IACjD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGA,uBAAkB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACtF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,mBAAmB,CAAC,OAAO,EAAE;IACvC,QAAQ,MAAM,QAAQ,GAAG,IAAIL,kBAAa,CAACX,kBAAU,CAAC,SAAS,CAAC;IAChE,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,uBAAuB,CAAC,OAAO,EAAE;IAC3C,QAAQ,MAAM,QAAQ,GAAG,IAAIA,kBAAa,CAAC,OAAO,CAAC,UAAU,CAAC;IAC9D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC;IAClD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC;IAC3F,QAAQ,MAAM,cAAc,GAAGA,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,qBAAqB,CAAC,OAAO,EAAE;IACzC,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;IAClC,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,0BAA0B,CAAC;IACjF;IACA,QAAQ,IAAI,CAAC,OAAO,CAAC,iBAAiB;IACtC,YAAY,EAAE,OAAO,CAAC,iBAAiB,YAAYM,sBAAiB,CAAC,EAAE;IACvE,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,gCAAgC,CAAC;IACvF;IACA,QAAQ,MAAMd,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,IAAI;IACZ,YAAY,MAAM,kBAAkB,GAAG,MAAM8B,0BAAqB,CAAC/B,MAAI,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,iBAAiB,CAAC;IACxH,YAAY,MAAM,EAAE,cAAc,EAAE,GAAG,kBAAkB;IACzD,YAAY,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC/E,YAAY,MAAM,KAAK,GAAG;IAC1B,gBAAgB,cAAc;IAC9B,aAAa;IACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,KAAK,CAAC;IACxF;IACA,QAAQ,OAAO,KAAK,EAAE;IACtB,YAAY,MAAM,KAAK,GAAG;IAC1B,gBAAgB,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;IACpD,aAAa;IACb,YAAY,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,+BAA+B,EAAE,KAAK,CAAC;IAClG;IACA;IACA,IAAI,MAAM,mBAAmB,GAAG;IAChC,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,MAAM,oBAAoB,GAAG;IACjC,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;IACrC,QAAQ,MAAM,QAAQ,GAAG,IAAIgB,wBAAmB,EAAE;IAClD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGA,wBAAmB,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACvF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,eAAe,CAAC,OAAO,EAAE;IACnC,QAAQ,MAAM,QAAQ,GAAG,IAAIR,kBAAa,CAACX,kBAAU,CAAC,KAAK,CAAC;IAC5D,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;IACxD,QAAQ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7I,QAAQ,MAAM,cAAc,GAAGW,kBAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC;IACjF,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,cAAc,CAAC;IACtE;IACA,IAAI,MAAM,OAAO,GAAG;IACpB,QAAQ,MAAMR,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAMD,MAAI,CAAC,OAAO,EAAE;IAC5B;IACA,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;IAC1B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;IAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,MAAM,IAAI,GAAG,MAAMgC,WAAM,CAAChC,MAAI,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC;IACvE,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACtD,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,IAAI,EAAE,UAAU;IAC5B,SAAS;IACT,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,MAAM,WAAW,CAAC,OAAO,EAAE;IAC/B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOiC,gBAAW,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC;IACzD;IACA,IAAI,MAAM,cAAc,CAAC,OAAO,EAAE;IAClC,QAAQ,MAAMjC,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOkC,mBAAc,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC;IAC/D;IACA,IAAI,MAAM,aAAa,CAAC,OAAO,EAAE;IACjC,QAAQ,MAAMlC,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOmC,kBAAa,CAAC,WAAW,EAAE;IAC1C,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW;IAC5C,YAAY,QAAQ,EAAE,OAAO,CAAC,QAAQ;IACtC,SAAS,CAAC;IACV;IACA,IAAI,MAAM,cAAc,GAAG;IAC3B,QAAQ,MAAMnC,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQD,MAAI,CAAC,iBAAiB,EAAE;IAChC;IACA,IAAI,MAAM,WAAW,CAAC,OAAO,EAAE;IAC/B,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI;IACzC,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM;IAC/C,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC1C,YAAYmC,wBAAmB,CAACpC,MAAI,EAAE,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAChE;IACA,aAAa;IACb,YAAYoC,wBAAmB,CAACpC,MAAI,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5E;IACA;IACA,IAAI,MAAM,uBAAuB,CAAC,OAAO,EAAE;IAC3C,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,MAAM,WAAW,GAAGD,MAAI,CAAC,WAAW;IAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAOqC,4BAAuB,CAAC,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAM,GAAG,SAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACrM;IACA,IAAI,qBAAqB,CAAC,IAAI,EAAE;IAChC,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACtD,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,IAAI,EAAE,UAAU;IAC5B,SAAS;IACT,QAAQ,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,MAAM,EAAE,IAAI,CAAC;IAC7F;IACA,IAAI,MAAM,mBAAmB,CAAC,IAAI,EAAE;IACpC,QAAQ,IAAI,CAAC,IAAI,EAAE;IACnB,YAAY;IACZ;IACA,QAAQ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;IACpD,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,KAAK,EAAE,OAAO;IAC1B,SAAS;IACT,QAAQ,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC;IAC3F;IACA,IAAI,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE;IAC1C,QAAQ,IAAI,OAAO,CAAC,gBAAgB,EAAE;IACtC,YAAY,MAAM,gBAAgB,GAAG,EAAE;IACvC,YAAY,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,IAAI;IACtD,gBAAgB,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK;IACjE,aAAa,CAAC;IACd,YAAY,QAAQ,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;IAC1D;IACA,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;IAC5B,YAAY,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;IAChD,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IACxC;IACA;IACA;IACA,IAAI,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE;IAC9C,QAAQ,MAAMrC,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;IACjC,YAAY,OAAOqC,uBAAkB,CAACtC,MAAI,EAAE,QAAQ,CAAC;IACrD;IACA,aAAa;IACb,YAAY,OAAOuC,oBAAe,CAACvC,MAAI,EAAE,QAAQ,CAAC;IAClD;IACA;IACA,IAAI,kCAAkC,CAAC,QAAQ,EAAE,IAAI,EAAE;IACvD,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;IAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;IACjC,YAAY,OAAOwC,qBAAgB,CAACxC,MAAI,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC/D;IACA,aAAa;IACb,YAAY,OAAOyC,kBAAa,CAACzC,MAAI,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC5D;IACA;IACA,IAAI,6BAA6B,CAAC,UAAU,EAAE;IAC9C,QAAQ,MAAMA,MAAI,GAAGC,YAAO,EAAE;IAC9B,QAAQ,IAAI,CAACD,MAAI,CAAC,WAAW,EAAE;IAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;IAC9E;IACA,QAAQ,OAAO0C,uBAAkB,CAAC1C,MAAI,CAAC,WAAW,EAAE,UAAU,CAAC;IAC/D;IACA,IAAI,wCAAwC,GAAG;IAC/C,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,sCAAsC,GAAG;IAC7C,QAAQ,IAAI,CAAC,sBAAsB,EAAE;IACrC;IACA,IAAI,kBAAkB,CAAC,cAAc,EAAE,cAAc,EAAE;IACvD,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,cAAc,KAAK,IAAI,IAAI,cAAc,KAAK,SAAM,GAAG,SAAM,GAAG,cAAc,CAAC,IAAI,KAAK,IAAI,CAAC;IAC/I,QAAQ,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;IAC5E,QAAQ,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,CAAC,cAAc,CAAC;IAC5F,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,IAAI,EAAE,UAAU;IAC5B,YAAY,UAAU,EAAE,gBAAgB;IACxC,YAAY,kBAAkB,EAAE,wBAAwB;IACxD,SAAS;IACT,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,sBAAsB,CAAC,UAAU,EAAE;IACvC,QAAQ,IAAI,CAAC,UAAU,EAAE;IACzB,YAAY,OAAO,IAAI;IACvB;IACA,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,UAAU,EAAE,UAAU,CAAC,UAAU;IAC7C,SAAS;IACT,QAAQ,IAAI,UAAU,YAAY2C,oBAAe,EAAE;IACnD,YAAY,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW;IACvD,YAAY,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO;IAC/C,YAAY,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM;IAC7C;IACA,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,gBAAgB,CAAC,IAAI,EAAE;IAC3B,QAAQ,IAAI,CAAC,IAAI,EAAE;IACnB,YAAY,OAAO,IAAI;IACvB;IACA,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;IACzC,YAAY,KAAK,EAAE,IAAI,CAAC,KAAK;IAC7B,YAAY,aAAa,EAAE,IAAI,CAAC,aAAa;IAC7C,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;IACzC,YAAY,QAAQ,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC;IAClE,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;IACzC,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;IACnC,YAAY,YAAY,EAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC;IAC9E,YAAY,UAAU,EAAE,IAAI,CAAC,UAAU;IACvC,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;IACnC,YAAY,GAAG,EAAE,IAAI,CAAC,GAAG;IACzB,SAAS;IACT,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,wBAAwB,CAAC,QAAQ,EAAE;IACvC,QAAQ,MAAM,MAAM,GAAG,EAAE;IACzB,QAAQ,IAAI,QAAQ,CAAC,YAAY,EAAE;IACnC,YAAY,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;IACnE;IACA,QAAQ,IAAI,QAAQ,CAAC,cAAc,EAAE;IACrC,YAAY,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC;IACvE;IACA,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,4BAA4B,CAAC,YAAY,EAAE;IAC/C,QAAQ,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,KAAK;IACzC,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;IACzC,YAAY,KAAK,EAAE,IAAI,CAAC,KAAK;IAC7B,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;IACzC,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;IACnC,YAAY,UAAU,EAAE,IAAI,CAAC,UAAU;IACvC,YAAY,GAAG,EAAE,IAAI,CAAC,GAAG;IACzB,SAAS,CAAC,CAAC;IACX;IACA,IAAI,8BAA8B,CAAC,UAAU,EAAE;IAC/C,QAAQ,IAAI,CAAC,UAAU,EAAE;IACzB,YAAY,OAAO,IAAI;IACvB;IACA,QAAQ,MAAM,kBAAkB,GAAGC,0BAAqB,CAAC,UAAU,CAAC;IACpE,QAAQ,IAAI,CAAC,kBAAkB,EAAE;IACjC,YAAY,OAAO,IAAI;IACvB;IACA,QAAQ,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,kBAAkB;IAC/E,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,SAAS;IACrB,SAAS;IACT,QAAQ,IAAI,UAAU,KAAK,IAAI,EAAE;IACjC,YAAY,MAAM,CAAC,UAAU,GAAG,UAAU;IAC1C;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE;IAC9B,YAAY,MAAM,CAAC,OAAO,GAAG,OAAO;IACpC;IACA,QAAQ,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;IACzD,YAAY,MAAM,CAAC,QAAQ,GAAG,QAAQ;IACtC;IACA,QAAQ,OAAO,MAAM;IACrB;IACA,IAAI,eAAe,CAAC,KAAK,EAAE;IAC3B,QAAQ,IAAI,KAAK,YAAY,MAAM;IACnC,YAAY,SAAS,IAAI,KAAK;IAC9B,YAAY,OAAO,KAAK,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE;IAClD,YAAY,OAAO,KAAK,CAAC,SAAS,CAAC;IACnC;IACA,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IACpC;IACA,IAAI,sBAAsB,GAAG;IAC7B,QAAQ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC;IAChD;IACA;IACA,yBAAyB,CAAC,uBAAuB,GAAG,iBAAiB;IACrE,yBAAyB,CAAC,qBAAqB,GAAG,eAAe;IACjE,yBAAyB,CAAC,qBAAqB,GAAG,eAAe;IACjE,yBAAyB,CAAC,+BAA+B,GAAG,yBAAyB;IACrF,yBAAyB,CAAC,uBAAuB,GAAG,uBAAuB;IAC3E,yBAAyB,CAAC,0BAA0B,GAAG,+BAA+B;IACtF,yBAAyB,CAAC,gCAAgC,GAAG,kFAAkF;IAC/I,yBAAyB,CAAC,iCAAiC,GAAG,6DAA6D;;;;;;;;;;;;;;;"}