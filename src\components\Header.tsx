import React, { useState, useRef, useEffect } from 'react'
import styled from 'styled-components'
import { Link, useNavigate } from 'react-router-dom'
import { Button } from './ui/button'
import { ShoppingCart, User, LogOut, Settings, ChevronDown } from 'lucide-react'
import type { User as UserType, ShoppingCart as ShoppingCartType } from '../../types'
import { AuthService } from '../services/authService'
import { LocalStorageService } from '../services/localStorageService'
import { container, flexBetween, transition, media } from '../styles/utils'

const HeaderContainer = styled.header`
  position: sticky;
  top: 0;
  z-index: 50;
  width: 100%;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
`

const HeaderContent = styled.div`
  ${container}
  height: 4rem;
  ${flexBetween}
`

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  text-decoration: none;
  color: inherit;
`

const LogoIcon = styled.div`
  width: 2rem;
  height: 2rem;
  background: ${({ theme }) => theme.colors.primary[500]};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
`

const LogoText = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  font-size: ${({ theme }) => theme.fontSizes.xl};
  color: ${({ theme }) => theme.colors.primary[500]};
`

const Navigation = styled.nav`
  display: none;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[6]};

  ${media.md`
    display: flex;
  `}
`

const NavLink = styled(Link)`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  text-decoration: none;
  ${transition('color')}

  &:hover {
    color: ${({ theme }) => theme.colors.primary[500]};
  }
`

const Actions = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`

const CartButton = styled(Button)`
  position: relative;
`

const CartBadge = styled.span`
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: ${({ theme }) => theme.colors.primary[500]};
  color: ${({ theme }) => theme.colors.white};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
`



const UserMenuContainer = styled.div`
  position: relative;
`

const UserMenuButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
`

const UserMenuDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.white};
  border: 1px solid ${({ theme }) => theme.colors.border.default};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  min-width: 12rem;
  z-index: 50;
  opacity: ${({ $isOpen }) => ($isOpen ? 1 : 0)};
  visibility: ${({ $isOpen }) => ($isOpen ? 'visible' : 'hidden')};
  transform: ${({ $isOpen }) => ($isOpen ? 'translateY(0)' : 'translateY(-10px)')};
  transition: all 200ms ease-in-out;
`

const MenuSection = styled.div`
  padding: ${({ theme }) => theme.spacing[2]};

  &:not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  }
`

const MenuTitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.muted};
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const MenuItem = styled.button`
  width: 100%;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  background: none;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  text-align: left;
  ${transition()}

  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
  }

  &.danger {
    color: ${({ theme }) => theme.colors.error};

    &:hover {
      background: rgba(239, 68, 68, 0.1);
    }
  }
`

const MenuLink = styled(Link)`
  width: 100%;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.primary};
  text-decoration: none;
  ${transition()}

  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
  }
`

interface HeaderProps {
  user: UserType | null
  cart: ShoppingCartType
  onLogout: () => void
}

/**
 * Componente de encabezado de la aplicación
 */
export const Header: React.FC<HeaderProps> = ({ user, cart, onLogout }) => {
  const navigate = useNavigate()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // Cerrar menú al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = async () => {
    try {
      await AuthService.logout()
      LocalStorageService.clearAll()
      onLogout()
      setIsMenuOpen(false)
      navigate('/')
    } catch (error) {
      console.error('Error cerrando sesión:', error)
    }
  }

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const cartItemsCount = cart.items.reduce((total, item) => total + item.quantity, 0)

  return (
    <HeaderContainer>
      <HeaderContent>
        {/* Logo */}
        <Logo to="/">
          <LogoIcon>
            <span style={{ color: 'white', fontWeight: 'bold', fontSize: '0.875rem' }}>X</span>
          </LogoIcon>
          <LogoText>AriFuxion</LogoText>
        </Logo>

        {/* Navegación central */}
        <Navigation>
          <NavLink to="/">
            Productos
          </NavLink>
          {user && (
            <>
              <NavLink to="/profile">
                Mi Perfil
              </NavLink>
              <NavLink to="/addresses">
                Direcciones
              </NavLink>
            </>
          )}
        </Navigation>

        {/* Acciones del usuario */}
        <Actions>
          {/* Carrito */}
          <CartButton variant="ghost" size="icon" asChild>
            <Link to="/cart">
              <ShoppingCart size={20} />
              {cartItemsCount > 0 && (
                <CartBadge>
                  {cartItemsCount}
                </CartBadge>
              )}
            </Link>
          </CartButton>

          {user ? (
            <>
              {/* Menú de usuario */}
              <UserMenuContainer ref={menuRef}>
                <UserMenuButton
                  variant="ghost"
                  onClick={toggleMenu}
                >
                  <User size={20} />
                  <span style={{ display: 'none' }}>
                    {user.displayName || user.email}
                  </span>
                  <ChevronDown size={16} />
                </UserMenuButton>

                <UserMenuDropdown $isOpen={isMenuOpen}>
                  <MenuSection>
                    <MenuTitle>Mi Cuenta</MenuTitle>
                    <MenuLink to="/profile" onClick={() => setIsMenuOpen(false)}>
                      <User size={16} />
                      Mi Perfil
                    </MenuLink>
                    <MenuLink to="/addresses" onClick={() => setIsMenuOpen(false)}>
                      <Settings size={16} />
                      Direcciones
                    </MenuLink>
                  </MenuSection>

                  <MenuSection>
                    <MenuTitle>Administración</MenuTitle>
                    <MenuLink to="/admin" onClick={() => setIsMenuOpen(false)}>
                      <Settings size={16} />
                      Panel Admin
                    </MenuLink>
                  </MenuSection>

                  <MenuSection>
                    <MenuItem onClick={handleLogout} className="danger">
                      <LogOut size={16} />
                      Cerrar Sesión
                    </MenuItem>
                  </MenuSection>
                </UserMenuDropdown>
              </UserMenuContainer>
            </>
          ) : (
            <>
              {/* Usuario no logueado */}
              <Button variant="ghost" asChild>
                <Link to="/login">Iniciar Sesión</Link>
              </Button>
            </>
          )}
        </Actions>
      </HeaderContent>
    </HeaderContainer>
  )
}
