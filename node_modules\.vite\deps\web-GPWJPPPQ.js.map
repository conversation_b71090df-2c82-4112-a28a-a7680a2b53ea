{"version": 3, "sources": ["../../@capacitor/browser/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type { BrowserPlugin, OpenOptions } from './definitions';\n\nexport class <PERSON><PERSON>er<PERSON>eb extends WebPlugin implements BrowserPlugin {\n  _lastWindow: Window | null;\n\n  constructor() {\n    super();\n    this._lastWindow = null;\n  }\n\n  async open(options: OpenOptions): Promise<void> {\n    this._lastWindow = window.open(options.url, options.windowName || '_blank');\n  }\n\n  async close(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this._lastWindow != null) {\n        this._lastWindow.close();\n        this._lastWindow = null;\n        resolve();\n      } else {\n        reject('No active window to close!');\n      }\n    });\n  }\n}\n\nconst Browser = new BrowserWeb();\n\nexport { Browser };\n"], "mappings": ";;;;;;AAIM,IAAO,aAAP,cAA0B,UAAS;EAGvC,cAAA;AACE,UAAK;AACL,SAAK,cAAc;EACrB;EAEA,MAAM,KAAK,SAAoB;AAC7B,SAAK,cAAc,OAAO,KAAK,QAAQ,KAAK,QAAQ,cAAc,QAAQ;EAC5E;EAEA,MAAM,QAAK;AACT,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,UAAI,KAAK,eAAe,MAAM;AAC5B,aAAK,YAAY,MAAK;AACtB,aAAK,cAAc;AACnB,gBAAO;aACF;AACL,eAAO,4BAA4B;;IAEvC,CAAC;EACH;;AAGF,IAAM,UAAU,IAAI,WAAU;", "names": []}