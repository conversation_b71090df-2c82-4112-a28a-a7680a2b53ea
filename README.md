# Fuxion - E-commerce de Productos Naturales de Fitness

Una aplicación moderna de e-commerce construida con React, TypeScript, Styled Components, Shadcn UI, Firebase y Stripe, optimizada para dispositivos móviles con Capacitor.

## 🚀 Características

- **Frontend Moderno**: React 18 + TypeScript + Styled Components
- **UI Components**: Shadcn UI + Radix UI para accesibilidad
- **Backend**: Firebase Auth + Firestore
- **Autenticación**: Google OAuth exclusivo con Capacitor Firebase Authentication
- **Pagos**: Integración completa con Stripe
- **Móvil**: Capacitor para iOS y Android
- **Estado Global**: Context API con persistencia en localStorage
- **Routing**: React Router con protección de rutas
- **Responsive**: Diseño adaptable para todos los dispositivos

## 📱 Funcionalidades

### Para Usuarios
- ✅ Navegación de productos con filtros y búsqueda
- ✅ Carrito de compras persistente
- ✅ **Autenticación con Google OAuth** (web y móvil)
- ✅ Gestión de direcciones de envío
- ✅ Checkout con Stripe
- ✅ Perfil de usuario

### Para Administradores
- ✅ **Login con Google OAuth** y verificación de permisos
- ✅ Dashboard con estadísticas y métricas
- ✅ Gestión completa de productos (CRUD)
- ✅ Gestión de órdenes y estados

## 🛠️ Tecnologías

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Styled Components, Shadcn UI, Radix UI
- **Backend**: Firebase (Auth + Firestore)
- **Autenticación**: @capacitor-firebase/authentication
- **Pagos**: Stripe
- **Móvil**: Capacitor
- **Icons**: Lucide React
- **Routing**: React Router DOM

## 📦 Instalación

1. **Instalar dependencias**
```bash
npm install
```

2. **Configurar Firebase**
   - Crear proyecto en [Firebase Console](https://console.firebase.google.com)
   - Habilitar Authentication con Google
   - Crear base de datos Firestore
   - Copiar configuración a `src/lib/firebase.ts`

3. **Configurar Google OAuth**
   - Ver guía detallada en `GOOGLE_AUTH_SETUP.md`

4. **Configurar Stripe**
   - Crear cuenta en [Stripe](https://stripe.com)
   - Obtener claves de API
   - Actualizar `src/services/stripeService.ts`

5. **Iniciar desarrollo**
```bash
npm run dev
```

## 🔐 Autenticación

La aplicación usa **exclusivamente Google OAuth** para todos los usuarios:

### Usuarios Regulares
- Acceso a través de `/login`
- Botón "Continuar con Google"
- Creación automática de perfil en Firestore

### Administradores
- Acceso a través de `/admin`
- Mismo flujo de Google OAuth
- Verificación automática de permisos en colección `admins`

**Ver `GOOGLE_AUTH_SETUP.md` para configuración detallada**

## 🗂️ Estructura del Proyecto

```
src/
├── components/          # Componentes reutilizables
│   ├── ui/             # Componentes UI base (Shadcn)
│   ├── Header.tsx      # Encabezado de la app
│   ├── ProductCard.tsx # Tarjeta de producto
│   └── ...
├── contexts/           # Context API
│   └── AppContext.tsx  # Estado global
├── lib/               # Utilidades
│   ├── firebase.ts    # Configuración Firebase
│   └── utils.ts       # Funciones utilitarias
├── pages/             # Páginas de la aplicación
│   ├── HomePage.tsx
│   ├── LoginPage.tsx
│   ├── CheckoutPage.tsx
│   └── ...
├── services/          # Servicios de API
│   ├── authService.ts
│   ├── productService.ts
│   ├── orderService.ts
│   └── ...
├── styles/            # Styled Components
│   ├── theme.ts       # Tema y tokens de diseño
│   ├── utils.ts       # Utilidades de estilo
│   └── styled.d.ts    # Tipos de TypeScript
└── types.ts          # Definiciones de tipos
```

## 🚀 Desarrollo

```bash
# Iniciar servidor de desarrollo
npm run dev

# Construir para producción
npm run build

# Preview de producción
npm run preview
```

## 📱 Capacitor (Móvil)

```bash
# Agregar plataformas
npx cap add ios
npx cap add android

# Sincronizar cambios
npx cap sync

# Abrir en IDE nativo
npx cap open ios
npx cap open android
```

## 🎨 Styled Components

La aplicación usa Styled Components en lugar de TailwindCSS:

- **Tema centralizado** en `src/styles/theme.ts`
- **Utilidades reutilizables** en `src/styles/utils.ts`
- **Componentes tipados** con TypeScript
- **Responsive design** con media queries
- **Animaciones y transiciones** integradas

## 🔧 Configuración de Administradores

Para crear un administrador, agrega un documento en la colección `admins`:

```javascript
// En Firestore Console
{
  uid: "uid-del-usuario-google",
  email: "<EMAIL>",
  displayName: "Administrador",
  permissions: ["manageProducts", "manageOrders"],
  createdAt: "2024-01-01T00:00:00.000Z"
}
```

## 💳 Pagos con Stripe

La integración incluye:
- Checkout seguro
- Webhooks para confirmación
- Metadata de órdenes
- Manejo de errores

## 📊 Datos de Ejemplo

La aplicación incluye productos de ejemplo que se cargan automáticamente si Firebase no está configurado.

## 🆘 Soporte

Para soporte y preguntas:
- Ver `SETUP.md` para configuración general
- Ver `GOOGLE_AUTH_SETUP.md` para autenticación
- Crear un issue en GitHub

---

**Desarrollado con ❤️ para la comunidad fitness usando tecnologías modernas**
