import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc,
  query,
  where,
  orderBy
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { Order, OrderProduct } from '../../types'

/**
 * Servicio para gestión de órdenes
 */
export class OrderService {
  
  /**
   * Crea una nueva orden
   */
  static async createOrder(orderData: Omit<Order, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'orders'), {
        ...orderData,
        createdAt: new Date().toISOString()
      })
      
      return docRef.id
    } catch (error) {
      console.error('Error creando orden:', error)
      throw error
    }
  }
  
  /**
   * Obtiene todas las órdenes de un usuario
   */
  static async getUserOrders(userId: string): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order)
      })
      
      return orders
    } catch (error) {
      console.error('Error obteniendo órdenes del usuario:', error)
      throw error
    }
  }
  
  /**
   * Obtiene todas las órdenes (para admin)
   */
  static async getAllOrders(): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order)
      })
      
      return orders
    } catch (error) {
      console.error('Error obteniendo todas las órdenes:', error)
      throw error
    }
  }
  
  /**
   * Obtiene una orden por ID
   */
  static async getOrderById(id: string): Promise<Order | null> {
    try {
      const docRef = doc(db, 'orders', id)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Order
      }
      
      return null
    } catch (error) {
      console.error('Error obteniendo orden:', error)
      throw error
    }
  }
  
  /**
   * Actualiza el estado de una orden
   */
  static async updateOrderStatus(id: string, status: Order['status']): Promise<void> {
    try {
      const docRef = doc(db, 'orders', id)
      await updateDoc(docRef, {
        status,
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error actualizando estado de orden:', error)
      throw error
    }
  }
  
  /**
   * Obtiene órdenes por estado
   */
  static async getOrdersByStatus(status: Order['status']): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order)
      })
      
      return orders
    } catch (error) {
      console.error('Error obteniendo órdenes por estado:', error)
      throw error
    }
  }
}
