import React from 'react'
import { Navigate } from 'react-router-dom'
import { User, Admin } from '../../types'

interface ProtectedRouteProps {
  children: React.ReactNode
  user: User | null
  admin?: Admin | null
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
}

/**
 * Componente para proteger rutas que requieren autenticación
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  user,
  admin,
  requireAuth = false,
  requireAdmin = false,
  redirectTo = '/login'
}) => {
  // Si requiere autenticación y no hay usuario
  if (requireAuth && !user) {
    return <Navigate to={redirectTo} replace />
  }

  // Si requiere admin y no hay admin
  if (requireAdmin && !admin) {
    return <Navigate to="/admin" replace />
  }

  return <>{children}</>
}
