ext {
    junitVersion = project.hasProperty('junitVersion') ? rootProject.ext.junitVersion : '4.13.2'
    androidxAppCompatVersion = project.hasProperty('androidxAppCompatVersion') ? rootProject.ext.androidxAppCompatVersion : '1.7.0'
    androidxJunitVersion = project.hasProperty('androidxJunitVersion') ? rootProject.ext.androidxJunitVersion : '1.2.1'
    androidxEspressoCoreVersion = project.hasProperty('androidxEspressoCoreVersion') ? rootProject.ext.androidxEspressoCoreVersion : '3.6.1'
    rgcfaIncludeGoogle = project.hasProperty('rgcfaIncludeGoogle') ? rootProject.ext.rgcfaIncludeGoogle : false
    rgcfaIncludeFacebook = project.hasProperty('rgcfaIncludeFacebook') ? rootProject.ext.rgcfaIncludeFacebook : false
    firebaseAuthVersion = project.hasProperty('firebaseAuthVersion') ? rootProject.ext.firebaseAuthVersion : '23.1.0'
    playServicesAuthVersion = project.hasProperty('playServicesAuthVersion') ? rootProject.ext.playServicesAuthVersion : '20.7.0'
    facebookLoginVersion = project.hasProperty('facebookLoginVersion') ? rootProject.ext.facebookLoginVersion : '18.0.0'
    androidxCredentialsVersion = project.hasProperty('androidxCredentialsVersion') ? rootProject.ext.androidxCredentialsVersion : '1.2.0-rc01'
    androidxCredentialsPlayServicesAuthVersion = project.hasProperty('androidxCredentialsPlayServicesAuthVersion') ? rootProject.ext.androidxCredentialsPlayServicesAuthVersion : '1.2.0-rc01'
    librariesIdentityGoogleidVersion = project.hasProperty('librariesIdentityGoogleidVersion') ? rootProject.ext.librariesIdentityGoogleidVersion : '1.1.0'
}

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.2'
    }
}

apply plugin: 'com.android.library'

android {
    namespace "io.capawesome.capacitorjs.plugins.firebase.authentication"
    compileSdk project.hasProperty('compileSdkVersion') ? rootProject.ext.compileSdkVersion : 35
    defaultConfig {
        minSdkVersion project.hasProperty('minSdkVersion') ? rootProject.ext.minSdkVersion : 23
        targetSdkVersion project.hasProperty('targetSdkVersion') ? rootProject.ext.targetSdkVersion : 35
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }
}

repositories {
    google()
    mavenCentral()
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':capacitor-android')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "com.google.firebase:firebase-auth:$firebaseAuthVersion"
    if (rgcfaIncludeGoogle) {
      implementation "com.google.android.gms:play-services-auth:$playServicesAuthVersion"
      implementation "androidx.credentials:credentials:$androidxCredentialsVersion"
      implementation "androidx.credentials:credentials-play-services-auth:$androidxCredentialsPlayServicesAuthVersion"
      implementation "com.google.android.libraries.identity.googleid:googleid:$librariesIdentityGoogleidVersion"
    } else {
      compileOnly "com.google.android.gms:play-services-auth:$playServicesAuthVersion"
      compileOnly "androidx.credentials:credentials:$androidxCredentialsVersion"
      compileOnly "androidx.credentials:credentials-play-services-auth:$androidxCredentialsPlayServicesAuthVersion"
      compileOnly "com.google.android.libraries.identity.googleid:googleid:$librariesIdentityGoogleidVersion"
    }
    if (rgcfaIncludeFacebook) {
      implementation "com.facebook.android:facebook-login:$facebookLoginVersion"
    } else {
      compileOnly "com.facebook.android:facebook-login:$facebookLoginVersion"
    }
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
}
