import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import styled from 'styled-components'
import { ProductService } from '../services/productService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { Product, ProductFormat } from '../../types'
import { Package, Plus, X, ArrowLeft, Save } from 'lucide-react'
import { container } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const FormCard = styled(Card)`
  max-width: 800px;
  margin: 0 auto;
`

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
`

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const TextArea = styled.textarea`
  min-height: 100px;
  padding: ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.border.default};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: inherit;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
`

const FormatSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing[6]};
  padding-top: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`

const FormatCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`

const FormatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing[6]};
  padding-top: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};

  @media (max-width: 640px) {
    flex-direction: column;
  }
`

export const EditProductPage: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [product, setProduct] = useState<Product | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    imageUrl: '',
    category: '',
    color: '#0072CE',
    isActive: true
  })
  const [formats, setFormats] = useState<ProductFormat[]>([
    { name: '', description: '', price: 0, stock: 0 }
  ])

  useEffect(() => {
    const loadProduct = async () => {
      if (!id) {
        navigate('/admin/products')
        return
      }

      try {
        const products = await ProductService.getProducts()
        const foundProduct = products.find(p => p.id === id)
        
        if (!foundProduct) {
          alert('Producto no encontrado')
          navigate('/admin/products')
          return
        }

        setProduct(foundProduct)
        setFormData({
          name: foundProduct.name,
          description: foundProduct.description,
          imageUrl: foundProduct.imageUrl,
          category: foundProduct.category || '',
          color: foundProduct.color!,
          isActive: foundProduct.isActive
        })
        setFormats(foundProduct.formats.length > 0 ? foundProduct.formats : [
          { name: '', description: '', price: 0, stock: 0 }
        ])
      } catch (error) {
        console.error('Error cargando producto:', error)
        alert('Error al cargar el producto')
        navigate('/admin/products')
      } finally {
        setLoading(false)
      }
    }

    loadProduct()
  }, [id, navigate])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleFormatChange = (index: number, field: keyof ProductFormat, value: string | number) => {
    setFormats(prev => prev.map((format, i) => 
      i === index ? { ...format, [field]: value } : format
    ))
  }

  const addFormat = () => {
    setFormats(prev => [...prev, { name: '', description: '', price: 0, stock: 0 }])
  }

  const removeFormat = (index: number) => {
    if (formats.length > 1) {
      setFormats(prev => prev.filter((_, i) => i !== index))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim() || formats.some(f => !f.name.trim())) {
      alert('Por favor completa todos los campos requeridos')
      return
    }

    setSaving(true)
    try {
      const updatedProduct = {
        ...product!,
        ...formData,
        formats: formats.filter(f => f.name.trim()),
        updatedAt: new Date().toISOString()
      }

      await ProductService.updateProduct(updatedProduct.id, updatedProduct)
      navigate('/admin/products')
    } catch (error) {
      console.error('Error actualizando producto:', error)
      alert('Error al actualizar el producto')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    navigate('/admin/products')
  }

  if (loading) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <LoadingSpinner />
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>
          <Package size={32} />
          Editar Producto
        </PageTitle>
      </PageHeader>

      <FormCard>
        <CardHeader>
          <CardTitle>Información del Producto</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <FormGrid>
              <FormGroup>
                <Label htmlFor="name">Nombre *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Nombre del producto"
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="category">Categoría</Label>
                <Input
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  placeholder="Categoría del producto"
                />
              </FormGroup>

              <FormGroup style={{ gridColumn: '1 / -1' }}>
                <Label htmlFor="description">Descripción</Label>
                <TextArea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Descripción del producto"
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="imageUrl">URL de Imagen</Label>
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  value={formData.imageUrl}
                  onChange={handleInputChange}
                  placeholder="https://ejemplo.com/imagen.jpg"
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="color">Color Principal</Label>
                <Input
                  id="color"
                  name="color"
                  type="color"
                  value={formData.color}
                  onChange={handleInputChange}
                />
              </FormGroup>

              <FormGroup>
                <Label>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    style={{ marginRight: '0.5rem' }}
                  />
                  Producto Activo
                </Label>
              </FormGroup>
            </FormGrid>

            <FormatSection>
              <FormatHeader>
                <h3>Formatos del Producto</h3>
                <Button type="button" onClick={addFormat} size="sm">
                  <Plus size={16} />
                  Agregar Formato
                </Button>
              </FormatHeader>

              {formats.map((format, index) => (
                <FormatCard key={index}>
                  <CardContent>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                      <h4>Formato {index + 1}</h4>
                      {formats.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFormat(index)}
                        >
                          <X size={16} />
                        </Button>
                      )}
                    </div>
                    
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                      <FormGroup>
                        <Label>Nombre del Formato *</Label>
                        <Input
                          value={format.name}
                          onChange={(e) => handleFormatChange(index, 'name', e.target.value)}
                          placeholder="Ej: 500ml, Grande, etc."
                          required
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Descripción</Label>
                        <Input
                          value={format.description}
                          onChange={(e) => handleFormatChange(index, 'description', e.target.value)}
                          placeholder="Descripción del formato"
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Precio</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={format.price}
                          onChange={(e) => handleFormatChange(index, 'price', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Stock (-1 = infinito)</Label>
                        <Input
                          type="number"
                          min="-1"
                          value={format.stock}
                          onChange={(e) => handleFormatChange(index, 'stock', parseInt(e.target.value) || 0)}
                          placeholder="0"
                        />
                      </FormGroup>
                    </div>
                  </CardContent>
                </FormatCard>
              ))}
            </FormatSection>

            <ButtonGroup>
              <Button type="button" variant="outline" onClick={handleCancel}>
                <ArrowLeft size={16} />
                Cancelar
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <Save size={16} />
                    Guardar Cambios
                  </>
                )}
              </Button>
            </ButtonGroup>
          </form>
        </CardContent>
      </FormCard>
    </PageContainer>
  )
}
