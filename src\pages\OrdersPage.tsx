import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useApp } from '../contexts/AppContext'
import { OrderService } from '../services/orderService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { Order } from '../../types'
import { Package, Calendar, CreditCard, MapPin, Eye } from 'lucide-react'
import { formatDate } from '../lib/utils'
import { container, media } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const OrdersGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[6]};
  max-width: 4xl;
  margin: 0 auto;
`

const OrderCard = styled(Card)`
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`

const OrderHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  
  ${media.sm`
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[2]};
  `}
`

const OrderInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
`

const OrderId = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const OrderDate = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
`

const StatusBadge = styled.span<{ status: string }>`
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-transform: uppercase;
  letter-spacing: 0.05em;
  
  ${({ status, theme }) => {
    switch (status) {
      case 'pending':
        return `
          background: ${theme.colors.yellow[100]};
          color: ${theme.colors.yellow[800]};
        `
      case 'paid':
        return `
          background: ${theme.colors.blue[100]};
          color: ${theme.colors.blue[800]};
        `
      case 'shipped':
        return `
          background: ${theme.colors.purple[100]};
          color: ${theme.colors.purple[800]};
        `
      case 'delivered':
        return `
          background: ${theme.colors.green[100]};
          color: ${theme.colors.green[800]};
        `
      case 'cancelled':
        return `
          background: ${theme.colors.red[100]};
          color: ${theme.colors.red[800]};
        `
      default:
        return `
          background: ${theme.colors.gray[100]};
          color: ${theme.colors.gray[800]};
        `
    }
  }}
`

const OrderDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};
  
  ${media.md`
    grid-template-columns: 2fr 1fr;
  `}
`

const ProductsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`

const ProductItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
`

const ProductName = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ProductQuantity = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const ProductPrice = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const OrderSummary = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
`

const SummaryItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const TotalAmount = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: ${({ theme }) => theme.spacing[3]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.text.muted};
`

const EmptyIcon = styled.div`
  margin: 0 auto ${({ theme }) => theme.spacing[4]};
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.gray[100]};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.gray[400]};
`

const getStatusText = (status: string): string => {
  switch (status) {
    case 'pending': return 'Pendiente'
    case 'paid': return 'Pagado'
    case 'shipped': return 'Enviado'
    case 'delivered': return 'Entregado'
    case 'cancelled': return 'Cancelado'
    default: return status
  }
}

export const OrdersPage: React.FC = () => {
  const { state } = useApp()
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadOrders = async () => {
      if (!state.user) {
        setLoading(false)
        return
      }

      try {
        const userOrders = await OrderService.getUserOrders(state.user.uid)
        // Ordenar por fecha más reciente primero
        const sortedOrders = userOrders.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        setOrders(sortedOrders)
      } catch (error) {
        console.error('Error cargando pedidos:', error)
      } finally {
        setLoading(false)
      }
    }

    loadOrders()
  }, [state.user])

  if (loading) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <LoadingSpinner size="lg" />
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>
          <Package size={32} />
          Mis Pedidos
        </PageTitle>
      </PageHeader>

      {orders.length === 0 ? (
        <EmptyState>
          <EmptyIcon>
            <Package size={32} />
          </EmptyIcon>
          <h3>No tienes pedidos aún</h3>
          <p>Cuando realices tu primera compra, aparecerá aquí.</p>
        </EmptyState>
      ) : (
        <OrdersGrid>
          {orders.map((order) => (
            <OrderCard key={order.id}>
              <CardHeader>
                <OrderHeader>
                  <OrderInfo>
                    <OrderId>Pedido #{order.id.slice(-8).toUpperCase()}</OrderId>
                    <OrderDate>
                      <Calendar size={16} />
                      {formatDate(order.createdAt)}
                    </OrderDate>
                  </OrderInfo>
                  <StatusBadge status={order.status}>
                    {getStatusText(order.status)}
                  </StatusBadge>
                </OrderHeader>
              </CardHeader>
              
              <CardContent>
                <OrderDetails>
                  <ProductsList>
                    {order.products.map((product, index) => (
                      <ProductItem key={index}>
                        <ProductInfo>
                          <ProductName>{product.name}</ProductName>
                          <ProductQuantity>Cantidad: {product.quantity}</ProductQuantity>
                        </ProductInfo>
                        <ProductPrice>${product.unitPrice.toFixed(2)}</ProductPrice>
                      </ProductItem>
                    ))}
                  </ProductsList>
                  
                  <OrderSummary>
                    <SummaryItem>
                      <CreditCard size={16} />
                      ID de Pago: {order.paymentId.slice(-8)}
                    </SummaryItem>
                    <SummaryItem>
                      <MapPin size={16} />
                      Dirección: {order.addressId.slice(-8)}
                    </SummaryItem>
                    <TotalAmount>
                      <span>Total:</span>
                      <span>${order.totalAmount.toFixed(2)}</span>
                    </TotalAmount>
                  </OrderSummary>
                </OrderDetails>
              </CardContent>
            </OrderCard>
          ))}
        </OrdersGrid>
      )}
    </PageContainer>
  )
}
