{"version": 3, "sources": ["../../@paypal/react-paypal-js/dist/esm/react-paypal-js.js"], "sourcesContent": ["/*!\n * react-paypal-js v8.8.3 (2025-04-11T19:50:46.506Z)\n * Copyright 2020-present, PayPal, Inc. All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport React, { createContext, useContext, useRef, useState, useEffect, useReducer } from 'react';\n\n/**\n * Enum for the SDK script resolve status,\n *\n * @enum {string}\n */\nvar SCRIPT_LOADING_STATE;\n(function (SCRIPT_LOADING_STATE) {\n  SCRIPT_LOADING_STATE[\"INITIAL\"] = \"initial\";\n  SCRIPT_LOADING_STATE[\"PENDING\"] = \"pending\";\n  SCRIPT_LOADING_STATE[\"REJECTED\"] = \"rejected\";\n  SCRIPT_LOADING_STATE[\"RESOLVED\"] = \"resolved\";\n})(SCRIPT_LOADING_STATE || (SCRIPT_LOADING_STATE = {}));\n/**\n * Enum for the PayPalScriptProvider context dispatch actions\n *\n * @enum {string}\n */\nvar DISPATCH_ACTION;\n(function (DISPATCH_ACTION) {\n  DISPATCH_ACTION[\"LOADING_STATUS\"] = \"setLoadingStatus\";\n  DISPATCH_ACTION[\"RESET_OPTIONS\"] = \"resetOptions\";\n  DISPATCH_ACTION[\"SET_BRAINTREE_INSTANCE\"] = \"braintreeInstance\";\n})(DISPATCH_ACTION || (DISPATCH_ACTION = {}));\n/**\n * Enum for all the available hosted fields\n *\n * @enum {string}\n */\nvar PAYPAL_HOSTED_FIELDS_TYPES;\n(function (PAYPAL_HOSTED_FIELDS_TYPES) {\n  PAYPAL_HOSTED_FIELDS_TYPES[\"NUMBER\"] = \"number\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"CVV\"] = \"cvv\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_DATE\"] = \"expirationDate\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_MONTH\"] = \"expirationMonth\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_YEAR\"] = \"expirationYear\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"POSTAL_CODE\"] = \"postalCode\";\n})(PAYPAL_HOSTED_FIELDS_TYPES || (PAYPAL_HOSTED_FIELDS_TYPES = {}));\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __rest$1(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/*********************************************\n * Common reference to the script identifier *\n *********************************************/\n// keep this script id value in kebab-case format\nvar SCRIPT_ID = \"data-react-paypal-script-id\";\nvar SDK_SETTINGS = {\n  DATA_CLIENT_TOKEN: \"dataClientToken\",\n  DATA_JS_SDK_LIBRARY: \"dataJsSdkLibrary\",\n  DATA_LIBRARY_VALUE: \"react-paypal-js\",\n  DATA_NAMESPACE: \"dataNamespace\",\n  DATA_SDK_INTEGRATION_SOURCE: \"dataSdkIntegrationSource\",\n  DATA_USER_ID_TOKEN: \"dataUserIdToken\"\n};\nvar LOAD_SCRIPT_ERROR = \"Failed to load the PayPal JS SDK script.\";\n/****************************\n * Braintree error messages *\n ****************************/\nvar EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE = \"Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.\";\nvar braintreeVersion = \"3.117.0\";\nvar BRAINTREE_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/client.min.js\");\nvar BRAINTREE_PAYPAL_CHECKOUT_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/paypal-checkout.min.js\");\n/*********************\n * PayPal namespaces *\n *********************/\nvar DEFAULT_PAYPAL_NAMESPACE = \"paypal\";\nvar DEFAULT_BRAINTREE_NAMESPACE = \"braintree\";\n/*****************\n * Hosted Fields *\n *****************/\nvar HOSTED_FIELDS_CHILDREN_ERROR = \"To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes\";\nvar HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate HostedFields as children\";\n/*******************\n * Script Provider *\n *******************/\nvar SCRIPT_PROVIDER_REDUCER_ERROR = \"usePayPalScriptReducer must be used within a PayPalScriptProvider\";\nvar CARD_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate CardFields as children\";\nvar CARD_FIELDS_CONTEXT_ERROR = \"Individual CardFields must be rendered inside the PayPalCardFieldsProvider\";\n\n/**\n * Get the namespace from the window in the browser\n * this is useful to get the paypal object from window\n * after load PayPal SDK script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getPayPalWindowNamespace$1(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_PAYPAL_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Get a namespace from the window in the browser\n * this is useful to get the braintree from window\n * after load Braintree script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getBraintreeWindowNamespace(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_BRAINTREE_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Creates a string hash code based on the string argument\n *\n * @param str the source input string to hash\n * @returns string hash code\n */\nfunction hashStr(str) {\n  var hash = \"\";\n  for (var i = 0; i < str.length; i++) {\n    var total = str[i].charCodeAt(0) * i;\n    if (str[i + 1]) {\n      total += str[i + 1].charCodeAt(0) * (i - 1);\n    }\n    hash += String.fromCharCode(97 + Math.abs(total) % 26);\n  }\n  return hash;\n}\nfunction generateErrorMessage(_a) {\n  var reactComponentName = _a.reactComponentName,\n    sdkComponentKey = _a.sdkComponentKey,\n    _b = _a.sdkRequestedComponents,\n    sdkRequestedComponents = _b === void 0 ? \"\" : _b,\n    _c = _a.sdkDataNamespace,\n    sdkDataNamespace = _c === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _c;\n  var requiredOptionCapitalized = sdkComponentKey.charAt(0).toUpperCase().concat(sdkComponentKey.substring(1));\n  var errorMessage = \"Unable to render <\".concat(reactComponentName, \" /> because window.\").concat(sdkDataNamespace, \".\").concat(requiredOptionCapitalized, \" is undefined.\");\n  // The JS SDK only loads the buttons component by default.\n  // All other components like messages and marks must be requested using the \"components\" query parameter\n  var requestedComponents = typeof sdkRequestedComponents === \"string\" ? sdkRequestedComponents : sdkRequestedComponents.join(\",\");\n  if (!requestedComponents.includes(sdkComponentKey)) {\n    var expectedComponents = [requestedComponents, sdkComponentKey].filter(Boolean).join();\n    errorMessage += \"\\nTo fix the issue, add '\".concat(sdkComponentKey, \"' to the list of components passed to the parent PayPalScriptProvider:\") + \"\\n`<PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>`.\");\n  }\n  return errorMessage;\n}\n\n/**\n * Generate a new random identifier for react-paypal-js\n *\n * @returns the {@code string} containing the random library name\n */\nfunction getScriptID(options) {\n  // exclude the data-react-paypal-script-id value from the options hash\n  var _a = options,\n    _b = SCRIPT_ID;\n  _a[_b];\n  var paypalScriptOptions = __rest$1(_a, [_b + \"\"]);\n  return \"react-paypal-js-\".concat(hashStr(JSON.stringify(paypalScriptOptions)));\n}\n/**\n * Destroy the PayPal SDK from the document page\n *\n * @param reactPayPalScriptID the script identifier\n */\nfunction destroySDKScript(reactPayPalScriptID) {\n  var scriptNode = self.document.querySelector(\"script[\".concat(SCRIPT_ID, \"=\\\"\").concat(reactPayPalScriptID, \"\\\"]\"));\n  if (scriptNode === null || scriptNode === void 0 ? void 0 : scriptNode.parentNode) {\n    scriptNode.parentNode.removeChild(scriptNode);\n  }\n}\n/**\n * Reducer function to handle complex state changes on the context\n *\n * @param state  the current state on the context object\n * @param action the action to be executed on the previous state\n * @returns a the same state if the action wasn't found, or a new state otherwise\n */\nfunction scriptReducer(state, action) {\n  var _a, _b;\n  switch (action.type) {\n    case DISPATCH_ACTION.LOADING_STATUS:\n      if (typeof action.value === \"object\") {\n        return __assign(__assign({}, state), {\n          loadingStatus: action.value.state,\n          loadingStatusErrorMessage: action.value.message\n        });\n      }\n      return __assign(__assign({}, state), {\n        loadingStatus: action.value\n      });\n    case DISPATCH_ACTION.RESET_OPTIONS:\n      // destroy existing script to make sure only one script loads at a time\n      destroySDKScript(state.options[SCRIPT_ID]);\n      return __assign(__assign({}, state), {\n        loadingStatus: SCRIPT_LOADING_STATE.PENDING,\n        options: __assign(__assign((_a = {}, _a[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _a), action.value), (_b = {}, _b[SCRIPT_ID] = \"\".concat(getScriptID(action.value)), _b))\n      });\n    case DISPATCH_ACTION.SET_BRAINTREE_INSTANCE:\n      return __assign(__assign({}, state), {\n        braintreePayPalCheckoutInstance: action.value\n      });\n    default:\n      {\n        return state;\n      }\n  }\n}\n// Create the React context to use in the script provider component\nvar ScriptContext = createContext(null);\n\n/**\n * Check if the context is valid and ready to dispatch actions.\n *\n * @param scriptContext the result of connecting to the context provider\n * @returns strict context avoiding null values in the type\n */\nfunction validateReducer(scriptContext) {\n  if (typeof (scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.dispatch) === \"function\" && scriptContext.dispatch.length !== 0) {\n    return scriptContext;\n  }\n  throw new Error(SCRIPT_PROVIDER_REDUCER_ERROR);\n}\n/**\n * Check if the dataClientToken or the dataUserIdToken are\n * set in the options of the context.\n * @type dataClientToken is use to pass a client token\n * @type dataUserIdToken is use to pass a client tokenization key\n *\n * @param scriptContext the result of connecting to the context provider\n * @throws an {@link Error} if both dataClientToken and the dataUserIdToken keys are null or undefined\n * @returns strict context if one of the keys are defined\n */\nvar validateBraintreeAuthorizationData = function (scriptContext) {\n  var _a, _b;\n  if (!((_a = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _a === void 0 ? void 0 : _a[SDK_SETTINGS.DATA_CLIENT_TOKEN]) && !((_b = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _b === void 0 ? void 0 : _b[SDK_SETTINGS.DATA_USER_ID_TOKEN])) {\n    throw new Error(EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE);\n  }\n  return scriptContext;\n};\n\n/**\n * Custom hook to get access to the Script context and\n * dispatch actions to modify the state on the {@link ScriptProvider} component\n *\n * @returns a tuple containing the state of the context and\n * a dispatch function to modify the state\n */\nfunction usePayPalScriptReducer() {\n  var scriptContext = validateReducer(useContext(ScriptContext));\n  var derivedStatusContext = __assign(__assign({}, scriptContext), {\n    isInitial: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.INITIAL,\n    isPending: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.PENDING,\n    isResolved: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.RESOLVED,\n    isRejected: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.REJECTED\n  });\n  return [derivedStatusContext, scriptContext.dispatch];\n}\n/**\n * Custom hook to get access to the ScriptProvider context\n *\n * @returns the latest state of the context\n */\nfunction useScriptProviderContext() {\n  var scriptContext = validateBraintreeAuthorizationData(validateReducer(useContext(ScriptContext)));\n  return [scriptContext, scriptContext.dispatch];\n}\n\n// Create the React context to use in the PayPal hosted fields provider\nvar PayPalHostedFieldsContext = createContext({});\n\n/**\n * Custom hook to get access to the PayPal Hosted Fields instance.\n * The instance represent the returned object after the render process\n * With this object a user can submit the fields and dynamically modify the cards\n *\n * @returns the hosted fields instance if is available in the component\n */\nfunction usePayPalHostedFields() {\n  return useContext(PayPalHostedFieldsContext);\n}\nfunction useProxyProps(props) {\n  var proxyRef = useRef(new Proxy({}, {\n    get: function (target, prop, receiver) {\n      /**\n       *\n       * If target[prop] is a function, return a function that accesses\n       * this function off the target object. We can mutate the target with\n       * new copies of this function without having to re-render the\n       * SDK components to pass new callbacks.\n       *\n       * */\n      if (typeof target[prop] === \"function\") {\n        return function () {\n          var args = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n          }\n          // eslint-disable-next-line @typescript-eslint/ban-types\n          return target[prop].apply(target, args);\n        };\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  }));\n  proxyRef.current = Object.assign(proxyRef.current, props);\n  return proxyRef.current;\n}\n\n/**\nThis `<PayPalButtons />` component supports rendering [buttons](https://developer.paypal.com/docs/business/javascript-sdk/javascript-sdk-reference/#buttons) for PayPal, Venmo, and alternative payment methods.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalButtons = function (_a) {\n  var _b;\n  var _c = _a.className,\n    className = _c === void 0 ? \"\" : _c,\n    _d = _a.disabled,\n    disabled = _d === void 0 ? false : _d,\n    children = _a.children,\n    _e = _a.forceReRender,\n    forceReRender = _e === void 0 ? [] : _e,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\"]);\n  var isDisabledStyle = disabled ? {\n    opacity: 0.38\n  } : {};\n  var classNames = \"\".concat(className, \" \").concat(disabled ? \"paypal-buttons-disabled\" : \"\").trim();\n  var buttonsContainerRef = useRef(null);\n  var buttons = useRef(null);\n  var proxyProps = useProxyProps(buttonProps);\n  var _f = usePayPalScriptReducer()[0],\n    isResolved = _f.isResolved,\n    options = _f.options;\n  var _g = useState(null),\n    initActions = _g[0],\n    setInitActions = _g[1];\n  var _h = useState(true),\n    isEligible = _h[0],\n    setIsEligible = _h[1];\n  var _j = useState(null),\n    setErrorState = _j[1];\n  function closeButtonsComponent() {\n    if (buttons.current !== null) {\n      buttons.current.close().catch(function () {\n        // ignore errors when closing the component\n      });\n    }\n  }\n  if ((_b = buttons.current) === null || _b === void 0 ? void 0 : _b.updateProps) {\n    buttons.current.updateProps({\n      message: buttonProps.message\n    });\n  }\n  // useEffect hook for rendering the buttons\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return closeButtonsComponent;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options.dataNamespace);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Buttons === undefined) {\n      setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalButtons.displayName,\n          sdkComponentKey: \"buttons\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n      return closeButtonsComponent;\n    }\n    var decoratedOnInit = function (data, actions) {\n      setInitActions(actions);\n      if (typeof buttonProps.onInit === \"function\") {\n        buttonProps.onInit(data, actions);\n      }\n    };\n    try {\n      buttons.current = paypalWindowNamespace.Buttons(__assign(__assign({}, proxyProps), {\n        onInit: decoratedOnInit\n      }));\n    } catch (err) {\n      return setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. Failed to initialize:  \".concat(err));\n      });\n    }\n    // only render the button when eligible\n    if (buttons.current.isEligible() === false) {\n      setIsEligible(false);\n      return closeButtonsComponent;\n    }\n    if (!buttonsContainerRef.current) {\n      return closeButtonsComponent;\n    }\n    buttons.current.render(buttonsContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (buttonsContainerRef.current === null || buttonsContainerRef.current.children.length === 0) {\n        // paypal buttons container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal buttons container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. \".concat(err));\n      });\n    });\n    return closeButtonsComponent;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray(__spreadArray([isResolved], forceReRender, true), [buttonProps.fundingSource], false));\n  // useEffect hook for managing disabled state\n  useEffect(function () {\n    if (initActions === null) {\n      return;\n    }\n    if (disabled === true) {\n      initActions.disable().catch(function () {\n        // ignore errors when disabling the component\n      });\n    } else {\n      initActions.enable().catch(function () {\n        // ignore errors when enabling the component\n      });\n    }\n  }, [disabled, initActions]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: buttonsContainerRef,\n    style: isDisabledStyle,\n    className: classNames\n  }) : children);\n};\nPayPalButtons.displayName = \"PayPalButtons\";\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction findScript(url, attributes) {\n  var currentScript = document.querySelector(\"script[src=\\\"\".concat(url, \"\\\"]\"));\n  if (currentScript === null) return null;\n  var nextScript = createScriptElement(url, attributes);\n  var currentScriptClone = currentScript.cloneNode();\n  delete currentScriptClone.dataset.uidAuto;\n  if (Object.keys(currentScriptClone.dataset).length !== Object.keys(nextScript.dataset).length) {\n    return null;\n  }\n  var isExactMatch = true;\n  Object.keys(currentScriptClone.dataset).forEach(function (key) {\n    if (currentScriptClone.dataset[key] !== nextScript.dataset[key]) {\n      isExactMatch = false;\n    }\n  });\n  return isExactMatch ? currentScript : null;\n}\nfunction insertScriptElement(_a) {\n  var url = _a.url,\n    attributes = _a.attributes,\n    onSuccess = _a.onSuccess,\n    onError = _a.onError;\n  var newScript = createScriptElement(url, attributes);\n  newScript.onerror = onError;\n  newScript.onload = onSuccess;\n  document.head.insertBefore(newScript, document.head.firstElementChild);\n}\nfunction processOptions(_a) {\n  var customSdkBaseUrl = _a.sdkBaseUrl,\n    environment = _a.environment,\n    options = __rest(_a, [\"sdkBaseUrl\", \"environment\"]);\n  var sdkBaseUrl = customSdkBaseUrl || processSdkBaseUrl(environment);\n  var optionsWithStringIndex = options;\n  var _b = Object.keys(optionsWithStringIndex).filter(function (key) {\n      return typeof optionsWithStringIndex[key] !== \"undefined\" && optionsWithStringIndex[key] !== null && optionsWithStringIndex[key] !== \"\";\n    }).reduce(function (accumulator, key) {\n      var value = optionsWithStringIndex[key].toString();\n      key = camelCaseToKebabCase(key);\n      if (key.substring(0, 4) === \"data\" || key === \"crossorigin\") {\n        accumulator.attributes[key] = value;\n      } else {\n        accumulator.queryParams[key] = value;\n      }\n      return accumulator;\n    }, {\n      queryParams: {},\n      attributes: {}\n    }),\n    queryParams = _b.queryParams,\n    attributes = _b.attributes;\n  if (queryParams[\"merchant-id\"] && queryParams[\"merchant-id\"].indexOf(\",\") !== -1) {\n    attributes[\"data-merchant-id\"] = queryParams[\"merchant-id\"];\n    queryParams[\"merchant-id\"] = \"*\";\n  }\n  return {\n    url: \"\".concat(sdkBaseUrl, \"?\").concat(objectToQueryString(queryParams)),\n    attributes: attributes\n  };\n}\nfunction camelCaseToKebabCase(str) {\n  var replacer = function (match, indexOfMatch) {\n    return (indexOfMatch ? \"-\" : \"\") + match.toLowerCase();\n  };\n  return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, replacer);\n}\nfunction objectToQueryString(params) {\n  var queryString = \"\";\n  Object.keys(params).forEach(function (key) {\n    if (queryString.length !== 0) queryString += \"&\";\n    queryString += key + \"=\" + params[key];\n  });\n  return queryString;\n}\nfunction processSdkBaseUrl(environment) {\n  return environment === \"sandbox\" ? \"https://www.sandbox.paypal.com/sdk/js\" : \"https://www.paypal.com/sdk/js\";\n}\nfunction createScriptElement(url, attributes) {\n  if (attributes === void 0) {\n    attributes = {};\n  }\n  var newScript = document.createElement(\"script\");\n  newScript.src = url;\n  Object.keys(attributes).forEach(function (key) {\n    newScript.setAttribute(key, attributes[key]);\n    if (key === \"data-csp-nonce\") {\n      newScript.setAttribute(\"nonce\", attributes[\"data-csp-nonce\"]);\n    }\n  });\n  return newScript;\n}\nfunction loadScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  if (typeof document === \"undefined\") return PromisePonyfill.resolve(null);\n  var _a = processOptions(options),\n    url = _a.url,\n    attributes = _a.attributes;\n  var namespace = attributes[\"data-namespace\"] || \"paypal\";\n  var existingWindowNamespace = getPayPalWindowNamespace(namespace);\n  if (!attributes[\"data-js-sdk-library\"]) {\n    attributes[\"data-js-sdk-library\"] = \"paypal-js\";\n  }\n  if (findScript(url, attributes) && existingWindowNamespace) {\n    return PromisePonyfill.resolve(existingWindowNamespace);\n  }\n  return loadCustomScript({\n    url: url,\n    attributes: attributes\n  }, PromisePonyfill).then(function () {\n    var newWindowNamespace = getPayPalWindowNamespace(namespace);\n    if (newWindowNamespace) {\n      return newWindowNamespace;\n    }\n    throw new Error(\"The window.\".concat(namespace, \" global variable is not available.\"));\n  });\n}\nfunction loadCustomScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  var url = options.url,\n    attributes = options.attributes;\n  if (typeof url !== \"string\" || url.length === 0) {\n    throw new Error(\"Invalid url.\");\n  }\n  if (typeof attributes !== \"undefined\" && typeof attributes !== \"object\") {\n    throw new Error(\"Expected attributes to be an object.\");\n  }\n  return new PromisePonyfill(function (resolve, reject) {\n    if (typeof document === \"undefined\") return resolve();\n    insertScriptElement({\n      url: url,\n      attributes: attributes,\n      onSuccess: function () {\n        return resolve();\n      },\n      onError: function () {\n        var defaultError = new Error(\"The script \\\"\".concat(url, \"\\\" failed to load. Check the HTTP status code and response body in DevTools to learn more.\"));\n        return reject(defaultError);\n      }\n    });\n  });\n}\nfunction getPayPalWindowNamespace(namespace) {\n  return window[namespace];\n}\nfunction validateArguments(options, PromisePonyfill) {\n  if (typeof options !== \"object\" || options === null) {\n    throw new Error(\"Expected an options object.\");\n  }\n  var environment = options.environment;\n  if (environment && environment !== \"production\" && environment !== \"sandbox\") {\n    throw new Error('The `environment` option must be either \"production\" or \"sandbox\".');\n  }\n  if (typeof PromisePonyfill !== \"undefined\" && typeof PromisePonyfill !== \"function\") {\n    throw new Error(\"Expected PromisePonyfill to be a function.\");\n  }\n}\n\n/**\n * Simple check to determine if the Braintree is a valid namespace.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns a boolean representing if the namespace is valid.\n */\nvar isValidBraintreeNamespace = function (braintreeSource) {\n  var _a, _b;\n  if (typeof ((_a = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.client) === null || _a === void 0 ? void 0 : _a.create) !== \"function\" && typeof ((_b = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.paypalCheckout) === null || _b === void 0 ? void 0 : _b.create) !== \"function\") {\n    throw new Error(\"The braintreeNamespace property is not a valid BraintreeNamespace type.\");\n  }\n  return true;\n};\n/**\n * Use `actions.braintree` to provide an interface for the paypalCheckoutInstance\n * through the createOrder, createBillingAgreement and onApprove callbacks\n *\n * @param braintreeButtonProps the component button options\n * @returns a new copy of the component button options casted as {@link PayPalButtonsComponentProps}\n */\nvar decorateActions = function (buttonProps, payPalCheckoutInstance) {\n  var createOrderRef = buttonProps.createOrder;\n  var createBillingAgreementRef = buttonProps.createBillingAgreement;\n  var onApproveRef = buttonProps.onApprove;\n  if (typeof createOrderRef === \"function\") {\n    buttonProps.createOrder = function (data, actions) {\n      return createOrderRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof createBillingAgreementRef === \"function\") {\n    buttonProps.createBillingAgreement = function (data, actions) {\n      return createBillingAgreementRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof onApproveRef === \"function\") {\n    buttonProps.onApprove = function (data, actions) {\n      return onApproveRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  return __assign({}, buttonProps);\n};\n/**\n * Get the Braintree namespace from the component props.\n * If the prop `braintreeNamespace` is undefined will try to load it from the CDN.\n * This function allows users to set the braintree manually on the `BraintreePayPalButtons` component.\n *\n * Use case can be for example legacy sites using AMD/UMD modules,\n * trying to integrate the `BraintreePayPalButtons` component.\n * If we attempt to load the Braintree from the CDN won't define the braintree namespace.\n * This happens because the braintree script is an UMD module.\n * After detecting the AMD on the global scope will create an anonymous module using `define`\n * and the `BraintreePayPalButtons` won't be able to get access to the `window.braintree` namespace\n * from the global context.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns the {@link BraintreeNamespace}\n */\nvar getBraintreeNamespace = function (braintreeSource) {\n  if (braintreeSource && isValidBraintreeNamespace(braintreeSource)) {\n    return Promise.resolve(braintreeSource);\n  }\n  return Promise.all([loadCustomScript({\n    url: BRAINTREE_SOURCE\n  }), loadCustomScript({\n    url: BRAINTREE_PAYPAL_CHECKOUT_SOURCE\n  })]).then(function () {\n    return getBraintreeWindowNamespace();\n  });\n};\n\n/**\nThis `<BraintreePayPalButtons />` component renders the [Braintree PayPal Buttons](https://developer.paypal.com/braintree/docs/guides/paypal/overview) for Braintree Merchants.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nNote: You are able to make your integration using the client token or using the tokenization key.\n\n- To use the client token integration set the key `dataClientToken` in the `PayPayScriptProvider` component's options.\n- To use the tokenization key integration set the key `dataUserIdToken` in the `PayPayScriptProvider` component's options.\n*/\nvar BraintreePayPalButtons = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.disabled,\n    disabled = _c === void 0 ? false : _c,\n    children = _a.children,\n    _d = _a.forceReRender,\n    forceReRender = _d === void 0 ? [] : _d,\n    braintreeNamespace = _a.braintreeNamespace,\n    merchantAccountId = _a.merchantAccountId,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\", \"braintreeNamespace\", \"merchantAccountId\"]);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var _f = useScriptProviderContext(),\n    providerContext = _f[0],\n    dispatch = _f[1];\n  useEffect(function () {\n    getBraintreeNamespace(braintreeNamespace).then(function (braintree) {\n      var clientTokenizationKey = providerContext.options[SDK_SETTINGS.DATA_USER_ID_TOKEN];\n      var clientToken = providerContext.options[SDK_SETTINGS.DATA_CLIENT_TOKEN];\n      return braintree.client.create({\n        authorization: clientTokenizationKey || clientToken\n      }).then(function (clientInstance) {\n        var merchantProp = merchantAccountId ? {\n          merchantAccountId: merchantAccountId\n        } : {};\n        return braintree.paypalCheckout.create(__assign(__assign({}, merchantProp), {\n          client: clientInstance\n        }));\n      }).then(function (paypalCheckoutInstance) {\n        dispatch({\n          type: DISPATCH_ACTION.SET_BRAINTREE_INSTANCE,\n          value: paypalCheckoutInstance\n        });\n      });\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [providerContext.options]);\n  return React.createElement(React.Fragment, null, providerContext.braintreePayPalCheckoutInstance && React.createElement(PayPalButtons, __assign({\n    className: className,\n    disabled: disabled,\n    forceReRender: forceReRender\n  }, decorateActions(buttonProps, providerContext.braintreePayPalCheckoutInstance)), children));\n};\n\n/**\nThe `<PayPalMarks />` component is used for conditionally rendering different payment options using radio buttons.\nThe [Display PayPal Buttons with other Payment Methods guide](https://developer.paypal.com/docs/business/checkout/add-capabilities/buyer-experience/#display-paypal-buttons-with-other-payment-methods) describes this style of integration in detail.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nThis component can also be configured to use a single funding source similar to the [standalone buttons](https://developer.paypal.com/docs/business/checkout/configure-payments/standalone-buttons/) approach.\nA `FUNDING` object is exported by this library which has a key for every available funding source option.\n*/\nvar PayPalMarks = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    children = _a.children,\n    markProps = __rest$1(_a, [\"className\", \"children\"]);\n  var _c = usePayPalScriptReducer()[0],\n    isResolved = _c.isResolved,\n    options = _c.options;\n  var markContainerRef = useRef(null);\n  var _d = useState(true),\n    isEligible = _d[0],\n    setIsEligible = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  /**\n   * Render PayPal Mark into the DOM\n   */\n  var renderPayPalMark = function (mark) {\n    var current = markContainerRef.current;\n    // only render the mark when eligible\n    if (!current || !mark.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Remove any children before render it again\n    if (current.firstChild) {\n      current.removeChild(current.firstChild);\n    }\n    mark.render(current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (current === null || current.children.length === 0) {\n        // paypal marks container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal marks container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMarks /> component. \".concat(err));\n      });\n    });\n  };\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Marks === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMarks.displayName,\n          sdkComponentKey: \"marks\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    renderPayPalMark(paypalWindowNamespace.Marks(__assign({}, markProps)));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isResolved, markProps.fundingSource]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: markContainerRef,\n    className: className\n  }) : children);\n};\nPayPalMarks.displayName = \"PayPalMarks\";\n\n/**\nThis `<PayPalMessages />` messages component renders a credit messaging on upstream merchant sites.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalMessages = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.forceReRender,\n    forceReRender = _c === void 0 ? [] : _c,\n    messageProps = __rest$1(_a, [\"className\", \"forceReRender\"]);\n  var _d = usePayPalScriptReducer()[0],\n    isResolved = _d.isResolved,\n    options = _d.options;\n  var messagesContainerRef = useRef(null);\n  var messages = useRef(null);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Messages === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMessages.displayName,\n          sdkComponentKey: \"messages\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    messages.current = paypalWindowNamespace.Messages(__assign({}, messageProps));\n    messages.current.render(messagesContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (messagesContainerRef.current === null || messagesContainerRef.current.children.length === 0) {\n        // paypal messages container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal messages container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMessages /> component. \".concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray([isResolved], forceReRender, true));\n  return React.createElement(\"div\", {\n    ref: messagesContainerRef,\n    className: className\n  });\n};\nPayPalMessages.displayName = \"PayPalMessages\";\n\n/**\nThis `<PayPalScriptProvider />` component takes care of loading the JS SDK `<script>`.\nIt manages state for script loading so children components like `<PayPalButtons />` know when it's safe to use the `window.paypal` global namespace.\n\nNote: You always should use this component as a wrapper for  `PayPalButtons`, `PayPalMarks`, `PayPalMessages` and `BraintreePayPalButtons` components.\n */\nvar PayPalScriptProvider = function (_a) {\n  var _b;\n  var _c = _a.options,\n    options = _c === void 0 ? {\n      clientId: \"test\"\n    } : _c,\n    children = _a.children,\n    _d = _a.deferLoading,\n    deferLoading = _d === void 0 ? false : _d;\n  var _e = useReducer(scriptReducer, {\n      options: __assign(__assign({}, options), (_b = {}, _b[SDK_SETTINGS.DATA_JS_SDK_LIBRARY] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SCRIPT_ID] = \"\".concat(getScriptID(options)), _b)),\n      loadingStatus: deferLoading ? SCRIPT_LOADING_STATE.INITIAL : SCRIPT_LOADING_STATE.PENDING\n    }),\n    state = _e[0],\n    dispatch = _e[1];\n  useEffect(function () {\n    if (deferLoading === false && state.loadingStatus === SCRIPT_LOADING_STATE.INITIAL) {\n      return dispatch({\n        type: DISPATCH_ACTION.LOADING_STATUS,\n        value: SCRIPT_LOADING_STATE.PENDING\n      });\n    }\n    if (state.loadingStatus !== SCRIPT_LOADING_STATE.PENDING) {\n      return;\n    }\n    var isSubscribed = true;\n    loadScript(state.options).then(function () {\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: SCRIPT_LOADING_STATE.RESOLVED\n        });\n      }\n    }).catch(function (err) {\n      console.error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: {\n            state: SCRIPT_LOADING_STATE.REJECTED,\n            message: String(err)\n          }\n        });\n      }\n    });\n    return function () {\n      isSubscribed = false;\n    };\n  }, [state.options, deferLoading, state.loadingStatus]);\n  return React.createElement(ScriptContext.Provider, {\n    value: __assign(__assign({}, state), {\n      dispatch: dispatch\n    })\n  }, children);\n};\n\n/**\n * Custom hook to store registered hosted fields children\n * Each `PayPalHostedField` component should be registered on the parent provider\n *\n * @param initialValue the initially registered components\n * @returns at first, an {@link Object} containing the registered hosted fields,\n * and at the second a function handler to register the hosted fields components\n */\nvar useHostedFieldsRegister = function (initialValue) {\n  if (initialValue === void 0) {\n    initialValue = {};\n  }\n  var registeredFields = useRef(initialValue);\n  var registerHostedField = function (component) {\n    registeredFields.current = __assign(__assign({}, registeredFields.current), component);\n  };\n  return [registeredFields, registerHostedField];\n};\n\n/**\n * Throw an exception if the HostedFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the hosted-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'hosted-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingHostedFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",hosted-fields\") : \"hosted-fields\";\n  var errorMessage = \"Unable to render <PayPalHostedFieldsProvider /> because window.\".concat(dataNamespace, \".HostedFields is undefined.\");\n  if (!components.includes(\"hosted-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\n/**\n * Validate the expiration date component. Valid combinations are:\n * 1- Only the `expirationDate` field exists.\n * 2- Only the `expirationMonth` and `expirationYear` fields exist. Cannot be used with the `expirationDate` field.\n *\n * @param registerTypes\n * @returns @type {true} when the children are valid\n */\nvar validateExpirationDate = function (registerTypes) {\n  return !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_DATE) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_MONTH) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_YEAR);\n};\n/**\n * Check if we find the [number, expiration, cvv] in children\n *\n * @param requiredChildren the list with required children [number, expiration, cvv]\n * @param registerTypes    the list of all the children types pass to the parent\n * @throw an @type {Error} when not find the default children\n */\nvar hasDefaultChildren = function (registerTypes) {\n  if (!registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.NUMBER) || !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.CVV) || validateExpirationDate(registerTypes)) {\n    throw new Error(HOSTED_FIELDS_CHILDREN_ERROR);\n  }\n};\n/**\n * Check if we don't have duplicate children types\n *\n * @param registerTypes the list of all the children types pass to the parent\n * @throw an @type {Error} when duplicate types was found\n */\nvar noDuplicateChildren = function (registerTypes) {\n  if (registerTypes.length !== new Set(registerTypes).size) {\n    throw new Error(HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR);\n  }\n};\n/**\n * Validate the hosted field children in the PayPalHostedFieldsProvider component.\n * These are the rules:\n * 1- We need to find 3 default children for number, expiration, cvv\n * 2- No duplicate children are allowed\n * 3- No invalid combinations of `expirationDate`, `expirationMonth`, and `expirationYear`\n *\n * @param childrenList     the list of children\n * @param requiredChildren the list with required children [number, expiration, cvv]\n */\nvar validateHostedFieldChildren = function (registeredFields) {\n  hasDefaultChildren(registeredFields);\n  noDuplicateChildren(registeredFields);\n};\n\n/**\nThis `<PayPalHostedFieldsProvider />` provider component wraps the form field elements and accepts props like `createOrder()`.\n\nThis provider component is designed to be used with the `<PayPalHostedField />` component.\n\nWarning: If you don't see anything in the screen probably your client is ineligible.\nTo handle this problem make sure to use the prop `notEligibleError` and pass a component with a custom message.\nTake a look to this link if that is the case: https://developer.paypal.com/docs/checkout/advanced/integrate/\n*/\nvar PayPalHostedFieldsProvider = function (_a) {\n  var styles = _a.styles,\n    createOrder = _a.createOrder,\n    notEligibleError = _a.notEligibleError,\n    children = _a.children,\n    installments = _a.installments;\n  var _b = useScriptProviderContext()[0],\n    options = _b.options,\n    loadingStatus = _b.loadingStatus;\n  var _c = useState(true),\n    isEligible = _c[0],\n    setIsEligible = _c[1];\n  var _d = useState(),\n    cardFields = _d[0],\n    setCardFields = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var hostedFieldsContainerRef = useRef(null);\n  var hostedFields = useRef();\n  var _f = useHostedFieldsRegister(),\n    registeredFields = _f[0],\n    registerHostedField = _f[1];\n  useEffect(function () {\n    var _a;\n    validateHostedFieldChildren(Object.keys(registeredFields.current));\n    // Only render the hosted fields when script is loaded and hostedFields is eligible\n    if (!(loadingStatus === SCRIPT_LOADING_STATE.RESOLVED)) {\n      return;\n    }\n    // Get the hosted fields from the [window.paypal.HostedFields] SDK\n    hostedFields.current = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]).HostedFields;\n    if (!hostedFields.current) {\n      throw new Error(generateMissingHostedFieldsError((_a = {\n        components: options.components\n      }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n    }\n    if (!hostedFields.current.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Clean all the fields before the rerender\n    if (cardFields) {\n      cardFields.teardown();\n    }\n    hostedFields.current.render({\n      // Call your server to set up the transaction\n      createOrder: createOrder,\n      fields: registeredFields.current,\n      installments: installments,\n      styles: styles\n    }).then(function (cardFieldsInstance) {\n      if (hostedFieldsContainerRef.current) {\n        setCardFields(cardFieldsInstance);\n      }\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalHostedFieldsProvider /> component. \".concat(err));\n      });\n    });\n  }, [loadingStatus, styles]); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: hostedFieldsContainerRef\n  }, isEligible ? React.createElement(PayPalHostedFieldsContext.Provider, {\n    value: {\n      cardFields: cardFields,\n      registerHostedField: registerHostedField\n    }\n  }, children) : notEligibleError);\n};\n\n/**\nThis `<PayPalHostedField />` component renders individual fields for [Hosted Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nIt relies on the `<PayPalHostedFieldsProvider />` parent component for managing state related to loading the JS SDK script\nand execute some validations before the rendering the fields.\n\nTo use the PayPal hosted fields you need to define at least three fields:\n\n- A card number field\n- The CVV code from the client card\n- The expiration date\n\nYou can define the expiration date as a single field similar to the example below,\nor you are able to define it in [two separate fields](https://paypal.github.io/react-paypal-js//?path=/docs/paypal-paypalhostedfields--expiration-date). One for the month and second for year.\n\nNote: Take care when using multiple instances of the PayPal Hosted Fields on the same page.\nThe component will fail to render when any of the selectors return more than one element.\n*/\nvar PayPalHostedField = function (_a) {\n  var hostedFieldType = _a.hostedFieldType,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    options = _a.options,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    props = __rest$1(_a, [\"hostedFieldType\", \"options\"]);\n  var hostedFieldContext = useContext(PayPalHostedFieldsContext);\n  useEffect(function () {\n    var _a;\n    if (!(hostedFieldContext === null || hostedFieldContext === void 0 ? void 0 : hostedFieldContext.registerHostedField)) {\n      throw new Error(\"The HostedField cannot be register in the PayPalHostedFieldsProvider parent component\");\n    }\n    // Register in the parent provider\n    hostedFieldContext.registerHostedField((_a = {}, _a[hostedFieldType] = {\n      selector: options.selector,\n      placeholder: options.placeholder,\n      type: options.type,\n      formatInput: options.formatInput,\n      maskInput: options.maskInput,\n      select: options.select,\n      maxlength: options.maxlength,\n      minlength: options.minlength,\n      prefill: options.prefill,\n      rejectUnsupportedCards: options.rejectUnsupportedCards\n    }, _a));\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", __assign({}, props));\n};\n\n/**\n * Throw an exception if the CardFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the card-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'card-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingCardFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",card-fields\") : \"card-fields\";\n  var errorMessage = \"Unable to render <PayPalCardFieldsProvider /> because window.\".concat(dataNamespace, \".CardFields is undefined.\");\n  if (!components.includes(\"card-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\nfunction ignore() {\n  return;\n}\nfunction hasChildren(container) {\n  var _a;\n  return !!((_a = container.current) === null || _a === void 0 ? void 0 : _a.children.length);\n}\nvar PayPalCardFieldsContext = createContext({\n  cardFieldsForm: null,\n  fields: {},\n  registerField: ignore,\n  unregisterField: ignore // implementation is inside hook and passed through the provider\n});\nvar usePayPalCardFields = function () {\n  return useContext(PayPalCardFieldsContext);\n};\nvar usePayPalCardFieldsRegistry = function () {\n  var _a = useState(null),\n    setError = _a[1];\n  var registeredFields = useRef({});\n  var registerField = function () {\n    var props = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      props[_i] = arguments[_i];\n    }\n    var fieldName = props[0],\n      options = props[1],\n      cardFields = props[2];\n    if (registeredFields.current[fieldName]) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_DUPLICATE_CHILDREN_ERROR);\n      });\n    }\n    registeredFields.current[fieldName] = cardFields === null || cardFields === void 0 ? void 0 : cardFields[fieldName](options);\n    return registeredFields.current[fieldName];\n  };\n  var unregisterField = function (fieldName) {\n    var field = registeredFields.current[fieldName];\n    if (field) {\n      field.close().catch(ignore);\n      delete registeredFields.current[fieldName];\n    }\n  };\n  return {\n    fields: registeredFields.current,\n    registerField: registerField,\n    unregisterField: unregisterField\n  };\n};\nvar FullWidthContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThe `<PayPalCardFieldsProvider />` is a context provider that is designed to support the rendering and state management of PayPal CardFields in your application.\n\nThe context provider will initialize the `CardFields` instance from the JS SDK and determine eligibility to render the CardField components. Once the `CardFields` are initialized, the context provider will manage the state of the `CardFields` instance as well as the reference to each individual card field.\n\nPassing the `inputEvents` and `style` props to the context provider will apply them to each of the individual field components.\n\nThe state managed by the provider is accessible through our custom hook `usePayPalCardFields`.\n\n*/\nvar PayPalCardFieldsProvider = function (_a) {\n  var children = _a.children,\n    props = __rest$1(_a, [\"children\"]);\n  var _b = usePayPalScriptReducer()[0],\n    isResolved = _b.isResolved,\n    options = _b.options;\n  var _c = usePayPalCardFieldsRegistry(),\n    fields = _c.fields,\n    registerField = _c.registerField,\n    unregisterField = _c.unregisterField;\n  var _d = useState(null),\n    cardFieldsForm = _d[0],\n    setCardFieldsForm = _d[1];\n  var cardFieldsInstance = useRef(null);\n  var _e = useState(false),\n    isEligible = _e[0],\n    setIsEligible = _e[1];\n  // We set the error inside state so that it can be caught by React's error boundary\n  var _f = useState(null),\n    setError = _f[1];\n  useEffect(function () {\n    var _a, _b, _c;\n    if (!isResolved) {\n      return;\n    }\n    try {\n      cardFieldsInstance.current = (_c = (_b = (_a = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE])).CardFields) === null || _b === void 0 ? void 0 : _b.call(_a, __assign({}, props))) !== null && _c !== void 0 ? _c : null;\n    } catch (error) {\n      setError(function () {\n        throw new Error(\"Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  \".concat(error));\n      });\n      return;\n    }\n    if (!cardFieldsInstance.current) {\n      setError(function () {\n        var _a;\n        throw new Error(generateMissingCardFieldsError((_a = {\n          components: options.components\n        }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n      });\n      return;\n    }\n    setIsEligible(cardFieldsInstance.current.isEligible());\n    setCardFieldsForm(cardFieldsInstance.current);\n    return function () {\n      setCardFieldsForm(null);\n      cardFieldsInstance.current = null;\n    };\n  }, [isResolved]); // eslint-disable-line react-hooks/exhaustive-deps\n  if (!isEligible) {\n    // TODO: What should be returned here?\n    return React.createElement(\"div\", null);\n  }\n  return React.createElement(FullWidthContainer, null, React.createElement(PayPalCardFieldsContext.Provider, {\n    value: {\n      cardFieldsForm: cardFieldsForm,\n      fields: fields,\n      registerField: registerField,\n      unregisterField: unregisterField\n    }\n  }, children));\n};\nvar PayPalCardField = function (_a) {\n  var className = _a.className,\n    fieldName = _a.fieldName,\n    options = __rest$1(_a, [\"className\", \"fieldName\"]);\n  var _b = usePayPalCardFields(),\n    cardFieldsForm = _b.cardFieldsForm,\n    registerField = _b.registerField,\n    unregisterField = _b.unregisterField;\n  var containerRef = useRef(null);\n  // Set errors is state so that they can be caught by React's error boundary\n  var _c = useState(null),\n    setError = _c[1];\n  function closeComponent() {\n    unregisterField(fieldName);\n  }\n  useEffect(function () {\n    if (!cardFieldsForm) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_CONTEXT_ERROR);\n      });\n      return closeComponent;\n    }\n    if (!containerRef.current) {\n      return closeComponent;\n    }\n    var registeredField = registerField(fieldName, options, cardFieldsForm);\n    registeredField === null || registeredField === void 0 ? void 0 : registeredField.render(containerRef.current).catch(function (err) {\n      if (!hasChildren(containerRef)) {\n        // Component no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // Component is still in the DOM\n      setError(function () {\n        throw new Error(\"Failed to render <PayPal\".concat(fieldName, \" /> component. \").concat(err));\n      });\n    });\n    return closeComponent;\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: containerRef,\n    className: className\n  });\n};\nvar PayPalNameField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NameField\"\n  }, options));\n};\nvar PayPalNumberField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NumberField\"\n  }, options));\n};\nvar PayPalExpiryField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"ExpiryField\"\n  }, options));\n};\nvar PayPalCVVField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"CVVField\"\n  }, options));\n};\nvar FlexContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      display: \"flex\",\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThis `<PayPalCardFieldsForm />` component renders the 4 individual fields for [Card Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nThis setup relies on the `<PayPalCardFieldsProvider />` parent component, which manages the state related to loading the JS SDK script and performs certain validations before rendering the fields.\n\n\n\nNote: If you want to have more granular control over the layout of how the fields are rendered, you can alternatively use our Individual Fields.\n*/\nvar PayPalCardFieldsForm = function (_a) {\n  var className = _a.className;\n  return React.createElement(\"div\", {\n    className: className\n  }, React.createElement(PayPalCardField, {\n    fieldName: \"NameField\"\n  }), React.createElement(PayPalCardField, {\n    fieldName: \"NumberField\"\n  }), React.createElement(FlexContainer, null, React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"ExpiryField\"\n  })), React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"CVVField\"\n  }))));\n};\nvar FUNDING$1 = {\n  PAYPAL: \"paypal\",\n  VENMO: \"venmo\",\n  APPLEPAY: \"applepay\",\n  ITAU: \"itau\",\n  CREDIT: \"credit\",\n  PAYLATER: \"paylater\",\n  CARD: \"card\",\n  IDEAL: \"ideal\",\n  SEPA: \"sepa\",\n  BANCONTACT: \"bancontact\",\n  GIROPAY: \"giropay\",\n  SOFORT: \"sofort\",\n  EPS: \"eps\",\n  MYBANK: \"mybank\",\n  P24: \"p24\",\n  PAYU: \"payu\",\n  BLIK: \"blik\",\n  TRUSTLY: \"trustly\",\n  OXXO: \"oxxo\",\n  BOLETO: \"boleto\",\n  BOLETOBANCARIO: \"boletobancario\",\n  WECHATPAY: \"wechatpay\",\n  MERCADOPAGO: \"mercadopago\",\n  MULTIBANCO: \"multibanco\",\n  SATISPAY: \"satispay\",\n  PAIDY: \"paidy\",\n  ZIMPLER: \"zimpler\",\n  MAXIMA: \"maxima\"\n};\n[FUNDING$1.IDEAL, FUNDING$1.BANCONTACT, FUNDING$1.GIROPAY, FUNDING$1.SOFORT, FUNDING$1.EPS, FUNDING$1.MYBANK, FUNDING$1.P24, FUNDING$1.PAYU, FUNDING$1.BLIK, FUNDING$1.TRUSTLY, FUNDING$1.OXXO, FUNDING$1.BOLETO, FUNDING$1.BOLETOBANCARIO, FUNDING$1.WECHATPAY, FUNDING$1.MERCADOPAGO, FUNDING$1.MULTIBANCO, FUNDING$1.SATISPAY, FUNDING$1.PAIDY, FUNDING$1.MAXIMA, FUNDING$1.ZIMPLER];\n\n// We do not re-export `FUNDING` from the `sdk-constants` module\n// directly because it has no type definitions.\n//\n// See https://github.com/paypal/react-paypal-js/issues/125\nvar FUNDING = FUNDING$1;\nexport { BraintreePayPalButtons, DISPATCH_ACTION, FUNDING, PAYPAL_HOSTED_FIELDS_TYPES, PayPalButtons, PayPalCVVField, PayPalCardFieldsContext, PayPalCardFieldsForm, PayPalCardFieldsProvider, PayPalExpiryField, PayPalHostedField, PayPalHostedFieldsProvider, PayPalMarks, PayPalMessages, PayPalNameField, PayPalNumberField, PayPalScriptProvider, SCRIPT_LOADING_STATE, ScriptContext, destroySDKScript, getScriptID, scriptReducer, usePayPalCardFields, usePayPalHostedFields, usePayPalScriptReducer, useScriptProviderContext };\n"], "mappings": ";;;;;;;;AAgBA,mBAA0F;AAO1F,IAAI;AAAA,CACH,SAAUA,uBAAsB;AAC/B,EAAAA,sBAAqB,SAAS,IAAI;AAClC,EAAAA,sBAAqB,SAAS,IAAI;AAClC,EAAAA,sBAAqB,UAAU,IAAI;AACnC,EAAAA,sBAAqB,UAAU,IAAI;AACrC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAMtD,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgB,gBAAgB,IAAI;AACpC,EAAAA,iBAAgB,eAAe,IAAI;AACnC,EAAAA,iBAAgB,wBAAwB,IAAI;AAC9C,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAM5C,IAAI;AAAA,CACH,SAAUC,6BAA4B;AACrC,EAAAA,4BAA2B,QAAQ,IAAI;AACvC,EAAAA,4BAA2B,KAAK,IAAI;AACpC,EAAAA,4BAA2B,iBAAiB,IAAI;AAChD,EAAAA,4BAA2B,kBAAkB,IAAI;AACjD,EAAAA,4BAA2B,iBAAiB,IAAI;AAChD,EAAAA,4BAA2B,aAAa,IAAI;AAC9C,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAClE,IAAI,WAAW,WAAY;AACzB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC/C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC7E;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AACA,SAAS,SAAS,GAAG,GAAG;AACtB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AACA,SAAS,cAAc,IAAI,MAAM,MAAM;AACrC,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACnF,QAAI,MAAM,EAAE,KAAK,OAAO;AACtB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;AAUA,IAAI,YAAY;AAChB,IAAI,eAAe;AAAA,EACjB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,6BAA6B;AAAA,EAC7B,oBAAoB;AACtB;AACA,IAAI,oBAAoB;AAIxB,IAAI,8CAA8C;AAClD,IAAI,mBAAmB;AACvB,IAAI,mBAAmB,uCAAuC,OAAO,kBAAkB,mBAAmB;AAC1G,IAAI,mCAAmC,uCAAuC,OAAO,kBAAkB,4BAA4B;AAInI,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAIlC,IAAI,+BAA+B;AACnC,IAAI,yCAAyC;AAI7C,IAAI,gCAAgC;AACpC,IAAI,uCAAuC;AAC3C,IAAI,4BAA4B;AAUhC,SAAS,2BAA2B,WAAW;AAC7C,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AAEA,SAAO,OAAO,SAAS;AACzB;AASA,SAAS,4BAA4B,WAAW;AAC9C,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AAEA,SAAO,OAAO,SAAS;AACzB;AAOA,SAAS,QAAQ,KAAK;AACpB,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,QAAQ,IAAI,CAAC,EAAE,WAAW,CAAC,IAAI;AACnC,QAAI,IAAI,IAAI,CAAC,GAAG;AACd,eAAS,IAAI,IAAI,CAAC,EAAE,WAAW,CAAC,KAAK,IAAI;AAAA,IAC3C;AACA,YAAQ,OAAO,aAAa,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EACvD;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,IAAI;AAChC,MAAI,qBAAqB,GAAG,oBAC1B,kBAAkB,GAAG,iBACrB,KAAK,GAAG,wBACR,yBAAyB,OAAO,SAAS,KAAK,IAC9C,KAAK,GAAG,kBACR,mBAAmB,OAAO,SAAS,2BAA2B;AAChE,MAAI,4BAA4B,gBAAgB,OAAO,CAAC,EAAE,YAAY,EAAE,OAAO,gBAAgB,UAAU,CAAC,CAAC;AAC3G,MAAI,eAAe,qBAAqB,OAAO,oBAAoB,qBAAqB,EAAE,OAAO,kBAAkB,GAAG,EAAE,OAAO,2BAA2B,gBAAgB;AAG1K,MAAI,sBAAsB,OAAO,2BAA2B,WAAW,yBAAyB,uBAAuB,KAAK,GAAG;AAC/H,MAAI,CAAC,oBAAoB,SAAS,eAAe,GAAG;AAClD,QAAI,qBAAqB,CAAC,qBAAqB,eAAe,EAAE,OAAO,OAAO,EAAE,KAAK;AACrF,oBAAgB,4BAA4B,OAAO,iBAAiB,wEAAwE,IAAI,oDAAoD,OAAO,oBAAoB,QAAQ;AAAA,EACzO;AACA,SAAO;AACT;AAOA,SAAS,YAAY,SAAS;AAE5B,MAAI,KAAK,SACP,KAAK;AACP,KAAG,EAAE;AACL,MAAI,sBAAsB,SAAS,IAAI,CAAC,KAAK,EAAE,CAAC;AAChD,SAAO,mBAAmB,OAAO,QAAQ,KAAK,UAAU,mBAAmB,CAAC,CAAC;AAC/E;AAMA,SAAS,iBAAiB,qBAAqB;AAC7C,MAAI,aAAa,KAAK,SAAS,cAAc,UAAU,OAAO,WAAW,IAAK,EAAE,OAAO,qBAAqB,IAAK,CAAC;AAClH,MAAI,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY;AACjF,eAAW,WAAW,YAAY,UAAU;AAAA,EAC9C;AACF;AAQA,SAAS,cAAc,OAAO,QAAQ;AACpC,MAAI,IAAI;AACR,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK,gBAAgB;AACnB,UAAI,OAAO,OAAO,UAAU,UAAU;AACpC,eAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,UACnC,eAAe,OAAO,MAAM;AAAA,UAC5B,2BAA2B,OAAO,MAAM;AAAA,QAC1C,CAAC;AAAA,MACH;AACA,aAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,QACnC,eAAe,OAAO;AAAA,MACxB,CAAC;AAAA,IACH,KAAK,gBAAgB;AAEnB,uBAAiB,MAAM,QAAQ,SAAS,CAAC;AACzC,aAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,QACnC,eAAe,qBAAqB;AAAA,QACpC,SAAS,SAAS,UAAU,KAAK,CAAC,GAAG,GAAG,aAAa,2BAA2B,IAAI,aAAa,oBAAoB,KAAK,OAAO,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG,SAAS,IAAI,GAAG,OAAO,YAAY,OAAO,KAAK,CAAC,GAAG,GAAG;AAAA,MAC9M,CAAC;AAAA,IACH,KAAK,gBAAgB;AACnB,aAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,QACnC,iCAAiC,OAAO;AAAA,MAC1C,CAAC;AAAA,IACH,SACE;AACE,aAAO;AAAA,IACT;AAAA,EACJ;AACF;AAEA,IAAI,oBAAgB,4BAAc,IAAI;AAQtC,SAAS,gBAAgB,eAAe;AACtC,MAAI,QAAQ,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,cAAc,cAAc,cAAc,SAAS,WAAW,GAAG;AACvJ,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,6BAA6B;AAC/C;AAWA,IAAI,qCAAqC,SAAU,eAAe;AAChE,MAAI,IAAI;AACR,MAAI,GAAG,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,iBAAiB,MAAM,GAAG,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,kBAAkB,IAAI;AACnV,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC7D;AACA,SAAO;AACT;AASA,SAAS,yBAAyB;AAChC,MAAI,gBAAgB,oBAAgB,yBAAW,aAAa,CAAC;AAC7D,MAAI,uBAAuB,SAAS,SAAS,CAAC,GAAG,aAAa,GAAG;AAAA,IAC/D,WAAW,cAAc,kBAAkB,qBAAqB;AAAA,IAChE,WAAW,cAAc,kBAAkB,qBAAqB;AAAA,IAChE,YAAY,cAAc,kBAAkB,qBAAqB;AAAA,IACjE,YAAY,cAAc,kBAAkB,qBAAqB;AAAA,EACnE,CAAC;AACD,SAAO,CAAC,sBAAsB,cAAc,QAAQ;AACtD;AAMA,SAAS,2BAA2B;AAClC,MAAI,gBAAgB,mCAAmC,oBAAgB,yBAAW,aAAa,CAAC,CAAC;AACjG,SAAO,CAAC,eAAe,cAAc,QAAQ;AAC/C;AAGA,IAAI,gCAA4B,4BAAc,CAAC,CAAC;AAShD,SAAS,wBAAwB;AAC/B,aAAO,yBAAW,yBAAyB;AAC7C;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,eAAW,qBAAO,IAAI,MAAM,CAAC,GAAG;AAAA,IAClC,KAAK,SAAU,QAAQ,MAAM,UAAU;AASrC,UAAI,OAAO,OAAO,IAAI,MAAM,YAAY;AACtC,eAAO,WAAY;AACjB,cAAI,OAAO,CAAC;AACZ,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,iBAAK,EAAE,IAAI,UAAU,EAAE;AAAA,UACzB;AAEA,iBAAO,OAAO,IAAI,EAAE,MAAM,QAAQ,IAAI;AAAA,QACxC;AAAA,MACF;AACA,aAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAAA,IAC3C;AAAA,EACF,CAAC,CAAC;AACF,WAAS,UAAU,OAAO,OAAO,SAAS,SAAS,KAAK;AACxD,SAAO,SAAS;AAClB;AAMA,IAAI,gBAAgB,SAAU,IAAI;AAChC,MAAI;AACJ,MAAI,KAAK,GAAG,WACV,YAAY,OAAO,SAAS,KAAK,IACjC,KAAK,GAAG,UACR,WAAW,OAAO,SAAS,QAAQ,IACnC,WAAW,GAAG,UACd,KAAK,GAAG,eACR,gBAAgB,OAAO,SAAS,CAAC,IAAI,IACrC,cAAc,SAAS,IAAI,CAAC,aAAa,YAAY,YAAY,eAAe,CAAC;AACnF,MAAI,kBAAkB,WAAW;AAAA,IAC/B,SAAS;AAAA,EACX,IAAI,CAAC;AACL,MAAI,aAAa,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,WAAW,4BAA4B,EAAE,EAAE,KAAK;AAClG,MAAI,0BAAsB,qBAAO,IAAI;AACrC,MAAI,cAAU,qBAAO,IAAI;AACzB,MAAI,aAAa,cAAc,WAAW;AAC1C,MAAI,KAAK,uBAAuB,EAAE,CAAC,GACjC,aAAa,GAAG,YAChB,UAAU,GAAG;AACf,MAAI,SAAK,uBAAS,IAAI,GACpB,cAAc,GAAG,CAAC,GAClB,iBAAiB,GAAG,CAAC;AACvB,MAAI,SAAK,uBAAS,IAAI,GACpB,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AACtB,MAAI,SAAK,uBAAS,IAAI,GACpB,gBAAgB,GAAG,CAAC;AACtB,WAAS,wBAAwB;AAC/B,QAAI,QAAQ,YAAY,MAAM;AAC5B,cAAQ,QAAQ,MAAM,EAAE,MAAM,WAAY;AAAA,MAE1C,CAAC;AAAA,IACH;AAAA,EACF;AACA,OAAK,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AAC9E,YAAQ,QAAQ,YAAY;AAAA,MAC1B,SAAS,YAAY;AAAA,IACvB,CAAC;AAAA,EACH;AAEA,8BAAU,WAAY;AAEpB,QAAI,eAAe,OAAO;AACxB,aAAO;AAAA,IACT;AACA,QAAI,wBAAwB,2BAA2B,QAAQ,aAAa;AAE5E,QAAI,0BAA0B,UAAa,sBAAsB,YAAY,QAAW;AACtF,oBAAc,WAAY;AACxB,cAAM,IAAI,MAAM,qBAAqB;AAAA,UACnC,oBAAoB,cAAc;AAAA,UAClC,iBAAiB;AAAA,UACjB,wBAAwB,QAAQ;AAAA,UAChC,kBAAkB,QAAQ,aAAa,cAAc;AAAA,QACvD,CAAC,CAAC;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,SAAU,MAAM,SAAS;AAC7C,qBAAe,OAAO;AACtB,UAAI,OAAO,YAAY,WAAW,YAAY;AAC5C,oBAAY,OAAO,MAAM,OAAO;AAAA,MAClC;AAAA,IACF;AACA,QAAI;AACF,cAAQ,UAAU,sBAAsB,QAAQ,SAAS,SAAS,CAAC,GAAG,UAAU,GAAG;AAAA,QACjF,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ,SAAS,KAAK;AACZ,aAAO,cAAc,WAAY;AAC/B,cAAM,IAAI,MAAM,wEAAwE,OAAO,GAAG,CAAC;AAAA,MACrG,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,QAAQ,WAAW,MAAM,OAAO;AAC1C,oBAAc,KAAK;AACnB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,oBAAoB,SAAS;AAChC,aAAO;AAAA,IACT;AACA,YAAQ,QAAQ,OAAO,oBAAoB,OAAO,EAAE,MAAM,SAAU,KAAK;AAEvE,UAAI,oBAAoB,YAAY,QAAQ,oBAAoB,QAAQ,SAAS,WAAW,GAAG;AAE7F;AAAA,MACF;AAEA,oBAAc,WAAY;AACxB,cAAM,IAAI,MAAM,iDAAiD,OAAO,GAAG,CAAC;AAAA,MAC9E,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EAET,GAAG,cAAc,cAAc,CAAC,UAAU,GAAG,eAAe,IAAI,GAAG,CAAC,YAAY,aAAa,GAAG,KAAK,CAAC;AAEtG,8BAAU,WAAY;AACpB,QAAI,gBAAgB,MAAM;AACxB;AAAA,IACF;AACA,QAAI,aAAa,MAAM;AACrB,kBAAY,QAAQ,EAAE,MAAM,WAAY;AAAA,MAExC,CAAC;AAAA,IACH,OAAO;AACL,kBAAY,OAAO,EAAE,MAAM,WAAY;AAAA,MAEvC,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,CAAC;AAC1B,SAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,aAAa,aAAAA,QAAM,cAAc,OAAO;AAAA,IACvF,KAAK;AAAA,IACL,OAAO;AAAA,IACP,WAAW;AAAA,EACb,CAAC,IAAI,QAAQ;AACf;AACA,cAAc,cAAc;AAC5B,SAAS,OAAO,GAAG,GAAG;AACpB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAKA,SAAS,WAAW,KAAK,YAAY;AACnC,MAAI,gBAAgB,SAAS,cAAc,eAAgB,OAAO,KAAK,IAAK,CAAC;AAC7E,MAAI,kBAAkB,KAAM,QAAO;AACnC,MAAI,aAAa,oBAAoB,KAAK,UAAU;AACpD,MAAI,qBAAqB,cAAc,UAAU;AACjD,SAAO,mBAAmB,QAAQ;AAClC,MAAI,OAAO,KAAK,mBAAmB,OAAO,EAAE,WAAW,OAAO,KAAK,WAAW,OAAO,EAAE,QAAQ;AAC7F,WAAO;AAAA,EACT;AACA,MAAI,eAAe;AACnB,SAAO,KAAK,mBAAmB,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC7D,QAAI,mBAAmB,QAAQ,GAAG,MAAM,WAAW,QAAQ,GAAG,GAAG;AAC/D,qBAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO,eAAe,gBAAgB;AACxC;AACA,SAAS,oBAAoB,IAAI;AAC/B,MAAI,MAAM,GAAG,KACX,aAAa,GAAG,YAChB,YAAY,GAAG,WACf,UAAU,GAAG;AACf,MAAI,YAAY,oBAAoB,KAAK,UAAU;AACnD,YAAU,UAAU;AACpB,YAAU,SAAS;AACnB,WAAS,KAAK,aAAa,WAAW,SAAS,KAAK,iBAAiB;AACvE;AACA,SAAS,eAAe,IAAI;AAC1B,MAAI,mBAAmB,GAAG,YACxB,cAAc,GAAG,aACjB,UAAU,OAAO,IAAI,CAAC,cAAc,aAAa,CAAC;AACpD,MAAI,aAAa,oBAAoB,kBAAkB,WAAW;AAClE,MAAI,yBAAyB;AAC7B,MAAI,KAAK,OAAO,KAAK,sBAAsB,EAAE,OAAO,SAAU,KAAK;AAC/D,WAAO,OAAO,uBAAuB,GAAG,MAAM,eAAe,uBAAuB,GAAG,MAAM,QAAQ,uBAAuB,GAAG,MAAM;AAAA,EACvI,CAAC,EAAE,OAAO,SAAU,aAAa,KAAK;AACpC,QAAI,QAAQ,uBAAuB,GAAG,EAAE,SAAS;AACjD,UAAM,qBAAqB,GAAG;AAC9B,QAAI,IAAI,UAAU,GAAG,CAAC,MAAM,UAAU,QAAQ,eAAe;AAC3D,kBAAY,WAAW,GAAG,IAAI;AAAA,IAChC,OAAO;AACL,kBAAY,YAAY,GAAG,IAAI;AAAA,IACjC;AACA,WAAO;AAAA,EACT,GAAG;AAAA,IACD,aAAa,CAAC;AAAA,IACd,YAAY,CAAC;AAAA,EACf,CAAC,GACD,cAAc,GAAG,aACjB,aAAa,GAAG;AAClB,MAAI,YAAY,aAAa,KAAK,YAAY,aAAa,EAAE,QAAQ,GAAG,MAAM,IAAI;AAChF,eAAW,kBAAkB,IAAI,YAAY,aAAa;AAC1D,gBAAY,aAAa,IAAI;AAAA,EAC/B;AACA,SAAO;AAAA,IACL,KAAK,GAAG,OAAO,YAAY,GAAG,EAAE,OAAO,oBAAoB,WAAW,CAAC;AAAA,IACvE;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,KAAK;AACjC,MAAI,WAAW,SAAU,OAAO,cAAc;AAC5C,YAAQ,eAAe,MAAM,MAAM,MAAM,YAAY;AAAA,EACvD;AACA,SAAO,IAAI,QAAQ,0BAA0B,QAAQ;AACvD;AACA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,cAAc;AAClB,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,QAAI,YAAY,WAAW,EAAG,gBAAe;AAC7C,mBAAe,MAAM,MAAM,OAAO,GAAG;AAAA,EACvC,CAAC;AACD,SAAO;AACT;AACA,SAAS,kBAAkB,aAAa;AACtC,SAAO,gBAAgB,YAAY,0CAA0C;AAC/E;AACA,SAAS,oBAAoB,KAAK,YAAY;AAC5C,MAAI,eAAe,QAAQ;AACzB,iBAAa,CAAC;AAAA,EAChB;AACA,MAAI,YAAY,SAAS,cAAc,QAAQ;AAC/C,YAAU,MAAM;AAChB,SAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC7C,cAAU,aAAa,KAAK,WAAW,GAAG,CAAC;AAC3C,QAAI,QAAQ,kBAAkB;AAC5B,gBAAU,aAAa,SAAS,WAAW,gBAAgB,CAAC;AAAA,IAC9D;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,WAAW,SAAS,iBAAiB;AAC5C,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,oBAAkB,SAAS,eAAe;AAC1C,MAAI,OAAO,aAAa,YAAa,QAAO,gBAAgB,QAAQ,IAAI;AACxE,MAAI,KAAK,eAAe,OAAO,GAC7B,MAAM,GAAG,KACT,aAAa,GAAG;AAClB,MAAI,YAAY,WAAW,gBAAgB,KAAK;AAChD,MAAI,0BAA0B,yBAAyB,SAAS;AAChE,MAAI,CAAC,WAAW,qBAAqB,GAAG;AACtC,eAAW,qBAAqB,IAAI;AAAA,EACtC;AACA,MAAI,WAAW,KAAK,UAAU,KAAK,yBAAyB;AAC1D,WAAO,gBAAgB,QAAQ,uBAAuB;AAAA,EACxD;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,EACF,GAAG,eAAe,EAAE,KAAK,WAAY;AACnC,QAAI,qBAAqB,yBAAyB,SAAS;AAC3D,QAAI,oBAAoB;AACtB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,cAAc,OAAO,WAAW,oCAAoC,CAAC;AAAA,EACvF,CAAC;AACH;AACA,SAAS,iBAAiB,SAAS,iBAAiB;AAClD,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,oBAAkB,SAAS,eAAe;AAC1C,MAAI,MAAM,QAAQ,KAChB,aAAa,QAAQ;AACvB,MAAI,OAAO,QAAQ,YAAY,IAAI,WAAW,GAAG;AAC/C,UAAM,IAAI,MAAM,cAAc;AAAA,EAChC;AACA,MAAI,OAAO,eAAe,eAAe,OAAO,eAAe,UAAU;AACvE,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACxD;AACA,SAAO,IAAI,gBAAgB,SAAU,SAAS,QAAQ;AACpD,QAAI,OAAO,aAAa,YAAa,QAAO,QAAQ;AACpD,wBAAoB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,WAAW,WAAY;AACrB,eAAO,QAAQ;AAAA,MACjB;AAAA,MACA,SAAS,WAAY;AACnB,YAAI,eAAe,IAAI,MAAM,eAAgB,OAAO,KAAK,2FAA4F,CAAC;AACtJ,eAAO,OAAO,YAAY;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,yBAAyB,WAAW;AAC3C,SAAO,OAAO,SAAS;AACzB;AACA,SAAS,kBAAkB,SAAS,iBAAiB;AACnD,MAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACnD,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AACA,MAAI,cAAc,QAAQ;AAC1B,MAAI,eAAe,gBAAgB,gBAAgB,gBAAgB,WAAW;AAC5E,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACtF;AACA,MAAI,OAAO,oBAAoB,eAAe,OAAO,oBAAoB,YAAY;AACnF,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AACF;AAQA,IAAI,4BAA4B,SAAU,iBAAiB;AACzD,MAAI,IAAI;AACR,MAAI,SAAS,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,cAAc,SAAS,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,YAAY;AAC5V,UAAM,IAAI,MAAM,yEAAyE;AAAA,EAC3F;AACA,SAAO;AACT;AAQA,IAAI,kBAAkB,SAAU,aAAa,wBAAwB;AACnE,MAAI,iBAAiB,YAAY;AACjC,MAAI,4BAA4B,YAAY;AAC5C,MAAI,eAAe,YAAY;AAC/B,MAAI,OAAO,mBAAmB,YAAY;AACxC,gBAAY,cAAc,SAAU,MAAM,SAAS;AACjD,aAAO,eAAe,MAAM,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG;AAAA,QAC1D,WAAW;AAAA,MACb,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,MAAI,OAAO,8BAA8B,YAAY;AACnD,gBAAY,yBAAyB,SAAU,MAAM,SAAS;AAC5D,aAAO,0BAA0B,MAAM,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG;AAAA,QACrE,WAAW;AAAA,MACb,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,MAAI,OAAO,iBAAiB,YAAY;AACtC,gBAAY,YAAY,SAAU,MAAM,SAAS;AAC/C,aAAO,aAAa,MAAM,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG;AAAA,QACxD,WAAW;AAAA,MACb,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,SAAO,SAAS,CAAC,GAAG,WAAW;AACjC;AAiBA,IAAI,wBAAwB,SAAU,iBAAiB;AACrD,MAAI,mBAAmB,0BAA0B,eAAe,GAAG;AACjE,WAAO,QAAQ,QAAQ,eAAe;AAAA,EACxC;AACA,SAAO,QAAQ,IAAI,CAAC,iBAAiB;AAAA,IACnC,KAAK;AAAA,EACP,CAAC,GAAG,iBAAiB;AAAA,IACnB,KAAK;AAAA,EACP,CAAC,CAAC,CAAC,EAAE,KAAK,WAAY;AACpB,WAAO,4BAA4B;AAAA,EACrC,CAAC;AACH;AAWA,IAAI,yBAAyB,SAAU,IAAI;AACzC,MAAI,KAAK,GAAG,WACV,YAAY,OAAO,SAAS,KAAK,IACjC,KAAK,GAAG,UACR,WAAW,OAAO,SAAS,QAAQ,IACnC,WAAW,GAAG,UACd,KAAK,GAAG,eACR,gBAAgB,OAAO,SAAS,CAAC,IAAI,IACrC,qBAAqB,GAAG,oBACxB,oBAAoB,GAAG,mBACvB,cAAc,SAAS,IAAI,CAAC,aAAa,YAAY,YAAY,iBAAiB,sBAAsB,mBAAmB,CAAC;AAC9H,MAAI,SAAK,uBAAS,IAAI,GACpB,gBAAgB,GAAG,CAAC;AACtB,MAAI,KAAK,yBAAyB,GAChC,kBAAkB,GAAG,CAAC,GACtB,WAAW,GAAG,CAAC;AACjB,8BAAU,WAAY;AACpB,0BAAsB,kBAAkB,EAAE,KAAK,SAAU,WAAW;AAClE,UAAI,wBAAwB,gBAAgB,QAAQ,aAAa,kBAAkB;AACnF,UAAI,cAAc,gBAAgB,QAAQ,aAAa,iBAAiB;AACxE,aAAO,UAAU,OAAO,OAAO;AAAA,QAC7B,eAAe,yBAAyB;AAAA,MAC1C,CAAC,EAAE,KAAK,SAAU,gBAAgB;AAChC,YAAI,eAAe,oBAAoB;AAAA,UACrC;AAAA,QACF,IAAI,CAAC;AACL,eAAO,UAAU,eAAe,OAAO,SAAS,SAAS,CAAC,GAAG,YAAY,GAAG;AAAA,UAC1E,QAAQ;AAAA,QACV,CAAC,CAAC;AAAA,MACJ,CAAC,EAAE,KAAK,SAAU,wBAAwB;AACxC,iBAAS;AAAA,UACP,MAAM,gBAAgB;AAAA,UACtB,OAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,oBAAc,WAAY;AACxB,cAAM,IAAI,MAAM,GAAG,OAAO,mBAAmB,GAAG,EAAE,OAAO,GAAG,CAAC;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,EAEH,GAAG,CAAC,gBAAgB,OAAO,CAAC;AAC5B,SAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,gBAAgB,mCAAmC,aAAAA,QAAM,cAAc,eAAe,SAAS;AAAA,IAC9I;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,gBAAgB,aAAa,gBAAgB,+BAA+B,CAAC,GAAG,QAAQ,CAAC;AAC9F;AAUA,IAAI,cAAc,SAAU,IAAI;AAC9B,MAAI,KAAK,GAAG,WACV,YAAY,OAAO,SAAS,KAAK,IACjC,WAAW,GAAG,UACd,YAAY,SAAS,IAAI,CAAC,aAAa,UAAU,CAAC;AACpD,MAAI,KAAK,uBAAuB,EAAE,CAAC,GACjC,aAAa,GAAG,YAChB,UAAU,GAAG;AACf,MAAI,uBAAmB,qBAAO,IAAI;AAClC,MAAI,SAAK,uBAAS,IAAI,GACpB,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AACtB,MAAI,SAAK,uBAAS,IAAI,GACpB,gBAAgB,GAAG,CAAC;AAItB,MAAI,mBAAmB,SAAU,MAAM;AACrC,QAAI,UAAU,iBAAiB;AAE/B,QAAI,CAAC,WAAW,CAAC,KAAK,WAAW,GAAG;AAClC,aAAO,cAAc,KAAK;AAAA,IAC5B;AAEA,QAAI,QAAQ,YAAY;AACtB,cAAQ,YAAY,QAAQ,UAAU;AAAA,IACxC;AACA,SAAK,OAAO,OAAO,EAAE,MAAM,SAAU,KAAK;AAExC,UAAI,YAAY,QAAQ,QAAQ,SAAS,WAAW,GAAG;AAErD;AAAA,MACF;AAEA,oBAAc,WAAY;AACxB,cAAM,IAAI,MAAM,+CAA+C,OAAO,GAAG,CAAC;AAAA,MAC5E,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,8BAAU,WAAY;AAEpB,QAAI,eAAe,OAAO;AACxB;AAAA,IACF;AACA,QAAI,wBAAwB,2BAA2B,QAAQ,aAAa,cAAc,CAAC;AAE3F,QAAI,0BAA0B,UAAa,sBAAsB,UAAU,QAAW;AACpF,aAAO,cAAc,WAAY;AAC/B,cAAM,IAAI,MAAM,qBAAqB;AAAA,UACnC,oBAAoB,YAAY;AAAA,UAChC,iBAAiB;AAAA,UACjB,wBAAwB,QAAQ;AAAA,UAChC,kBAAkB,QAAQ,aAAa,cAAc;AAAA,QACvD,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AACA,qBAAiB,sBAAsB,MAAM,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EAEvE,GAAG,CAAC,YAAY,UAAU,aAAa,CAAC;AACxC,SAAO,aAAAA,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,aAAa,aAAAA,QAAM,cAAc,OAAO;AAAA,IACvF,KAAK;AAAA,IACL;AAAA,EACF,CAAC,IAAI,QAAQ;AACf;AACA,YAAY,cAAc;AAM1B,IAAI,iBAAiB,SAAU,IAAI;AACjC,MAAI,KAAK,GAAG,WACV,YAAY,OAAO,SAAS,KAAK,IACjC,KAAK,GAAG,eACR,gBAAgB,OAAO,SAAS,CAAC,IAAI,IACrC,eAAe,SAAS,IAAI,CAAC,aAAa,eAAe,CAAC;AAC5D,MAAI,KAAK,uBAAuB,EAAE,CAAC,GACjC,aAAa,GAAG,YAChB,UAAU,GAAG;AACf,MAAI,2BAAuB,qBAAO,IAAI;AACtC,MAAI,eAAW,qBAAO,IAAI;AAC1B,MAAI,SAAK,uBAAS,IAAI,GACpB,gBAAgB,GAAG,CAAC;AACtB,8BAAU,WAAY;AAEpB,QAAI,eAAe,OAAO;AACxB;AAAA,IACF;AACA,QAAI,wBAAwB,2BAA2B,QAAQ,aAAa,cAAc,CAAC;AAE3F,QAAI,0BAA0B,UAAa,sBAAsB,aAAa,QAAW;AACvF,aAAO,cAAc,WAAY;AAC/B,cAAM,IAAI,MAAM,qBAAqB;AAAA,UACnC,oBAAoB,eAAe;AAAA,UACnC,iBAAiB;AAAA,UACjB,wBAAwB,QAAQ;AAAA,UAChC,kBAAkB,QAAQ,aAAa,cAAc;AAAA,QACvD,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AACA,aAAS,UAAU,sBAAsB,SAAS,SAAS,CAAC,GAAG,YAAY,CAAC;AAC5E,aAAS,QAAQ,OAAO,qBAAqB,OAAO,EAAE,MAAM,SAAU,KAAK;AAEzE,UAAI,qBAAqB,YAAY,QAAQ,qBAAqB,QAAQ,SAAS,WAAW,GAAG;AAE/F;AAAA,MACF;AAEA,oBAAc,WAAY;AACxB,cAAM,IAAI,MAAM,kDAAkD,OAAO,GAAG,CAAC;AAAA,MAC/E,CAAC;AAAA,IACH,CAAC;AAAA,EAEH,GAAG,cAAc,CAAC,UAAU,GAAG,eAAe,IAAI,CAAC;AACnD,SAAO,aAAAA,QAAM,cAAc,OAAO;AAAA,IAChC,KAAK;AAAA,IACL;AAAA,EACF,CAAC;AACH;AACA,eAAe,cAAc;AAQ7B,IAAI,uBAAuB,SAAU,IAAI;AACvC,MAAI;AACJ,MAAI,KAAK,GAAG,SACV,UAAU,OAAO,SAAS;AAAA,IACxB,UAAU;AAAA,EACZ,IAAI,IACJ,WAAW,GAAG,UACd,KAAK,GAAG,cACR,eAAe,OAAO,SAAS,QAAQ;AACzC,MAAI,SAAK,yBAAW,eAAe;AAAA,IAC/B,SAAS,SAAS,SAAS,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,aAAa,mBAAmB,IAAI,aAAa,oBAAoB,GAAG,aAAa,2BAA2B,IAAI,aAAa,oBAAoB,GAAG,SAAS,IAAI,GAAG,OAAO,YAAY,OAAO,CAAC,GAAG,GAAG;AAAA,IAC/P,eAAe,eAAe,qBAAqB,UAAU,qBAAqB;AAAA,EACpF,CAAC,GACD,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,8BAAU,WAAY;AACpB,QAAI,iBAAiB,SAAS,MAAM,kBAAkB,qBAAqB,SAAS;AAClF,aAAO,SAAS;AAAA,QACd,MAAM,gBAAgB;AAAA,QACtB,OAAO,qBAAqB;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,QAAI,MAAM,kBAAkB,qBAAqB,SAAS;AACxD;AAAA,IACF;AACA,QAAI,eAAe;AACnB,eAAW,MAAM,OAAO,EAAE,KAAK,WAAY;AACzC,UAAI,cAAc;AAChB,iBAAS;AAAA,UACP,MAAM,gBAAgB;AAAA,UACtB,OAAO,qBAAqB;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,IACF,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,cAAQ,MAAM,GAAG,OAAO,mBAAmB,GAAG,EAAE,OAAO,GAAG,CAAC;AAC3D,UAAI,cAAc;AAChB,iBAAS;AAAA,UACP,MAAM,gBAAgB;AAAA,UACtB,OAAO;AAAA,YACL,OAAO,qBAAqB;AAAA,YAC5B,SAAS,OAAO,GAAG;AAAA,UACrB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO,WAAY;AACjB,qBAAe;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,MAAM,SAAS,cAAc,MAAM,aAAa,CAAC;AACrD,SAAO,aAAAA,QAAM,cAAc,cAAc,UAAU;AAAA,IACjD,OAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH,GAAG,QAAQ;AACb;AAUA,IAAI,0BAA0B,SAAU,cAAc;AACpD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe,CAAC;AAAA,EAClB;AACA,MAAI,uBAAmB,qBAAO,YAAY;AAC1C,MAAI,sBAAsB,SAAU,WAAW;AAC7C,qBAAiB,UAAU,SAAS,SAAS,CAAC,GAAG,iBAAiB,OAAO,GAAG,SAAS;AAAA,EACvF;AACA,SAAO,CAAC,kBAAkB,mBAAmB;AAC/C;AAYA,IAAI,mCAAmC,SAAU,IAAI;AACnD,MAAI,KAAK,GAAG,YACV,aAAa,OAAO,SAAS,KAAK,IAClC,KAAK,aAAa,gBAClB,KAAK,GAAG,EAAE,GACV,gBAAgB,OAAO,SAAS,2BAA2B;AAC7D,MAAI,qBAAqB,aAAa,GAAG,OAAO,YAAY,gBAAgB,IAAI;AAChF,MAAI,eAAe,kEAAkE,OAAO,eAAe,6BAA6B;AACxI,MAAI,CAAC,WAAW,SAAS,eAAe,GAAG;AACzC,oBAAgB,8JAA8J,OAAO,oBAAoB,MAAM;AAAA,EACjN;AACA,SAAO;AACT;AASA,IAAI,yBAAyB,SAAU,eAAe;AACpD,SAAO,CAAC,cAAc,SAAS,2BAA2B,eAAe,KAAK,CAAC,cAAc,SAAS,2BAA2B,gBAAgB,KAAK,CAAC,cAAc,SAAS,2BAA2B,eAAe;AAC1N;AAQA,IAAI,qBAAqB,SAAU,eAAe;AAChD,MAAI,CAAC,cAAc,SAAS,2BAA2B,MAAM,KAAK,CAAC,cAAc,SAAS,2BAA2B,GAAG,KAAK,uBAAuB,aAAa,GAAG;AAClK,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACF;AAOA,IAAI,sBAAsB,SAAU,eAAe;AACjD,MAAI,cAAc,WAAW,IAAI,IAAI,aAAa,EAAE,MAAM;AACxD,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACxD;AACF;AAWA,IAAI,8BAA8B,SAAU,kBAAkB;AAC5D,qBAAmB,gBAAgB;AACnC,sBAAoB,gBAAgB;AACtC;AAWA,IAAI,6BAA6B,SAAU,IAAI;AAC7C,MAAI,SAAS,GAAG,QACd,cAAc,GAAG,aACjB,mBAAmB,GAAG,kBACtB,WAAW,GAAG,UACd,eAAe,GAAG;AACpB,MAAI,KAAK,yBAAyB,EAAE,CAAC,GACnC,UAAU,GAAG,SACb,gBAAgB,GAAG;AACrB,MAAI,SAAK,uBAAS,IAAI,GACpB,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AACtB,MAAI,SAAK,uBAAS,GAChB,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AACtB,MAAI,SAAK,uBAAS,IAAI,GACpB,gBAAgB,GAAG,CAAC;AACtB,MAAI,+BAA2B,qBAAO,IAAI;AAC1C,MAAI,mBAAe,qBAAO;AAC1B,MAAI,KAAK,wBAAwB,GAC/B,mBAAmB,GAAG,CAAC,GACvB,sBAAsB,GAAG,CAAC;AAC5B,8BAAU,WAAY;AACpB,QAAIC;AACJ,gCAA4B,OAAO,KAAK,iBAAiB,OAAO,CAAC;AAEjE,QAAI,EAAE,kBAAkB,qBAAqB,WAAW;AACtD;AAAA,IACF;AAEA,iBAAa,UAAU,2BAA2B,QAAQ,aAAa,cAAc,CAAC,EAAE;AACxF,QAAI,CAAC,aAAa,SAAS;AACzB,YAAM,IAAI,MAAM,kCAAkCA,MAAK;AAAA,QACrD,YAAY,QAAQ;AAAA,MACtB,GAAGA,IAAG,aAAa,cAAc,IAAI,QAAQ,aAAa,cAAc,GAAGA,IAAG,CAAC;AAAA,IACjF;AACA,QAAI,CAAC,aAAa,QAAQ,WAAW,GAAG;AACtC,aAAO,cAAc,KAAK;AAAA,IAC5B;AAEA,QAAI,YAAY;AACd,iBAAW,SAAS;AAAA,IACtB;AACA,iBAAa,QAAQ,OAAO;AAAA;AAAA,MAE1B;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB;AAAA,MACA;AAAA,IACF,CAAC,EAAE,KAAK,SAAU,oBAAoB;AACpC,UAAI,yBAAyB,SAAS;AACpC,sBAAc,kBAAkB;AAAA,MAClC;AAAA,IACF,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,oBAAc,WAAY;AACxB,cAAM,IAAI,MAAM,8DAA8D,OAAO,GAAG,CAAC;AAAA,MAC3F,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,MAAM,CAAC;AAC1B,SAAO,aAAAD,QAAM,cAAc,OAAO;AAAA,IAChC,KAAK;AAAA,EACP,GAAG,aAAa,aAAAA,QAAM,cAAc,0BAA0B,UAAU;AAAA,IACtE,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,QAAQ,IAAI,gBAAgB;AACjC;AAmBA,IAAI,oBAAoB,SAAU,IAAI;AACpC,MAAI,kBAAkB,GAAG,iBAEvB,UAAU,GAAG,SAEb,QAAQ,SAAS,IAAI,CAAC,mBAAmB,SAAS,CAAC;AACrD,MAAI,yBAAqB,yBAAW,yBAAyB;AAC7D,8BAAU,WAAY;AACpB,QAAIC;AACJ,QAAI,EAAE,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,sBAAsB;AACrH,YAAM,IAAI,MAAM,uFAAuF;AAAA,IACzG;AAEA,uBAAmB,qBAAqBA,MAAK,CAAC,GAAGA,IAAG,eAAe,IAAI;AAAA,MACrE,UAAU,QAAQ;AAAA,MAClB,aAAa,QAAQ;AAAA,MACrB,MAAM,QAAQ;AAAA,MACd,aAAa,QAAQ;AAAA,MACrB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,MAChB,WAAW,QAAQ;AAAA,MACnB,WAAW,QAAQ;AAAA,MACnB,SAAS,QAAQ;AAAA,MACjB,wBAAwB,QAAQ;AAAA,IAClC,GAAGA,IAAG;AAAA,EACR,GAAG,CAAC,CAAC;AACL,SAAO,aAAAD,QAAM,cAAc,OAAO,SAAS,CAAC,GAAG,KAAK,CAAC;AACvD;AAYA,IAAI,iCAAiC,SAAU,IAAI;AACjD,MAAI,KAAK,GAAG,YACV,aAAa,OAAO,SAAS,KAAK,IAClC,KAAK,aAAa,gBAClB,KAAK,GAAG,EAAE,GACV,gBAAgB,OAAO,SAAS,2BAA2B;AAC7D,MAAI,qBAAqB,aAAa,GAAG,OAAO,YAAY,cAAc,IAAI;AAC9E,MAAI,eAAe,gEAAgE,OAAO,eAAe,2BAA2B;AACpI,MAAI,CAAC,WAAW,SAAS,aAAa,GAAG;AACvC,oBAAgB,4JAA4J,OAAO,oBAAoB,MAAM;AAAA,EAC/M;AACA,SAAO;AACT;AACA,SAAS,SAAS;AAChB;AACF;AACA,SAAS,YAAY,WAAW;AAC9B,MAAI;AACJ,SAAO,CAAC,GAAG,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AACtF;AACA,IAAI,8BAA0B,4BAAc;AAAA,EAC1C,gBAAgB;AAAA,EAChB,QAAQ,CAAC;AAAA,EACT,eAAe;AAAA,EACf,iBAAiB;AAAA;AACnB,CAAC;AACD,IAAI,sBAAsB,WAAY;AACpC,aAAO,yBAAW,uBAAuB;AAC3C;AACA,IAAI,8BAA8B,WAAY;AAC5C,MAAI,SAAK,uBAAS,IAAI,GACpB,WAAW,GAAG,CAAC;AACjB,MAAI,uBAAmB,qBAAO,CAAC,CAAC;AAChC,MAAI,gBAAgB,WAAY;AAC9B,QAAI,QAAQ,CAAC;AACb,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,YAAM,EAAE,IAAI,UAAU,EAAE;AAAA,IAC1B;AACA,QAAI,YAAY,MAAM,CAAC,GACrB,UAAU,MAAM,CAAC,GACjB,aAAa,MAAM,CAAC;AACtB,QAAI,iBAAiB,QAAQ,SAAS,GAAG;AACvC,eAAS,WAAY;AACnB,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD,CAAC;AAAA,IACH;AACA,qBAAiB,QAAQ,SAAS,IAAI,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,SAAS,EAAE,OAAO;AAC3H,WAAO,iBAAiB,QAAQ,SAAS;AAAA,EAC3C;AACA,MAAI,kBAAkB,SAAU,WAAW;AACzC,QAAI,QAAQ,iBAAiB,QAAQ,SAAS;AAC9C,QAAI,OAAO;AACT,YAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,aAAO,iBAAiB,QAAQ,SAAS;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AAAA,IACL,QAAQ,iBAAiB;AAAA,IACzB;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,qBAAqB,SAAU,IAAI;AACrC,MAAI,WAAW,GAAG;AAClB,SAAO,aAAAA,QAAM,cAAc,OAAO;AAAA,IAChC,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF,GAAG,QAAQ;AACb;AAYA,IAAI,2BAA2B,SAAU,IAAI;AAC3C,MAAI,WAAW,GAAG,UAChB,QAAQ,SAAS,IAAI,CAAC,UAAU,CAAC;AACnC,MAAI,KAAK,uBAAuB,EAAE,CAAC,GACjC,aAAa,GAAG,YAChB,UAAU,GAAG;AACf,MAAI,KAAK,4BAA4B,GACnC,SAAS,GAAG,QACZ,gBAAgB,GAAG,eACnB,kBAAkB,GAAG;AACvB,MAAI,SAAK,uBAAS,IAAI,GACpB,iBAAiB,GAAG,CAAC,GACrB,oBAAoB,GAAG,CAAC;AAC1B,MAAI,yBAAqB,qBAAO,IAAI;AACpC,MAAI,SAAK,uBAAS,KAAK,GACrB,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AAEtB,MAAI,SAAK,uBAAS,IAAI,GACpB,WAAW,GAAG,CAAC;AACjB,8BAAU,WAAY;AACpB,QAAIC,KAAIC,KAAIC;AACZ,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,QAAI;AACF,yBAAmB,WAAWA,OAAMD,OAAMD,MAAK,2BAA2B,QAAQ,aAAa,cAAc,CAAC,GAAG,gBAAgB,QAAQC,QAAO,SAAS,SAASA,IAAG,KAAKD,KAAI,SAAS,CAAC,GAAG,KAAK,CAAC,OAAO,QAAQE,QAAO,SAASA,MAAK;AAAA,IACvO,SAAS,OAAO;AACd,eAAS,WAAY;AACnB,cAAM,IAAI,MAAM,mFAAmF,OAAO,KAAK,CAAC;AAAA,MAClH,CAAC;AACD;AAAA,IACF;AACA,QAAI,CAAC,mBAAmB,SAAS;AAC/B,eAAS,WAAY;AACnB,YAAIF;AACJ,cAAM,IAAI,MAAM,gCAAgCA,MAAK;AAAA,UACnD,YAAY,QAAQ;AAAA,QACtB,GAAGA,IAAG,aAAa,cAAc,IAAI,QAAQ,aAAa,cAAc,GAAGA,IAAG,CAAC;AAAA,MACjF,CAAC;AACD;AAAA,IACF;AACA,kBAAc,mBAAmB,QAAQ,WAAW,CAAC;AACrD,sBAAkB,mBAAmB,OAAO;AAC5C,WAAO,WAAY;AACjB,wBAAkB,IAAI;AACtB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AACf,MAAI,CAAC,YAAY;AAEf,WAAO,aAAAD,QAAM,cAAc,OAAO,IAAI;AAAA,EACxC;AACA,SAAO,aAAAA,QAAM,cAAc,oBAAoB,MAAM,aAAAA,QAAM,cAAc,wBAAwB,UAAU;AAAA,IACzG,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,QAAQ,CAAC;AACd;AACA,IAAI,kBAAkB,SAAU,IAAI;AAClC,MAAI,YAAY,GAAG,WACjB,YAAY,GAAG,WACf,UAAU,SAAS,IAAI,CAAC,aAAa,WAAW,CAAC;AACnD,MAAI,KAAK,oBAAoB,GAC3B,iBAAiB,GAAG,gBACpB,gBAAgB,GAAG,eACnB,kBAAkB,GAAG;AACvB,MAAI,mBAAe,qBAAO,IAAI;AAE9B,MAAI,SAAK,uBAAS,IAAI,GACpB,WAAW,GAAG,CAAC;AACjB,WAAS,iBAAiB;AACxB,oBAAgB,SAAS;AAAA,EAC3B;AACA,8BAAU,WAAY;AACpB,QAAI,CAAC,gBAAgB;AACnB,eAAS,WAAY;AACnB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C,CAAC;AACD,aAAO;AAAA,IACT;AACA,QAAI,CAAC,aAAa,SAAS;AACzB,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,cAAc,WAAW,SAAS,cAAc;AACtE,wBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,OAAO,aAAa,OAAO,EAAE,MAAM,SAAU,KAAK;AAClI,UAAI,CAAC,YAAY,YAAY,GAAG;AAE9B;AAAA,MACF;AAEA,eAAS,WAAY;AACnB,cAAM,IAAI,MAAM,2BAA2B,OAAO,WAAW,iBAAiB,EAAE,OAAO,GAAG,CAAC;AAAA,MAC7F,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,aAAAA,QAAM,cAAc,OAAO;AAAA,IAChC,KAAK;AAAA,IACL;AAAA,EACF,CAAC;AACH;AACA,IAAI,kBAAkB,SAAU,SAAS;AACvC,SAAO,aAAAA,QAAM,cAAc,iBAAiB,SAAS;AAAA,IACnD,WAAW;AAAA,EACb,GAAG,OAAO,CAAC;AACb;AACA,IAAI,oBAAoB,SAAU,SAAS;AACzC,SAAO,aAAAA,QAAM,cAAc,iBAAiB,SAAS;AAAA,IACnD,WAAW;AAAA,EACb,GAAG,OAAO,CAAC;AACb;AACA,IAAI,oBAAoB,SAAU,SAAS;AACzC,SAAO,aAAAA,QAAM,cAAc,iBAAiB,SAAS;AAAA,IACnD,WAAW;AAAA,EACb,GAAG,OAAO,CAAC;AACb;AACA,IAAI,iBAAiB,SAAU,SAAS;AACtC,SAAO,aAAAA,QAAM,cAAc,iBAAiB,SAAS;AAAA,IACnD,WAAW;AAAA,EACb,GAAG,OAAO,CAAC;AACb;AACA,IAAI,gBAAgB,SAAU,IAAI;AAChC,MAAI,WAAW,GAAG;AAClB,SAAO,aAAAA,QAAM,cAAc,OAAO;AAAA,IAChC,OAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF,GAAG,QAAQ;AACb;AAUA,IAAI,uBAAuB,SAAU,IAAI;AACvC,MAAI,YAAY,GAAG;AACnB,SAAO,aAAAA,QAAM,cAAc,OAAO;AAAA,IAChC;AAAA,EACF,GAAG,aAAAA,QAAM,cAAc,iBAAiB;AAAA,IACtC,WAAW;AAAA,EACb,CAAC,GAAG,aAAAA,QAAM,cAAc,iBAAiB;AAAA,IACvC,WAAW;AAAA,EACb,CAAC,GAAG,aAAAA,QAAM,cAAc,eAAe,MAAM,aAAAA,QAAM,cAAc,oBAAoB,MAAM,aAAAA,QAAM,cAAc,iBAAiB;AAAA,IAC9H,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,aAAAA,QAAM,cAAc,oBAAoB,MAAM,aAAAA,QAAM,cAAc,iBAAiB;AAAA,IACtF,WAAW;AAAA,EACb,CAAC,CAAC,CAAC,CAAC;AACN;AACA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AACV;AACA,CAAC,UAAU,OAAO,UAAU,YAAY,UAAU,SAAS,UAAU,QAAQ,UAAU,KAAK,UAAU,QAAQ,UAAU,KAAK,UAAU,MAAM,UAAU,MAAM,UAAU,SAAS,UAAU,MAAM,UAAU,QAAQ,UAAU,gBAAgB,UAAU,WAAW,UAAU,aAAa,UAAU,YAAY,UAAU,UAAU,UAAU,OAAO,UAAU,QAAQ,UAAU,OAAO;AAMtX,IAAI,UAAU;", "names": ["SCRIPT_LOADING_STATE", "DISPATCH_ACTION", "PAYPAL_HOSTED_FIELDS_TYPES", "__assign", "React", "React", "_a", "_b", "_c"]}