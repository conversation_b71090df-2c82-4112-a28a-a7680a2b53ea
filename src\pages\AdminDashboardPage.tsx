import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { Link } from 'react-router-dom'
import { ProductService } from '../services/productService'
import { OrderService } from '../services/orderService'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { 
  Package, 
  ShoppingCart, 
  Users, 
  DollarSign, 
  TrendingUp, 
  AlertCircle,
  Settings,
  BarChart3
} from 'lucide-react'
import { formatPrice } from '../lib/utils'
import { container, hoverLift } from '../styles/utils'
import type { Product, Order } from '../../types'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const PageDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing[6]};
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
`

const StatCard = styled(Card)`
  ${hoverLift}
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  background: white;
`

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const StatIcon = styled.div<{ $color: string }>`
  width: 3rem;
  height: 3rem;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ $color }) => $color};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
`

const StatValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

const ActionsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing[6]};
  }

  @media (min-width: 1280px) {
    grid-template-columns: repeat(3, 1fr);
  }
`

const ActionCard = styled(Card)`
  ${hoverLift}
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  background: white;
`

/**
 * Dashboard principal de administración
 */
export const AdminDashboardPage: React.FC = () => {
  const [stats, setStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    totalOrders: 0,
    pendingOrders: 0,
    totalRevenue: 0,
    lowStockProducts: 0
  })
  const [recentOrders, setRecentOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Cargar productos
      const products = await ProductService.getAllProducts()
      const activeProducts = products.filter(p => p.isActive)

      // Calcular productos con stock bajo (formatos con stock <= 5 y no infinito)
      const lowStockProducts = products.filter(p =>
        p.isActive && p.formats.some(f => f.stock <= 5 && f.stock !== -1)
      )
      
      // Cargar órdenes
      const orders = await OrderService.getAllOrders()
      const pendingOrders = orders.filter(o => o.status === 'pending')
      const paidOrders = orders.filter(o => o.status === 'paid' || o.status === 'shipped' || o.status === 'delivered')
      
      // Calcular ingresos totales
      const totalRevenue = paidOrders.reduce((sum, order) => sum + order.totalAmount, 0)
      
      // Órdenes recientes (últimas 5)
      const recentOrders = orders.slice(0, 5)

      setStats({
        totalProducts: products.length,
        activeProducts: activeProducts.length,
        totalOrders: orders.length,
        pendingOrders: pendingOrders.length,
        totalRevenue,
        lowStockProducts: lowStockProducts.length
      })
      
      setRecentOrders(recentOrders)
    } catch (error) {
      console.error('Error cargando datos del dashboard:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <PageContainer>
        <LoadingSpinner size="lg" className="h-64" />
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      {/* Header */}
      <PageHeader>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <PageTitle>Dashboard</PageTitle>
            <PageDescription>
              Resumen general del estado de la tienda
            </PageDescription>
          </div>
          <Button onClick={loadDashboardData}>
            Actualizar Datos
          </Button>
        </div>
      </PageHeader>

      {/* Estadísticas principales */}
      <StatsGrid>
        <StatCard>
          <CardHeader>
            <StatHeader>
              <CardTitle style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                Productos Totales
              </CardTitle>
              <StatIcon $color="#0072CE">
                <Package size={20} />
              </StatIcon>
            </StatHeader>
          </CardHeader>
          <CardContent>
            <StatValue>{stats.totalProducts}</StatValue>
            <StatLabel>{stats.activeProducts} activos</StatLabel>
          </CardContent>
        </StatCard>

        <StatCard>
          <CardHeader>
            <StatHeader>
              <CardTitle style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                Órdenes Totales
              </CardTitle>
              <StatIcon $color="#10B981">
                <ShoppingCart size={20} />
              </StatIcon>
            </StatHeader>
          </CardHeader>
          <CardContent>
            <StatValue>{stats.totalOrders}</StatValue>
            <StatLabel>{stats.pendingOrders} pendientes</StatLabel>
          </CardContent>
        </StatCard>

        <StatCard>
          <CardHeader>
            <StatHeader>
              <CardTitle style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                Ingresos Totales
              </CardTitle>
              <StatIcon $color="#F59E0B">
                <DollarSign size={20} />
              </StatIcon>
            </StatHeader>
          </CardHeader>
          <CardContent>
            <StatValue>{formatPrice(stats.totalRevenue)}</StatValue>
            <StatLabel>Órdenes completadas</StatLabel>
          </CardContent>
        </StatCard>

        <StatCard>
          <CardHeader>
            <StatHeader>
              <CardTitle style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                Stock Bajo
              </CardTitle>
              <StatIcon $color="#EF4444">
                <AlertCircle size={20} />
              </StatIcon>
            </StatHeader>
          </CardHeader>
          <CardContent>
            <StatValue style={{ color: '#EF4444' }}>{stats.lowStockProducts}</StatValue>
            <StatLabel>Productos con ≤5 unidades</StatLabel>
          </CardContent>
        </StatCard>
      </StatsGrid>

      <ActionsGrid>
        {/* Órdenes recientes */}
        <ActionCard>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="w-5 h-5" />
              Órdenes Recientes
            </CardTitle>
            <CardDescription>
              Las últimas órdenes recibidas
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentOrders.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">
                No hay órdenes recientes
              </p>
            ) : (
              <div className="space-y-4">
                {recentOrders.map(order => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Orden #{order.id.slice(-8)}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.products.length} productos
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatPrice(order.totalAmount)}</p>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'paid' ? 'bg-green-100 text-green-800' :
                        order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                        order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {order.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link to="/admin/orders">Ver Todas las Órdenes</Link>
              </Button>
            </div>
          </CardContent>
        </ActionCard>

        {/* Acciones rápidas */}
        <ActionCard>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Acciones Rápidas
            </CardTitle>
            <CardDescription>
              Accesos directos a funciones principales
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button className="w-full justify-start" asChild>
              <Link to="/admin/products">
                <Package className="w-4 h-4 mr-2" />
                Gestionar Productos
              </Link>
            </Button>
            
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link to="/admin/orders">
                <ShoppingCart className="w-4 h-4 mr-2" />
                Gestionar Órdenes
              </Link>
            </Button>
            
            <Button variant="outline" className="w-full justify-start" disabled>
              <Users className="w-4 h-4 mr-2" />
              Gestionar Usuarios
              <span className="ml-auto text-xs text-muted-foreground">Próximamente</span>
            </Button>
            
            <Button variant="outline" className="w-full justify-start" disabled>
              <BarChart3 className="w-4 h-4 mr-2" />
              Reportes y Análisis
              <span className="ml-auto text-xs text-muted-foreground">Próximamente</span>
            </Button>
          </CardContent>
        </ActionCard>
      </ActionsGrid>

      {/* Alertas y notificaciones */}
      {stats.lowStockProducts > 0 && (
        <Card className="mt-8 border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertCircle className="w-5 h-5" />
              Alerta de Stock Bajo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-orange-700">
              Tienes {stats.lowStockProducts} producto(s) con stock bajo (≤5 unidades). 
              Es recomendable reabastecer estos productos pronto.
            </p>
            <Button variant="outline" className="mt-4" asChild>
              <Link to="/admin/products">Ver Productos con Stock Bajo</Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </PageContainer>
  )
}
