export var PaymentSheetEventsEnum;
(function (PaymentSheetEventsEnum) {
    PaymentSheetEventsEnum["Loaded"] = "paymentSheetLoaded";
    PaymentSheetEventsEnum["FailedToLoad"] = "paymentSheetFailedToLoad";
    PaymentSheetEventsEnum["Completed"] = "paymentSheetCompleted";
    PaymentSheetEventsEnum["Canceled"] = "paymentSheetCanceled";
    PaymentSheetEventsEnum["Failed"] = "paymentSheetFailed";
})(PaymentSheetEventsEnum || (PaymentSheetEventsEnum = {}));
//# sourceMappingURL=payment-sheet-events.enum.js.map