{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAIjD,MAAM,sBAAsB,GAAG,cAAc,CAC3C,wBAAwB,EACxB;IACE,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,CAAC;CACxE,CACF,CAAC;AAEF,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,sBAAsB,EAAE,CAAC", "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { FirebaseAuthenticationPlugin } from './definitions';\n\nconst FirebaseAuthentication = registerPlugin<FirebaseAuthenticationPlugin>(\n  'FirebaseAuthentication',\n  {\n    web: () => import('./web').then(m => new m.FirebaseAuthenticationWeb()),\n  },\n);\n\nexport * from './definitions';\nexport { FirebaseAuthentication };\n"]}