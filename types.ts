// types.ts

export interface Product {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  formats: ProductFormat[];
  category?: string;
  color?: string; // Color personalizado para el producto (hex, rgb, etc.)
  isActive: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface ProductCategory {
  title: string;
  color?: string;
}

export interface ProductFormat {
  name: string;
  description: string;
  price: number;
  stock: number;
}

export interface UserAddress {
  id: string;
  uid: string;
  name: string; // Ej. "Casa", "Trabajo"
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  isDefault: boolean;
}

export interface ShoppingCartItem {
  productId: string;
  name: string;
  formatName: string;
  formatDescription: string;
  quantity: number;
  unitPrice: number;
  imageUrl: string;
}

export interface ShoppingCart {
  items: ShoppingCartItem[];
  totalAmount: number;
  updatedAt: string;
}

export interface User {
  uid: string;
  email: string;
  photoURL?: string;
  emailVerified: boolean;
  providerId: string;
  displayName?: string;
  phone?: string;
  createdAt: string;
  addresses?: UserAddress[];
  cart?: ShoppingCart;
  isAdmin?: boolean;
}

export interface Admin {
  uid: string;
  email: string;
  displayName?: string;
  permissions: string[]; // Ej. ['manageOrders', 'manageProducts']
  createdAt: string;
}

export interface OrderProduct {
  productId: string;
  name: string;
  formatName: string;
  quantity: number;
  unitPrice: number;
}

export interface Order {
  id: string;
  userId: string;
  userName: string;
  email: string;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  products: OrderProduct[];
  totalAmount: number;
  paymentId: string; // stripe payment id
  metadata?: Record<string, string>;
  createdAt: string;
  addressId: string; // user address id
}