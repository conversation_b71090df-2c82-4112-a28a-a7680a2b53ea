import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import type { ProductCategory, Product, ProductFormat } from '../../types'
import { ProductService } from '../services/productService'
import { ProductCard } from '../components/ProductCard'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { useApp } from '../contexts/AppContext'
import { Input } from '../components/ui/input'
import { Search } from 'lucide-react'
import { includesIgnoreAccents } from '../lib/utils'
import { container, media } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.primary[500]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const PageDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  color: ${({ theme }) => theme.colors.text.muted};
  max-width: 32rem;
  margin: 0 auto;
`

const FiltersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};

  ${media.md`
    // flex-direction: row;
    align-items: center;
    justify-content: space-between;
  `}
`

const SearchContainer = styled.div`
  position: relative;
  width: 100%;

  ${media.md`
    flex: 1;
    max-width: 24rem;
    margin-right: 1rem;
  `}

  ${media.lg`
    max-width: 28rem;
  `}
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: ${({ theme }) => theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.muted};
  width: 1rem;
  height: 1rem;
`

const SearchInput = styled(Input)`
  padding-left: 2.5rem;
  height: 2.75rem;
  font-size: ${({ theme }) => theme.fontSizes.base};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  border: 2px solid ${({ theme }) => theme.colors.border.default};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px rgba(0, 114, 206, 0.1);
  }
`

const FilterButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  flex-wrap: wrap;
`

const CategoryChip = styled.button<{ $isSelected: boolean; $backgroundColor?: string }>`
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  border: 2px solid transparent;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  ${({ $isSelected, $backgroundColor, theme }) => {
    if ($isSelected) {
      return `
        background: ${$backgroundColor || theme.colors.primary[500]};
        color: white;
        border-color: ${$backgroundColor || theme.colors.primary[500]};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
      `
    } else {
      return `
        background: ${theme.colors.white};
        color: ${theme.colors.text.secondary};
        border-color: ${theme.colors.border.light};
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:hover {
          background: ${$backgroundColor || theme.colors.primary[50]};
          border-color: ${$backgroundColor || theme.colors.primary[200]};
          color: ${$backgroundColor ? 'white' : theme.colors.primary[700]};
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      `
    }
  }}

  &:active {
    transform: translateY(0);
  }
`

const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[6]};

  ${media.md`
    grid-template-columns: repeat(2, 1fr);
  `}

  ${media.lg`
    grid-template-columns: repeat(3, 1fr);
  `}

  ${media.xl`
    grid-template-columns: repeat(4, 1fr);
  `}
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]} 0;
`

const EmptyStateText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
`

const InfoSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing[16]};
  text-align: center;
`

const InfoTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[8]};
  max-width: 64rem;
  margin: 0 auto;

  ${media.md`
    grid-template-columns: repeat(3, 1fr);
  `}
`

const InfoCard = styled.div`
  text-align: center;
`

const InfoIcon = styled.div`
  width: 4rem;
  height: 4rem;
  background: ${({ theme }) => theme.colors.primary[100]};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[4]};
  font-size: 2rem;
`

const InfoCardTitle = styled.h3`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const InfoCardText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
`

/**
 * Página principal que muestra la grilla de productos
 */
export const HomePage: React.FC = () => {
  const { state, dispatch } = useApp()
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory | null>(null)
  const [categories, setCategories] = useState<ProductCategory[]>([])

  // Cargar productos al montar el componente
  useEffect(() => {
    // setLoading(false)
    loadProducts()
  }, [])

  // Extraer categorías únicas cuando cambien los productos
  useEffect(() => {
    const uniqueCategories = Array.from(
      new Set(state.products.map(p => ({ title: p.category, color: p.color })).filter(Boolean))
    ) as ProductCategory[]
    setCategories(uniqueCategories)
  }, [state.products])

  const loadProducts = async () => {
    try {
      setLoading(true)
      // Intentar cargar desde Firebase, si falla usar datos de ejemplo
      try {
        const products = await ProductService.getProducts()
        dispatch({ type: 'SET_PRODUCTS', payload: products })
      } catch (firebaseError) {
        console.warn('Firebase no disponible, usando datos de ejemplo:', firebaseError)
        // Usar productos de ejemplo para demostración
        dispatch({ type: 'SET_PRODUCTS', payload: [] })

        // sampleProducts.forEach(async product => {
        //   try {
        //     if (product.category) {
        //       const newProduct = {
        //         ...product,
        //         createdAt: new Date().toISOString(),
        //         updatedAt: new Date().toISOString()
        //       }

        //       ProductService.createProduct(newProduct)
        //     }
        //   } catch (error) {
        //     console.error('Error creando producto de ejemplo:', error)
        //   }
        // })
      }
    } catch (error) {
      console.error('Error cargando productos:', error)
      dispatch({ type: 'SET_ERROR', payload: 'Error cargando productos' })
    } finally {
      setLoading(false)
    }
  }

  const handleAddToCart = (product: Product, format: ProductFormat) => {
    dispatch({ type: 'ADD_TO_CART', payload: { product, format } })
  }

  const handleUpdateQuantity = (productId: string, quantity: number) => {
    // Buscar el item en el carrito para obtener el formatName
    const cartItem = state.cart.items.find(item => item.productId === productId)
    if (cartItem) {
      dispatch({
        type: 'UPDATE_CART_ITEM',
        payload: { productId, formatName: cartItem.formatName, quantity }
      })
    }
  }

  // Filtrar productos por búsqueda y categoría
  const filteredProducts = state.products.filter(product => {
     const matchesSearch = includesIgnoreAccents(product.name, searchTerm) ||
       includesIgnoreAccents(product.description, searchTerm)

    // const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    //   product.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = !selectedCategory || selectedCategory && product.category === selectedCategory.title

    return matchesSearch && matchesCategory
  })

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <LoadingSpinner size="lg" className="h-64" />
      </div>
    )
  }

  return (
    <PageContainer>
      {/* Header de la página */}
      <PageHeader>
        <PageTitle onClick={loadProducts}>
          ¡Que te gustaria <span style={{ color: '#019D4A' }}>mejorar hoy</span> en tu salud?
        </PageTitle>
        <PageDescription>
          Desde mejorar tu digestión y calmar la gastritis, hasta activar tu energía natural, fortalecer tu piel con colágeno o limpiar tus vías respiratorias, cada producto aquí fue creado para ayudarte a sentirte mejor
        </PageDescription>
      </PageHeader>

      {/* Filtros y búsqueda */}
      <FiltersContainer>
        <SearchContainer>
          <SearchIcon />
          <SearchInput
            placeholder="Buscar productos..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>

        <FilterButtons>
          <CategoryChip
            $isSelected={selectedCategory === null}
            onClick={() => setSelectedCategory(null)}
          >
            Todos
          </CategoryChip>
          {categories.map(category => (
            <CategoryChip
              key={category.title}
              $isSelected={selectedCategory?.title === category.title}
              $backgroundColor={category.color}
              onClick={() => setSelectedCategory(category)}
            >
              {category.title}
            </CategoryChip>
          ))}
        </FilterButtons>
      </FiltersContainer>

      {/* Grilla de productos */}
      {filteredProducts.length === 0 ? (
        <EmptyState>
          <EmptyStateText>
            No se encontraron productos que coincidan con tu búsqueda.
          </EmptyStateText>
        </EmptyState>
      ) : (
        <ProductsGrid>
          {filteredProducts.map(product => {
            const cartItem = state.cart.items.find(item => item.productId === product.id)

            return (
              <ProductCard
                key={product.id}
                product={product}
                cartItem={cartItem}
                onAddToCart={handleAddToCart}
                onUpdateQuantity={handleUpdateQuantity}
              />
            )
          })}
        </ProductsGrid>
      )}

      {/* Información adicional */}
      <InfoSection>
        <InfoTitle>¿Por qué elegir Fuxion?</InfoTitle>
        <InfoGrid>
          <InfoCard>
            <InfoIcon>
              <span>🌿</span>
            </InfoIcon>
            <InfoCardTitle>100% Natural</InfoCardTitle>
            <InfoCardText>
              Productos elaborados con ingredientes naturales de la más alta calidad.
            </InfoCardText>
          </InfoCard>

          <InfoCard>
            <InfoIcon>
              <span>💪</span>
            </InfoIcon>
            <InfoCardTitle>Resultados Comprobados</InfoCardTitle>
            <InfoCardText>
              Fórmulas respaldadas por la ciencia para maximizar tu rendimiento.
            </InfoCardText>
          </InfoCard>

          <InfoCard>
            <InfoIcon>
              <span>🚚</span>
            </InfoIcon>
            <InfoCardTitle>Envío Rápido</InfoCardTitle>
            <InfoCardText>
              Recibe tus productos en la comodidad de tu hogar en tiempo récord.
            </InfoCardText>
          </InfoCard>
        </InfoGrid>
      </InfoSection>
    </PageContainer>
  )
}
