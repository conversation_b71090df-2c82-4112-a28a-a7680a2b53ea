import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useApp } from '../contexts/AppContext'
import { AddressService } from '../services/addressService'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog'
import { LoadingSpinner } from '../components/LoadingSpinner'
import type { UserAddress } from '../../types'
import { MapPin, Plus, Edit, Trash2, Star } from 'lucide-react'
import { generateId } from '../lib/utils'
import { container, media } from '../styles/utils'

const PageContainer = styled.div`
  ${container}
  padding-top: ${({ theme }) => theme.spacing[8]};
  padding-bottom: ${({ theme }) => theme.spacing[8]};
  min-height: 100vh;
`

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const PageDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
`

const AddressGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[6]};

  ${media.md`
    grid-template-columns: repeat(2, 1fr);
  `}

  ${media.lg`
    grid-template-columns: repeat(3, 1fr);
  `}
`

const AddressCard = styled(Card)`
  position: relative;
  transition: all 200ms ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`

const DefaultBadge = styled.div`
  position: absolute;
  top: ${({ theme }) => theme.spacing[3]};
  right: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.primary[500]};
  color: white;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
`

const AddressInfo = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const AddressName = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`

const AddressText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.muted};
  line-height: 1.5;
`

const AddressActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  justify-content: flex-end;
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]} 0;
`

const EmptyStateText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.muted};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`

const FormGrid = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing[4]};

  ${media.md`
    grid-template-columns: repeat(2, 1fr);
  `}
`

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};

  &.full-width {
    ${media.md`
      grid-column: 1 / -1;
    `}
  }
`

const FormLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const CheckboxField = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`

/**
 * Página de gestión de direcciones de usuario
 */
export const AddressManagementPage: React.FC = () => {
  const { state } = useApp()
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    street: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    isDefault: false
  })

  useEffect(() => {
    if (state.user) {
      loadAddresses()
    }
  }, [state.user])

  const loadAddresses = async () => {
    if (!state.user) return

    try {
      setLoading(true)
      const userAddresses = await AddressService.getUserAddresses(state.user.uid)
      setAddresses(userAddresses)
    } catch (error) {
      console.error('Error cargando direcciones:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!state.user) return

    try {
      const addressData: Omit<UserAddress, 'id'> = {
        ...formData,
        uid: state.user.uid
      }

      if (editingAddress) {
        await AddressService.updateAddress(editingAddress.id, addressData)
      } else {
        await AddressService.createAddress(addressData)
      }

      await loadAddresses()
      handleCloseDialog()
    } catch (error) {
      console.error('Error guardando dirección:', error)
    }
  }

  const handleEdit = (address: UserAddress) => {
    setEditingAddress(address)
    setFormData({
      name: address.name,
      street: address.street,
      city: address.city,
      state: address.state,
      country: address.country,
      postalCode: address.postalCode,
      isDefault: address.isDefault
    })
    setDialogOpen(true)
  }

  const handleDelete = async (addressId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta dirección?')) return

    try {
      await AddressService.deleteAddress(addressId)
      await loadAddresses()
    } catch (error) {
      console.error('Error eliminando dirección:', error)
    }
  }

  const handleSetDefault = async (addressId: string) => {
    if (!state.user) return

    try {
      await AddressService.setDefaultAddress(addressId, state.user.uid)
      await loadAddresses()
    } catch (error) {
      console.error('Error estableciendo dirección por defecto:', error)
    }
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setEditingAddress(null)
    setFormData({
      name: '',
      street: '',
      city: '',
      state: '',
      country: '',
      postalCode: '',
      isDefault: false
    })
  }

  if (!state.user) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center' }}>
          <p>No hay usuario autenticado</p>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Mis Direcciones</PageTitle>
        <PageDescription>
          Gestiona tus direcciones de envío
        </PageDescription>
      </PageHeader>

      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '2rem' }}>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Agregar Dirección
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingAddress ? 'Editar Dirección' : 'Nueva Dirección'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-2">
                    Nombre de la dirección
                  </label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Ej: Casa, Trabajo"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="street" className="block text-sm font-medium mb-2">
                    Dirección
                  </label>
                  <Input
                    id="street"
                    name="street"
                    value={formData.street}
                    onChange={handleInputChange}
                    placeholder="Calle y número"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium mb-2">
                      Ciudad
                    </label>
                    <Input
                      id="city"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="state" className="block text-sm font-medium mb-2">
                      Estado/Provincia
                    </label>
                    <Input
                      id="state"
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="postalCode" className="block text-sm font-medium mb-2">
                      Código Postal
                    </label>
                    <Input
                      id="postalCode"
                      name="postalCode"
                      value={formData.postalCode}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="country" className="block text-sm font-medium mb-2">
                      País
                    </label>
                    <Input
                      id="country"
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    name="isDefault"
                    checked={formData.isDefault}
                    onChange={handleInputChange}
                    className="rounded"
                  />
                  <label htmlFor="isDefault" className="text-sm">
                    Establecer como dirección por defecto
                  </label>
                </div>

                <div className="flex gap-2">
                  <Button type="submit" className="flex-1">
                    {editingAddress ? 'Actualizar' : 'Guardar'}
                  </Button>
                  <Button type="button" variant="outline" onClick={handleCloseDialog}>
                    Cancelar
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {loading ? (
          <LoadingSpinner size="lg" className="h-64" />
        ) : addresses.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No tienes direcciones guardadas</h3>
              <p className="text-muted-foreground mb-4">
                Agrega una dirección para facilitar tus compras futuras
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {addresses.map(address => (
              <Card key={address.id} className={address.isDefault ? 'ring-2 ring-primary' : ''}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      {address.name}
                    </span>
                    {address.isDefault && (
                      <span className="flex items-center gap-1 text-xs bg-primary text-primary-foreground px-2 py-1 rounded">
                        <Star className="w-3 h-3" />
                        Por defecto
                      </span>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-sm text-muted-foreground mb-4">
                    <p>{address.street}</p>
                    <p>{address.city}, {address.state} {address.postalCode}</p>
                    <p>{address.country}</p>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(address)}>
                      <Edit className="w-4 h-4 mr-1" />
                      Editar
                    </Button>
                    {!address.isDefault && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleSetDefault(address.id)}
                      >
                        <Star className="w-4 h-4 mr-1" />
                        Por defecto
                      </Button>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDelete(address.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Eliminar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
    </PageContainer>
  )
}
