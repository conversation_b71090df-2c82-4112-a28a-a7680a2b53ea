import React, { useState, useCallback } from 'react'
import styled from 'styled-components'
// import { PayPalButtons, PayPalScriptProvider } from '@paypal/react-paypal-js'
import { Browser } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import { LoadingSpinner } from './LoadingSpinner'
import { Button } from './ui/button'
import { ExternalLink } from 'lucide-react'

// Configuración de PayPal
const PAYPAL_CONFIG = {
  clientId: 'AYGKb-LV8LHkfS7-AfAfCqOGZXZG98WpjhIDPfRKOz4f44Q3lFPIe6w1kaHiz74WYvb71Xdv9JjoxT7SXNkMspp7g61Fpvlr',
  currency: 'USD',
  intent: 'capture',
  components: 'buttons',
  'disable-funding': 'credit,card', // Opcional: deshabilitar métodos de pago específicos
}

// Verificar que el Client ID esté configurado
if (!PAYPAL_CONFIG.clientId || PAYPAL_CONFIG.clientId.length < 10) {
  console.error('PayPal Client ID no está configurado correctamente')
}

const PayPalContainer = styled.div`
  width: 100%;
  min-height: 50px;
  position: relative;
`

const ErrorMessage = styled.div`
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const SuccessMessage = styled.div`
  color: #16a34a;
  background: rgba(22, 163, 74, 0.1);
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const MobilePaymentButton = styled(Button)`
  width: 100%;
  background: #0070ba;
  border-color: #0070ba;
  
  &:hover {
    background: #005ea6;
    border-color: #005ea6;
  }
`

export interface PayPalCheckoutProps {
  amount: number
  description?: string
  onSuccess?: (transactionId: string) => void
  onError?: (error: string) => void
  onCancel?: () => void
  successUrl?: string
  disabled?: boolean
}

/**
 * Componente de checkout de PayPal compatible con web y móvil
 */
export const PayPalCheckout: React.FC<PayPalCheckoutProps> = ({
  amount,
  description = 'Pago',
  onSuccess,
  onError,
  onCancel: _onCancel,
  successUrl,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')
  const [isMobile] = useState(Capacitor.getPlatform() !== 'web')

  // Debug: Log del estado del componente
  console.log('PayPalCheckout render:', {
    amount,
    disabled,
    isMobile,
    isLoading,
    error,
    clientId: PAYPAL_CONFIG.clientId.substring(0, 20) + '...'
  })

  // Crear orden de PayPal
  const createOrder = useCallback(async () => {
    try {
      setError('')
      setIsLoading(true)

      // Validación antes de crear la orden
      if (disabled) {
        throw new Error('No se puede procesar el pago en este momento')
      }

      if (amount <= 0) {
        throw new Error('El monto debe ser mayor a cero')
      }

      // En producción, esto sería una llamada a tu backend
      const orderData = {
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: amount.toFixed(2),
            },
            description: description,
          },
        ],
        application_context: {
          brand_name: 'AriFuxion',
          landing_page: 'NO_PREFERENCE',
          user_action: 'PAY_NOW',
          return_url: successUrl || `${window.location.origin}/payment-success`,
          cancel_url: `${window.location.origin}/checkout`,
        },
      }

      console.log('Creando orden PayPal:', orderData)

      // Simular creación de orden (en producción sería una llamada al backend)
      const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      return orderId
    } catch (error) {
      console.error('Error creando orden:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [amount, description, successUrl, disabled])

  // Funciones comentadas temporalmente para debug
  /*
  const onApprove = useCallback(async (data: any) => {
    // ... código comentado
  }, [amount, onSuccess, onError, successUrl])

  const onErrorHandler = useCallback((error: any) => {
    // ... código comentado
  }, [onError])

  const onCancelHandler = useCallback(() => {
    // ... código comentado
  }, [onCancel])
  */

  // Abrir PayPal en navegador externo para móvil
  const openPayPalInBrowser = useCallback(async () => {
    try {
      setIsLoading(true)
      setError('')

      // Crear orden
      const orderId = await createOrder()

      // URL de PayPal para móvil
      const paypalUrl = `https://www.paypal.com/checkoutnow?token=${orderId}`

      if (Capacitor.getPlatform() !== 'web') {
        // Abrir en navegador externo en móvil
        await Browser.open({
          url: paypalUrl,
          windowName: '_system',
          presentationStyle: 'popover'
        })

        // Simular éxito después de un delay (en producción manejarías esto con deep links)
        setTimeout(() => {
          const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          console.log('Pago móvil simulado exitoso:', transactionId)
          
          if (onSuccess) {
            onSuccess(transactionId)
          }
        }, 5000)
      } else {
        // En web, abrir en nueva ventana
        window.open(paypalUrl, '_blank')
      }

    } catch (error: any) {
      console.error('Error abriendo PayPal:', error)
      const errorMessage = error.message || 'Error abriendo PayPal'
      setError(errorMessage)
      
      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }, [createOrder, onSuccess, onError])

  // Renderizar para móvil
  if (isMobile) {
    return (
      <PayPalContainer>
        {disabled && (
          <ErrorMessage>
            Selecciona una dirección de envío para continuar con el pago
          </ErrorMessage>
        )}

        <MobilePaymentButton
          onClick={openPayPalInBrowser}
          disabled={disabled || isLoading}
        >
          {isLoading ? (
            <LoadingSpinner size="sm" />
          ) : (
            <>
              <ExternalLink size={16} style={{ marginRight: '0.5rem' }} />
              Pagar con PayPal ${amount.toFixed(2)}
            </>
          )}
        </MobilePaymentButton>

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
      </PayPalContainer>
    )
  }

  // Renderizar para web
  return (
    <PayPalContainer>
      {disabled && (
        <ErrorMessage>
          Selecciona una dirección de envío para continuar con el pago
        </ErrorMessage>
      )}

      {/* Botón temporal mientras se soluciona PayPal SDK */}
      <MobilePaymentButton
        onClick={async () => {
          if (disabled) {
            setError('Selecciona una dirección de envío')
            return
          }

          try {
            setIsLoading(true)
            setError('')

            // Simular procesamiento
            await new Promise(resolve => setTimeout(resolve, 2000))

            // Generar transaction ID simulado
            const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

            console.log('Pago simulado exitoso:', transactionId)
            setSuccess(`Pago exitoso. ID: ${transactionId}`)

            if (onSuccess) {
              onSuccess(transactionId)
            }
          } catch (error: any) {
            const errorMessage = error.message || 'Error procesando el pago'
            setError(errorMessage)
            if (onError) {
              onError(errorMessage)
            }
          } finally {
            setIsLoading(false)
          }
        }}
        disabled={disabled || isLoading}
      >
        {isLoading ? (
          <LoadingSpinner size="sm" />
        ) : (
          <>
            <ExternalLink size={16} style={{ marginRight: '0.5rem' }} />
            Pagar con PayPal ${amount.toFixed(2)} (Simulado)
          </>
        )}
      </MobilePaymentButton>

      {error && <ErrorMessage>{error}</ErrorMessage>}
      {success && <SuccessMessage>{success}</SuccessMessage>}
    </PayPalContainer>
  )
}

export default PayPalCheckout
