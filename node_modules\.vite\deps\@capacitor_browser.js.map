{"version": 3, "sources": ["../../@capacitor/browser/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { BrowserPlugin } from './definitions';\n\nconst Browser = registerPlugin<BrowserPlugin>('Browser', {\n  web: () => import('./web').then(m => new m.BrowserWeb()),\n});\n\nexport * from './definitions';\nexport { Browser };\n"], "mappings": ";;;;;;AAIA,IAAM,UAAU,eAA8B,WAAW;EACvD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,WAAU,CAAE;CACxD;", "names": []}