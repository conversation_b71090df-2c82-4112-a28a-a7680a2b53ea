import React, { useEffect, useState } from 'react'
import { Product } from '../../types'
import { ProductService } from '../services/productService'
import { ProductCard } from '../components/ProductCard'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { useApp } from '../contexts/AppContext'
import { Input } from '../components/ui/input'
import { Button } from '../components/ui/button'
import { Search, Filter } from 'lucide-react'

/**
 * Página principal que muestra la grilla de productos
 */
export const HomePage: React.FC = () => {
  const { state, dispatch } = useApp()
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [categories, setCategories] = useState<string[]>([])

  // Cargar productos al montar el componente
  useEffect(() => {
    loadProducts()
  }, [])

  // Extraer categorías únicas cuando cambien los productos
  useEffect(() => {
    const uniqueCategories = Array.from(
      new Set(state.products.map(p => p.category).filter(Boolean))
    ) as string[]
    setCategories(uniqueCategories)
  }, [state.products])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const products = await ProductService.getProducts()
      dispatch({ type: 'SET_PRODUCTS', payload: products })
    } catch (error) {
      console.error('Error cargando productos:', error)
      dispatch({ type: 'SET_ERROR', payload: 'Error cargando productos' })
    } finally {
      setLoading(false)
    }
  }

  const handleAddToCart = (product: Product) => {
    dispatch({ type: 'ADD_TO_CART', payload: product })
  }

  const handleUpdateQuantity = (productId: string, quantity: number) => {
    dispatch({ type: 'UPDATE_CART_ITEM', payload: { productId, quantity } })
  }

  // Filtrar productos por búsqueda y categoría
  const filteredProducts = state.products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || product.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <LoadingSpinner size="lg" className="h-64" />
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header de la página */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-primary mb-4">
          Productos Naturales de Fitness
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Descubre nuestra selección de productos naturales para potenciar tu rendimiento 
          y alcanzar tus objetivos de fitness de manera saludable.
        </p>
      </div>

      {/* Filtros y búsqueda */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Buscar productos..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={selectedCategory === '' ? 'default' : 'outline'}
            onClick={() => setSelectedCategory('')}
          >
            Todos
          </Button>
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'default' : 'outline'}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Grilla de productos */}
      {filteredProducts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            No se encontraron productos que coincidan con tu búsqueda.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map(product => {
            const cartItem = state.cart.items.find(item => item.productId === product.id)
            
            return (
              <ProductCard
                key={product.id}
                product={product}
                cartItem={cartItem}
                onAddToCart={handleAddToCart}
                onUpdateQuantity={handleUpdateQuantity}
              />
            )
          })}
        </div>
      )}

      {/* Información adicional */}
      <div className="mt-16 text-center">
        <h2 className="text-2xl font-semibold mb-4">¿Por qué elegir Fuxion?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🌿</span>
            </div>
            <h3 className="font-semibold mb-2">100% Natural</h3>
            <p className="text-sm text-muted-foreground">
              Productos elaborados con ingredientes naturales de la más alta calidad.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">💪</span>
            </div>
            <h3 className="font-semibold mb-2">Resultados Comprobados</h3>
            <p className="text-sm text-muted-foreground">
              Fórmulas respaldadas por la ciencia para maximizar tu rendimiento.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🚚</span>
            </div>
            <h3 className="font-semibold mb-2">Envío Rápido</h3>
            <p className="text-sm text-muted-foreground">
              Recibe tus productos en la comodidad de tu hogar en tiempo récord.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
