import React, { useState, useCallback } from 'react'
import styled from 'styled-components'
import { PayPalButtons, PayPalScriptProvider } from '@paypal/react-paypal-js'
// import { Browser } from '@capacitor/browser' // No usado en checkout real
import { Capacitor } from '@capacitor/core'
import { LoadingSpinner } from './LoadingSpinner'
import { Button } from './ui/button'
import { ExternalLink } from 'lucide-react'

// Configuración de PayPal
const PAYPAL_CONFIG = {
  clientId: 'AfAfCqOGZXZG98WpjhIDPfRKOz4f44Q3lFPIe6w1kaHiz74WYvb71Xdv9JjoxT7SXNkMspp7g61Fpvlr',
  environment: 'sandbox',
  currency: 'USD',
  intent: 'capture',
  components: 'buttons',
  'disable-funding': 'credit,card', // Opcional: deshabilitar métodos de pago específicos
}

// Verificar que el Client ID esté configurado
if (!PAYPAL_CONFIG.clientId || PAYPAL_CONFIG.clientId.length < 10) {
  console.error('PayPal Client ID no está configurado correctamente')
}

const PayPalContainer = styled.div`
  width: 100%;
  min-height: 50px;
  position: relative;
`

const ErrorMessage = styled.div`
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const SuccessMessage = styled.div`
  color: #16a34a;
  background: rgba(22, 163, 74, 0.1);
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const MobilePaymentButton = styled(Button)`
  width: 100%;
  background: #0070ba;
  border-color: #0070ba;
  
  &:hover {
    background: #005ea6;
    border-color: #005ea6;
  }
`

export interface PayPalCheckoutProps {
  amount: number
  description?: string
  onSuccess?: (transactionId: string) => void
  onError?: (error: string) => void
  onCancel?: () => void
  successUrl?: string
  disabled?: boolean
}

/**
 * Componente de checkout de PayPal compatible con web y móvil
 */
export const PayPalCheckout: React.FC<PayPalCheckoutProps> = ({
  amount,
  description = 'Pago',
  onSuccess,
  onError,
  onCancel: _onCancel,
  successUrl,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')
  const [isMobile] = useState(Capacitor.getPlatform() !== 'web')

  // Debug: Log del estado del componente
  console.log('PayPalCheckout render:', {
    amount,
    disabled,
    isMobile,
    isLoading,
    error,
    clientId: PAYPAL_CONFIG.clientId.substring(0, 20) + '...'
  })

  // Crear orden de PayPal REAL
  const createOrder = useCallback((_data: any, actions: any) => {
    try {
      setError('')
      setIsLoading(true)

      // Validación antes de crear la orden
      if (disabled) {
        throw new Error('No se puede procesar el pago en este momento')
      }

      if (amount <= 0) {
        throw new Error('El monto debe ser mayor a cero')
      }

      console.log('Creando orden PayPal REAL con amount:', amount)

      // Crear orden real usando PayPal SDK actions
      return actions.order.create({
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: amount.toFixed(2),
            },
            description: description,
          },
        ],
        application_context: {
          brand_name: 'AriFuxion',
          landing_page: 'NO_PREFERENCE',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'PAY_NOW',
          // return_url: successUrl || `${window.location.origin}/payment-success`,
          // cancel_url: `${window.location.origin}/checkout`,
        },
      })
    } catch (error) {
      console.error('Error creando orden:', error)
      setError('Error creando la orden de pago')
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [amount, description, successUrl, disabled])

  // Capturar pago REAL
  const onApprove = useCallback(async (data: any, actions: any) => {
    try {
      setIsLoading(true)
      setError('')

      console.log('Pago aprobado por PayPal:', data)

      // Capturar el pago usando PayPal SDK
      const captureResult = await actions.order.capture()

      console.log('Pago capturado exitosamente:', captureResult)

      // Extraer el transaction ID real de PayPal
      const transactionId = captureResult.purchase_units[0].payments.captures[0].id
      const payerInfo = captureResult.payer

      console.log('Transaction ID de PayPal:', transactionId)
      console.log('Información del pagador:', payerInfo)

      setSuccess(`Pago exitoso. ID: ${transactionId}`)

      // Llamar callback de éxito con el transaction ID real
      if (onSuccess) {
        onSuccess(transactionId)
      }

      // No redirigir automáticamente, dejar que el callback maneje la redirección

    } catch (error: any) {
      console.error('Error capturando pago:', error)
      const errorMessage = error.message || 'Error procesando el pago'
      setError(errorMessage)

      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }, [onSuccess, onError])

  // Manejar errores
  const onErrorHandler = useCallback((error: any) => {
    console.error('Error en PayPal:', error)
    const errorMessage = 'Error en el sistema de pagos. Por favor, intenta de nuevo.'
    setError(errorMessage)

    if (onError) {
      onError(errorMessage)
    }
  }, [onError])

  // Manejar cancelación
  const onCancelHandler = useCallback(() => {
    console.log('Pago cancelado por el usuario')
    setError('Pago cancelado')

    if (_onCancel) {
      _onCancel()
    }
  }, [_onCancel])

  // Para móvil, mostrar mensaje de que use la versión web
  const openPayPalInBrowser = useCallback(async () => {
    try {
      setError('Para pagos con PayPal, por favor usa la versión web de la aplicación')

      if (onError) {
        onError('Función no disponible en móvil. Usa la versión web.')
      }
    } catch (error: any) {
      console.error('Error:', error)
    }
  }, [onError])

  // Renderizar para móvil
  if (isMobile) {
    return (
      <PayPalContainer>
        {disabled && (
          <ErrorMessage>
            Selecciona una dirección de envío para continuar con el pago
          </ErrorMessage>
        )}

        <MobilePaymentButton
          onClick={openPayPalInBrowser}
          disabled={disabled || isLoading}
        >
          {isLoading ? (
            <LoadingSpinner size="sm" />
          ) : (
            <>
              <ExternalLink size={16} style={{ marginRight: '0.5rem' }} />
              Pagar con PayPal ${amount.toFixed(2)}
            </>
          )}
        </MobilePaymentButton>

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
      </PayPalContainer>
    )
  }

  // Renderizar para web
  return (
    <PayPalScriptProvider options={{
      clientId: PAYPAL_CONFIG.clientId,
      currency: PAYPAL_CONFIG.currency,
      intent: PAYPAL_CONFIG.intent,
      components: PAYPAL_CONFIG.components,
    }}>
      <PayPalContainer>
        {disabled && (
          <ErrorMessage>
            Selecciona una dirección de envío para continuar con el pago
          </ErrorMessage>
        )}

        {isLoading && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 10
          }}>
            <LoadingSpinner size="sm" />
          </div>
        )}

        <PayPalButtons
          style={{
            layout: 'vertical',
            color: 'blue',
            shape: 'rect',
            label: 'paypal',
          }}
          disabled={disabled || isLoading}
          createOrder={createOrder}
          onApprove={onApprove}
          onError={onErrorHandler}
          onCancel={onCancelHandler}
        />

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
      </PayPalContainer>
    </PayPalScriptProvider>
  )
}

export default PayPalCheckout
