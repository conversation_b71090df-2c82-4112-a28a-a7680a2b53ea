{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/applepay/apple-pay-events.enum.js", "esm/googlepay/google-pay-events.enum.js", "esm/paymentflow/payment-flow-events.enum.js", "esm/paymentsheet/payment-sheet-events.enum.js", "esm/index.js", "esm/shared/platform.js", "esm/web.js"], "sourcesContent": ["export var ApplePayEventsEnum;\n(function (ApplePayEventsEnum) {\n    ApplePayEventsEnum[\"Loaded\"] = \"applePayLoaded\";\n    ApplePayEventsEnum[\"FailedToLoad\"] = \"applePayFailedToLoad\";\n    ApplePayEventsEnum[\"Completed\"] = \"applePayCompleted\";\n    ApplePayEventsEnum[\"Canceled\"] = \"applePayCanceled\";\n    ApplePayEventsEnum[\"Failed\"] = \"applePayFailed\";\n    ApplePayEventsEnum[\"DidSelectShippingContact\"] = \"applePayDidSelectShippingContact\";\n    ApplePayEventsEnum[\"DidCreatePaymentMethod\"] = \"applePayDidCreatePaymentMethod\";\n})(ApplePayEventsEnum || (ApplePayEventsEnum = {}));\n//# sourceMappingURL=apple-pay-events.enum.js.map", "export var GooglePayEventsEnum;\n(function (GooglePayEventsEnum) {\n    GooglePayEventsEnum[\"Loaded\"] = \"googlePayLoaded\";\n    GooglePayEventsEnum[\"FailedToLoad\"] = \"googlePayFailedToLoad\";\n    GooglePayEventsEnum[\"Completed\"] = \"googlePayCompleted\";\n    GooglePayEventsEnum[\"Canceled\"] = \"googlePayCanceled\";\n    GooglePayEventsEnum[\"Failed\"] = \"googlePayFailed\";\n})(GooglePayEventsEnum || (GooglePayEventsEnum = {}));\n//# sourceMappingURL=google-pay-events.enum.js.map", "export var PaymentFlowEventsEnum;\n(function (PaymentFlowEventsEnum) {\n    PaymentFlowEventsEnum[\"Loaded\"] = \"paymentFlowLoaded\";\n    PaymentFlowEventsEnum[\"FailedToLoad\"] = \"paymentFlowFailedToLoad\";\n    PaymentFlowEventsEnum[\"Opened\"] = \"paymentFlowOpened\";\n    PaymentFlowEventsEnum[\"Created\"] = \"paymentFlowCreated\";\n    PaymentFlowEventsEnum[\"Completed\"] = \"paymentFlowCompleted\";\n    PaymentFlowEventsEnum[\"Canceled\"] = \"paymentFlowCanceled\";\n    PaymentFlowEventsEnum[\"Failed\"] = \"paymentFlowFailed\";\n})(PaymentFlowEventsEnum || (PaymentFlowEventsEnum = {}));\n//# sourceMappingURL=payment-flow-events.enum.js.map", "export var PaymentSheetEventsEnum;\n(function (PaymentSheetEventsEnum) {\n    PaymentSheetEventsEnum[\"Loaded\"] = \"paymentSheetLoaded\";\n    PaymentSheetEventsEnum[\"FailedToLoad\"] = \"paymentSheetFailedToLoad\";\n    PaymentSheetEventsEnum[\"Completed\"] = \"paymentSheetCompleted\";\n    PaymentSheetEventsEnum[\"Canceled\"] = \"paymentSheetCanceled\";\n    PaymentSheetEventsEnum[\"Failed\"] = \"paymentSheetFailed\";\n})(PaymentSheetEventsEnum || (PaymentSheetEventsEnum = {}));\n//# sourceMappingURL=payment-sheet-events.enum.js.map", "import { registerPlugin } from '@capacitor/core';\nconst Stripe = registerPlugin('Stripe', {\n    web: () => import('./web').then((m) => new m.StripeWeb()),\n});\nexport * from './definitions';\nexport { Stripe };\n//# sourceMappingURL=index.js.map", "/**\n * @url https://github.com/ionic-team/ionic-framework/blob/main/core/src/utils/platform.ts\n * So `@typescript-eslint/no-explicit-any` `@typescript-eslint/no-non-null-assertion` is disabled here\n */\nexport const getPlatforms = (win) => setupPlatforms(win);\nexport const isPlatform = (winOrPlatform, platform) => {\n    if (typeof winOrPlatform === 'string') {\n        platform = winOrPlatform;\n        winOrPlatform = undefined;\n    }\n    return getPlatforms(winOrPlatform).includes(platform);\n};\nexport const setupPlatforms = (win = window) => {\n    if (typeof win === 'undefined') {\n        return [];\n    }\n    win.Ionic = win.Ionic || {};\n    let platforms = win.Ionic.platforms;\n    if (platforms == null) {\n        platforms = win.Ionic.platforms = detectPlatforms(win);\n        platforms.forEach(p => win.document.documentElement.classList.add(`plt-${p}`));\n    }\n    return platforms;\n};\nconst detectPlatforms = (win) => Object.keys(PLATFORMS_MAP).filter(p => PLATFORMS_MAP[p](win));\nconst isMobileWeb = (win) => isMobile(win) && !isHybrid(win);\nconst isIpad = (win) => {\n    // iOS 12 and below\n    if (testUserAgent(win, /iPad/i)) {\n        return true;\n    }\n    // iOS 13+\n    if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n        return true;\n    }\n    return false;\n};\nconst isIphone = (win) => testUserAgent(win, /iPhone/i);\nconst isIOS = (win) => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = (win) => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = (win) => {\n    return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = (win) => {\n    const width = win.innerWidth;\n    const height = win.innerHeight;\n    const smallest = Math.min(width, height);\n    const largest = Math.max(width, height);\n    return (smallest > 390 && smallest < 520) &&\n        (largest > 620 && largest < 800);\n};\nconst isTablet = (win) => {\n    const width = win.innerWidth;\n    const height = win.innerHeight;\n    const smallest = Math.min(width, height);\n    const largest = Math.max(width, height);\n    return (isIpad(win) ||\n        isAndroidTablet(win) ||\n        ((smallest > 460 && smallest < 820) &&\n            (largest > 780 && largest < 1400)));\n};\nconst isMobile = (win) => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = (win) => !isMobile(win);\nconst isHybrid = (win) => isCordova(win) || isCapacitorNative(win);\nconst isCordova = (win) => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = (win) => {\n    const capacitor = win['Capacitor'];\n    return !!(capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative);\n};\nconst isElectron = (win) => testUserAgent(win, /electron/i);\nconst isPWA = (win) => !!(win.matchMedia('(display-mode: standalone)').matches || win.navigator.standalone);\nexport const testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => win.matchMedia(query).matches;\nconst PLATFORMS_MAP = {\n    'ipad': isIpad,\n    'iphone': isIphone,\n    'ios': isIOS,\n    'android': isAndroid,\n    'phablet': isPhablet,\n    'tablet': isTablet,\n    'cordova': isCordova,\n    'capacitor': isCapacitorNative,\n    'electron': isElectron,\n    'pwa': isPWA,\n    'mobile': isMobile,\n    'mobileweb': isMobileWeb,\n    'desktop': isDesktop,\n    'hybrid': isHybrid\n};\n//# sourceMappingURL=platform.js.map", "import { WebPlugin } from '@capacitor/core';\nimport { ApplePayEventsEnum, GooglePayEventsEnum, PaymentFlowEventsEnum, PaymentSheetEventsEnum } from './definitions';\nimport { isPlatform } from './shared/platform';\nexport class StripeWeb extends WebPlugin {\n    async initialize(options) {\n        if (typeof options.publishableKey !== 'string' || options.publishableKey.trim().length === 0) {\n            throw new Error('you must provide a valid key');\n        }\n        this.publishableKey = options.publishableKey;\n        if (options.stripeAccount) {\n            this.stripeAccount = options.stripeAccount;\n        }\n    }\n    async createPaymentSheet(options) {\n        var _a;\n        if (!this.publishableKey) {\n            this.notifyListeners(PaymentSheetEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.paymentSheet = document.createElement('stripe-payment-sheet');\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(this.paymentSheet);\n        await customElements.whenDefined('stripe-payment-sheet');\n        this.paymentSheet.publishableKey = this.publishableKey;\n        if (this.stripeAccount) {\n            this.paymentSheet.stripeAccount = this.stripeAccount;\n        }\n        this.paymentSheet.applicationName = '@capacitor-community/stripe';\n        this.paymentSheet.intentClientSecret = options.paymentIntentClientSecret;\n        this.paymentSheet.intentType = 'payment';\n        if (options.withZipCode !== undefined) {\n            this.paymentSheet.zip = options.withZipCode;\n        }\n        this.notifyListeners(PaymentSheetEventsEnum.Loaded, null);\n    }\n    async presentPaymentSheet() {\n        if (!this.paymentSheet) {\n            throw new Error();\n        }\n        const props = await this.paymentSheet.present();\n        if (props === undefined) {\n            this.notifyListeners(PaymentSheetEventsEnum.Canceled, null);\n            return {\n                paymentResult: PaymentSheetEventsEnum.Canceled,\n            };\n        }\n        const { detail: { stripe, cardNumberElement }, } = props;\n        const result = await stripe.createPaymentMethod({\n            type: 'card',\n            card: cardNumberElement,\n        });\n        this.paymentSheet.updateProgress('success');\n        this.paymentSheet.remove();\n        if (result.error !== undefined) {\n            this.notifyListeners(PaymentSheetEventsEnum.Failed, null);\n            return {\n                paymentResult: PaymentSheetEventsEnum.Failed,\n            };\n        }\n        this.notifyListeners(PaymentSheetEventsEnum.Completed, null);\n        return {\n            paymentResult: PaymentSheetEventsEnum.Completed,\n        };\n    }\n    async createPaymentFlow(options) {\n        var _a;\n        if (!this.publishableKey) {\n            this.notifyListeners(PaymentFlowEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.paymentSheet = document.createElement('stripe-payment-sheet');\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(this.paymentSheet);\n        await customElements.whenDefined('stripe-payment-sheet');\n        this.paymentSheet.publishableKey = this.publishableKey;\n        if (this.stripeAccount) {\n            this.paymentSheet.stripeAccount = this.stripeAccount;\n        }\n        this.paymentSheet.applicationName = '@capacitor-community/stripe';\n        // eslint-disable-next-line no-prototype-builtins\n        if (options.hasOwnProperty('paymentIntentClientSecret')) {\n            this.paymentSheet.intentType = 'payment';\n            this.paymentSheet.intentClientSecret = options.paymentIntentClientSecret;\n        }\n        else {\n            this.paymentSheet.intentType = 'setup';\n            this.paymentSheet.intentClientSecret = options.setupIntentClientSecret;\n        }\n        if (options.withZipCode !== undefined) {\n            this.paymentSheet.zip = options.withZipCode;\n        }\n        if (isPlatform(window, 'ios')) {\n            this.paymentSheet.buttonLabel = 'Add card';\n            this.paymentSheet.sheetTitle = 'Add a card';\n        }\n        else {\n            this.paymentSheet.buttonLabel = 'Add';\n        }\n        this.notifyListeners(PaymentFlowEventsEnum.Loaded, null);\n    }\n    async presentPaymentFlow() {\n        if (!this.paymentSheet) {\n            throw new Error();\n        }\n        this.notifyListeners(PaymentFlowEventsEnum.Opened, null);\n        const props = await this.paymentSheet.present().catch(() => undefined);\n        if (props === undefined) {\n            this.notifyListeners(PaymentFlowEventsEnum.Canceled, null);\n            throw new Error();\n        }\n        const { detail: { stripe, cardNumberElement }, } = props;\n        const { token } = await stripe.createToken(cardNumberElement);\n        if (token === undefined || token.card === undefined) {\n            throw new Error();\n        }\n        this.flowStripe = stripe;\n        this.flowCardNumberElement = cardNumberElement;\n        this.notifyListeners(PaymentFlowEventsEnum.Created, {\n            cardNumber: token.card.last4,\n        });\n        return {\n            cardNumber: token.card.last4,\n        };\n    }\n    async confirmPaymentFlow() {\n        if (!this.paymentSheet || !this.flowStripe || !this.flowCardNumberElement) {\n            throw new Error();\n        }\n        const result = await this.flowStripe.createPaymentMethod({\n            type: 'card',\n            card: this.flowCardNumberElement,\n        });\n        if (result.error !== undefined) {\n            this.notifyListeners(PaymentFlowEventsEnum.Failed, null);\n        }\n        this.paymentSheet.updateProgress('success');\n        this.paymentSheet.remove();\n        this.notifyListeners(PaymentFlowEventsEnum.Completed, null);\n        return {\n            paymentResult: PaymentFlowEventsEnum.Completed,\n        };\n    }\n    isApplePayAvailable() {\n        return this.isAvailable('applePay');\n    }\n    async createApplePay(createApplePayOption) {\n        if (!this.publishableKey) {\n            this.notifyListeners(ApplePayEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.requestApplePay = await this.createPaymentRequestButton();\n        this.requestApplePayOptions = createApplePayOption;\n        this.notifyListeners(ApplePayEventsEnum.Loaded, null);\n    }\n    presentApplePay() {\n        return this.presentPaymentRequestButton('applePay', this.requestApplePay, this.requestApplePayOptions, ApplePayEventsEnum);\n    }\n    isGooglePayAvailable() {\n        return this.isAvailable('googlePay');\n    }\n    async createGooglePay(createGooglePayOption) {\n        if (!this.publishableKey) {\n            this.notifyListeners(GooglePayEventsEnum.FailedToLoad, null);\n            return;\n        }\n        this.requestGooglePay = await this.createPaymentRequestButton();\n        this.requestGooglePayOptions = createGooglePayOption;\n        this.notifyListeners(GooglePayEventsEnum.Loaded, null);\n    }\n    presentGooglePay() {\n        return this.presentPaymentRequestButton('googlePay', this.requestGooglePay, this.requestGooglePayOptions, GooglePayEventsEnum);\n    }\n    async isAvailable(type) {\n        var _a;\n        const requestButton = document.createElement('stripe-payment-request-button');\n        requestButton.id = `isAvailable-${type}`;\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(requestButton);\n        await customElements.whenDefined('stripe-payment-request-button');\n        if (this.publishableKey) {\n            requestButton.publishableKey = this.publishableKey;\n        }\n        if (this.stripeAccount) {\n            requestButton.stripeAccount = this.stripeAccount;\n        }\n        requestButton.applicationName = '@capacitor-community/stripe';\n        return await requestButton.isAvailable(type).finally(() => requestButton.remove());\n    }\n    async createPaymentRequestButton() {\n        var _a;\n        const requestButton = document.createElement('stripe-payment-request-button');\n        (_a = document.querySelector('body')) === null || _a === void 0 ? void 0 : _a.appendChild(requestButton);\n        await customElements.whenDefined('stripe-payment-request-button');\n        if (this.publishableKey) {\n            requestButton.publishableKey = this.publishableKey;\n        }\n        if (this.stripeAccount) {\n            requestButton.stripeAccount = this.stripeAccount;\n        }\n        requestButton.applicationName = '@capacitor-community/stripe';\n        return requestButton;\n    }\n    async presentPaymentRequestButton(type, requestButton, requestButtonOptions, EventsEnum) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise(async (resolve) => {\n            if (requestButton === undefined || requestButtonOptions === undefined || this.publishableKey === undefined) {\n                this.notifyListeners(EventsEnum.Failed, null);\n                return resolve({\n                    paymentResult: EventsEnum.Failed,\n                });\n            }\n            await requestButton.setPaymentRequestOption({\n                country: requestButtonOptions.countryCode.toUpperCase(),\n                currency: requestButtonOptions.currency.toLowerCase(),\n                total: requestButtonOptions.paymentSummaryItems[requestButtonOptions.paymentSummaryItems.length - 1],\n                disableWallets: type === 'applePay' ? ['googlePay', 'browserCard'] : ['applePay', 'browserCard'],\n                requestPayerName: true,\n                requestPayerEmail: true,\n            });\n            // await this.requestButton.setPaymentRequestShippingAddressEventHandler(async (event, stripe) => {});\n            const intentClientSecret = requestButtonOptions.paymentIntentClientSecret;\n            await requestButton.setPaymentMethodEventHandler(async (event, stripe) => {\n                const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(intentClientSecret, {\n                    payment_method: event.paymentMethod.id,\n                }, { handleActions: false });\n                if (confirmError) {\n                    event.complete('fail');\n                    this.notifyListeners(EventsEnum.Failed, confirmError);\n                    return resolve({\n                        paymentResult: EventsEnum.Failed,\n                    });\n                }\n                if ((paymentIntent === null || paymentIntent === void 0 ? void 0 : paymentIntent.status) === 'requires_action') {\n                    const { error: confirmError } = await stripe.confirmCardPayment(intentClientSecret);\n                    if (confirmError) {\n                        event.complete('fail');\n                        this.notifyListeners(EventsEnum.Failed, confirmError);\n                        return resolve({\n                            paymentResult: EventsEnum.Failed,\n                        });\n                    }\n                }\n                event.complete('success');\n                this.notifyListeners(EventsEnum.Completed, null);\n                return resolve({\n                    paymentResult: EventsEnum.Completed,\n                });\n            });\n            await requestButton.initStripe(this.publishableKey, {\n                stripeAccount: this.stripeAccount,\n                showButton: false,\n            });\n        });\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["ApplePayEventsEnum", "GooglePayEventsEnum", "PaymentFlowEventsEnum", "PaymentSheetEventsEnum", "registerPlugin", "WebPlugin"], "mappings": ";;;;AAAWA;AACX,CAAC,UAAU,kBAAkB,EAAE;AAC/B,IAAI,kBAAkB,CAAC,QAAQ,CAAC,GAAG,gBAAgB;AACnD,IAAI,kBAAkB,CAAC,cAAc,CAAC,GAAG,sBAAsB;AAC/D,IAAI,kBAAkB,CAAC,WAAW,CAAC,GAAG,mBAAmB;AACzD,IAAI,kBAAkB,CAAC,UAAU,CAAC,GAAG,kBAAkB;AACvD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,GAAG,gBAAgB;AACnD,IAAI,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,kCAAkC;AACvF,IAAI,kBAAkB,CAAC,wBAAwB,CAAC,GAAG,gCAAgC;AACnF,CAAC,EAAEA,0BAAkB,KAAKA,0BAAkB,GAAG,EAAE,CAAC,CAAC;;ACTxCC;AACX,CAAC,UAAU,mBAAmB,EAAE;AAChC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,GAAG,iBAAiB;AACrD,IAAI,mBAAmB,CAAC,cAAc,CAAC,GAAG,uBAAuB;AACjE,IAAI,mBAAmB,CAAC,WAAW,CAAC,GAAG,oBAAoB;AAC3D,IAAI,mBAAmB,CAAC,UAAU,CAAC,GAAG,mBAAmB;AACzD,IAAI,mBAAmB,CAAC,QAAQ,CAAC,GAAG,iBAAiB;AACrD,CAAC,EAAEA,2BAAmB,KAAKA,2BAAmB,GAAG,EAAE,CAAC,CAAC;;ACP1CC;AACX,CAAC,UAAU,qBAAqB,EAAE;AAClC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;AACzD,IAAI,qBAAqB,CAAC,cAAc,CAAC,GAAG,yBAAyB;AACrE,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;AACzD,IAAI,qBAAqB,CAAC,SAAS,CAAC,GAAG,oBAAoB;AAC3D,IAAI,qBAAqB,CAAC,WAAW,CAAC,GAAG,sBAAsB;AAC/D,IAAI,qBAAqB,CAAC,UAAU,CAAC,GAAG,qBAAqB;AAC7D,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;AACzD,CAAC,EAAEA,6BAAqB,KAAKA,6BAAqB,GAAG,EAAE,CAAC,CAAC;;ACT9CC;AACX,CAAC,UAAU,sBAAsB,EAAE;AACnC,IAAI,sBAAsB,CAAC,QAAQ,CAAC,GAAG,oBAAoB;AAC3D,IAAI,sBAAsB,CAAC,cAAc,CAAC,GAAG,0BAA0B;AACvE,IAAI,sBAAsB,CAAC,WAAW,CAAC,GAAG,uBAAuB;AACjE,IAAI,sBAAsB,CAAC,UAAU,CAAC,GAAG,sBAAsB;AAC/D,IAAI,sBAAsB,CAAC,QAAQ,CAAC,GAAG,oBAAoB;AAC3D,CAAC,EAAEA,8BAAsB,KAAKA,8BAAsB,GAAG,EAAE,CAAC,CAAC;;ACNtD,MAAC,MAAM,GAAGC,mBAAc,CAAC,QAAQ,EAAE;AACxC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;AAC7D,CAAC;;ACHD;AACA;AACA;AACA;AACO,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK,cAAc,CAAC,GAAG,CAAC;AACjD,MAAM,UAAU,GAAG,CAAC,aAAa,EAAE,QAAQ,KAAK;AACvD,IAAI,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AAC3C,QAAQ,QAAQ,GAAG,aAAa;AAChC,QAAQ,aAAa,GAAG,SAAS;AACjC;AACA,IAAI,OAAO,YAAY,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACzD,CAAC;AACM,MAAM,cAAc,GAAG,CAAC,GAAG,GAAG,MAAM,KAAK;AAChD,IAAI,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AACpC,QAAQ,OAAO,EAAE;AACjB;AACA,IAAI,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE;AAC/B,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS;AACvC,IAAI,IAAI,SAAS,IAAI,IAAI,EAAE;AAC3B,QAAQ,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC;AAC9D,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF;AACA,IAAI,OAAO,SAAS;AACpB,CAAC;AACD,MAAM,eAAe,GAAG,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9F,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC5D,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK;AACxB;AACA,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE;AACrC,QAAQ,OAAO,IAAI;AACnB;AACA;AACA,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC3D,QAAQ,OAAO,IAAI;AACnB;AACA,IAAI,OAAO,KAAK;AAChB,CAAC;AACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC;AACvD,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC;AACxE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,eAAe,CAAC;AAC9D,MAAM,eAAe,GAAG,CAAC,GAAG,KAAK;AACjC,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC;AAC3D,CAAC;AACD,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK;AAC3B,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU;AAChC,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAC3C,IAAI,OAAO,CAAC,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG;AAC5C,SAAS,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,GAAG,CAAC;AACxC,CAAC;AACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;AAC1B,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU;AAChC,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,WAAW;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAC3C,IAAI,QAAQ,MAAM,CAAC,GAAG,CAAC;AACvB,QAAQ,eAAe,CAAC,GAAG,CAAC;AAC5B,SAAS,CAAC,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG;AAC1C,aAAa,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC;AAC9C,CAAC;AACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE,sBAAsB,CAAC;AACjE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;AACzC,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC;AAClE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;AACnF,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACnC,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC;AACtC,IAAI,OAAO,CAAC,EAAE,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,SAAM,GAAG,SAAM,GAAG,SAAS,CAAC,QAAQ,CAAC;AACvF,CAAC;AACD,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC;AAC3D,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC;AACpG,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC;AAC9E,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO;AAChE,MAAM,aAAa,GAAG;AACtB,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,WAAW,EAAE,iBAAiB;AAClC,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,WAAW,EAAE,WAAW;AAC5B,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,QAAQ,EAAE;AACd,CAAC;;ACrFM,MAAM,SAAS,SAASC,cAAS,CAAC;AACzC,IAAI,MAAM,UAAU,CAAC,OAAO,EAAE;AAC9B,QAAQ,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;AACtG,YAAY,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;AAC3D;AACA,QAAQ,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc;AACpD,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;AACnC,YAAY,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa;AACtD;AACA;AACA,IAAI,MAAM,kBAAkB,CAAC,OAAO,EAAE;AACtC,QAAQ,IAAI,EAAE;AACd,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAClC,YAAY,IAAI,CAAC,eAAe,CAACF,8BAAsB,CAAC,YAAY,EAAE,IAAI,CAAC;AAC3E,YAAY;AACZ;AACA,QAAQ,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC;AAC1E,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AACpH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,sBAAsB,CAAC;AAChE,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;AAC9D,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;AAChC,YAAY,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;AAChE;AACA,QAAQ,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,6BAA6B;AACzE,QAAQ,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB;AAChF,QAAQ,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS;AAChD,QAAQ,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;AAC/C,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW;AACvD;AACA,QAAQ,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,MAAM,EAAE,IAAI,CAAC;AACjE;AACA,IAAI,MAAM,mBAAmB,GAAG;AAChC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAChC,YAAY,MAAM,IAAI,KAAK,EAAE;AAC7B;AACA,QAAQ,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACvD,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;AACjC,YAAY,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC;AACvE,YAAY,OAAO;AACnB,gBAAgB,aAAa,EAAEA,8BAAsB,CAAC,QAAQ;AAC9D,aAAa;AACb;AACA,QAAQ,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,GAAG,KAAK;AAChE,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC;AACxD,YAAY,IAAI,EAAE,MAAM;AACxB,YAAY,IAAI,EAAE,iBAAiB;AACnC,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;AACnD,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AAClC,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;AACxC,YAAY,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,MAAM,EAAE,IAAI,CAAC;AACrE,YAAY,OAAO;AACnB,gBAAgB,aAAa,EAAEA,8BAAsB,CAAC,MAAM;AAC5D,aAAa;AACb;AACA,QAAQ,IAAI,CAAC,eAAe,CAACA,8BAAsB,CAAC,SAAS,EAAE,IAAI,CAAC;AACpE,QAAQ,OAAO;AACf,YAAY,aAAa,EAAEA,8BAAsB,CAAC,SAAS;AAC3D,SAAS;AACT;AACA,IAAI,MAAM,iBAAiB,CAAC,OAAO,EAAE;AACrC,QAAQ,IAAI,EAAE;AACd,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAClC,YAAY,IAAI,CAAC,eAAe,CAACD,6BAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;AAC1E,YAAY;AACZ;AACA,QAAQ,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC;AAC1E,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AACpH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,sBAAsB,CAAC;AAChE,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;AAC9D,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;AAChC,YAAY,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;AAChE;AACA,QAAQ,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,6BAA6B;AACzE;AACA,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAE;AACjE,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS;AACpD,YAAY,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB;AACpF;AACA,aAAa;AACb,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,OAAO;AAClD,YAAY,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB;AAClF;AACA,QAAQ,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;AAC/C,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW;AACvD;AACA,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;AACvC,YAAY,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,UAAU;AACtD,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,YAAY;AACvD;AACA,aAAa;AACb,YAAY,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,KAAK;AACjD;AACA,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;AAChE;AACA,IAAI,MAAM,kBAAkB,GAAG;AAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAChC,YAAY,MAAM,IAAI,KAAK,EAAE;AAC7B;AACA,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;AAChE,QAAQ,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC;AAC9E,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;AACjC,YAAY,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC;AACtE,YAAY,MAAM,IAAI,KAAK,EAAE;AAC7B;AACA,QAAQ,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,GAAG,KAAK;AAChE,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC;AACrE,QAAQ,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;AAC7D,YAAY,MAAM,IAAI,KAAK,EAAE;AAC7B;AACA,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM;AAChC,QAAQ,IAAI,CAAC,qBAAqB,GAAG,iBAAiB;AACtD,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,OAAO,EAAE;AAC5D,YAAY,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AACxC,SAAS,CAAC;AACV,QAAQ,OAAO;AACf,YAAY,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AACxC,SAAS;AACT;AACA,IAAI,MAAM,kBAAkB,GAAG;AAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AACnF,YAAY,MAAM,IAAI,KAAK,EAAE;AAC7B;AACA,QAAQ,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;AACjE,YAAY,IAAI,EAAE,MAAM;AACxB,YAAY,IAAI,EAAE,IAAI,CAAC,qBAAqB;AAC5C,SAAS,CAAC;AACV,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;AACxC,YAAY,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;AACpE;AACA,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;AACnD,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AAClC,QAAQ,IAAI,CAAC,eAAe,CAACA,6BAAqB,CAAC,SAAS,EAAE,IAAI,CAAC;AACnE,QAAQ,OAAO;AACf,YAAY,aAAa,EAAEA,6BAAqB,CAAC,SAAS;AAC1D,SAAS;AACT;AACA,IAAI,mBAAmB,GAAG;AAC1B,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;AAC3C;AACA,IAAI,MAAM,cAAc,CAAC,oBAAoB,EAAE;AAC/C,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAClC,YAAY,IAAI,CAAC,eAAe,CAACF,0BAAkB,CAAC,YAAY,EAAE,IAAI,CAAC;AACvE,YAAY;AACZ;AACA,QAAQ,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE;AACtE,QAAQ,IAAI,CAAC,sBAAsB,GAAG,oBAAoB;AAC1D,QAAQ,IAAI,CAAC,eAAe,CAACA,0BAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;AAC7D;AACA,IAAI,eAAe,GAAG;AACtB,QAAQ,OAAO,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,sBAAsB,EAAEA,0BAAkB,CAAC;AAClI;AACA,IAAI,oBAAoB,GAAG;AAC3B,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;AAC5C;AACA,IAAI,MAAM,eAAe,CAAC,qBAAqB,EAAE;AACjD,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAClC,YAAY,IAAI,CAAC,eAAe,CAACC,2BAAmB,CAAC,YAAY,EAAE,IAAI,CAAC;AACxE,YAAY;AACZ;AACA,QAAQ,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE;AACvE,QAAQ,IAAI,CAAC,uBAAuB,GAAG,qBAAqB;AAC5D,QAAQ,IAAI,CAAC,eAAe,CAACA,2BAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;AAC9D;AACA,IAAI,gBAAgB,GAAG;AACvB,QAAQ,OAAO,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,EAAEA,2BAAmB,CAAC;AACtI;AACA,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE;AAC5B,QAAQ,IAAI,EAAE;AACd,QAAQ,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC;AACrF,QAAQ,aAAa,CAAC,EAAE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAChD,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;AAChH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,+BAA+B,CAAC;AACzE,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;AACjC,YAAY,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;AAC9D;AACA,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;AAChC,YAAY,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;AAC5D;AACA,QAAQ,aAAa,CAAC,eAAe,GAAG,6BAA6B;AACrE,QAAQ,OAAO,MAAM,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,aAAa,CAAC,MAAM,EAAE,CAAC;AAC1F;AACA,IAAI,MAAM,0BAA0B,GAAG;AACvC,QAAQ,IAAI,EAAE;AACd,QAAQ,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC;AACrF,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,SAAM,GAAG,SAAM,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;AAChH,QAAQ,MAAM,cAAc,CAAC,WAAW,CAAC,+BAA+B,CAAC;AACzE,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;AACjC,YAAY,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;AAC9D;AACA,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;AAChC,YAAY,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;AAC5D;AACA,QAAQ,aAAa,CAAC,eAAe,GAAG,6BAA6B;AACrE,QAAQ,OAAO,aAAa;AAC5B;AACA,IAAI,MAAM,2BAA2B,CAAC,IAAI,EAAE,aAAa,EAAE,oBAAoB,EAAE,UAAU,EAAE;AAC7F;AACA,QAAQ,OAAO,IAAI,OAAO,CAAC,OAAO,OAAO,KAAK;AAC9C,YAAY,IAAI,aAAa,KAAK,SAAS,IAAI,oBAAoB,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;AACxH,gBAAgB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;AAC7D,gBAAgB,OAAO,OAAO,CAAC;AAC/B,oBAAoB,aAAa,EAAE,UAAU,CAAC,MAAM;AACpD,iBAAiB,CAAC;AAClB;AACA,YAAY,MAAM,aAAa,CAAC,uBAAuB,CAAC;AACxD,gBAAgB,OAAO,EAAE,oBAAoB,CAAC,WAAW,CAAC,WAAW,EAAE;AACvE,gBAAgB,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,CAAC,WAAW,EAAE;AACrE,gBAAgB,KAAK,EAAE,oBAAoB,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;AACpH,gBAAgB,cAAc,EAAE,IAAI,KAAK,UAAU,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC;AAChH,gBAAgB,gBAAgB,EAAE,IAAI;AACtC,gBAAgB,iBAAiB,EAAE,IAAI;AACvC,aAAa,CAAC;AACd;AACA,YAAY,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,yBAAyB;AACrF,YAAY,MAAM,aAAa,CAAC,4BAA4B,CAAC,OAAO,KAAK,EAAE,MAAM,KAAK;AACtF,gBAAgB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;AACnH,oBAAoB,cAAc,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE;AAC1D,iBAAiB,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;AAC5C,gBAAgB,IAAI,YAAY,EAAE;AAClC,oBAAoB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1C,oBAAoB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC;AACzE,oBAAoB,OAAO,OAAO,CAAC;AACnC,wBAAwB,aAAa,EAAE,UAAU,CAAC,MAAM;AACxD,qBAAqB,CAAC;AACtB;AACA,gBAAgB,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,SAAM,GAAG,SAAM,GAAG,aAAa,CAAC,MAAM,MAAM,iBAAiB,EAAE;AAChI,oBAAoB,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;AACvG,oBAAoB,IAAI,YAAY,EAAE;AACtC,wBAAwB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC9C,wBAAwB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC;AAC7E,wBAAwB,OAAO,OAAO,CAAC;AACvC,4BAA4B,aAAa,EAAE,UAAU,CAAC,MAAM;AAC5D,yBAAyB,CAAC;AAC1B;AACA;AACA,gBAAgB,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;AACzC,gBAAgB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;AAChE,gBAAgB,OAAO,OAAO,CAAC;AAC/B,oBAAoB,aAAa,EAAE,UAAU,CAAC,SAAS;AACvD,iBAAiB,CAAC;AAClB,aAAa,CAAC;AACd,YAAY,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE;AAChE,gBAAgB,aAAa,EAAE,IAAI,CAAC,aAAa;AACjD,gBAAgB,UAAU,EAAE,KAAK;AACjC,aAAa,CAAC;AACd,SAAS,CAAC;AACV;AACA;;;;;;;;;"}