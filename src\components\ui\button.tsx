import * as React from "react"
import styled from "styled-components"
import { Slot } from "@radix-ui/react-slot"
import { buttonVariants, buttonSizes, transition, focusRing } from "../../styles/utils"

// Tipos para las variantes del botón
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'default' | 'link'
export type ButtonSize = 'sm' | 'md' | 'lg' | 'icon'

interface StyledButtonProps {
  $variant: ButtonVariant
  $size: ButtonSize
}

const StyledButton = styled.button<StyledButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  font-weight: 500;
  cursor: pointer;
  ${transition()}
  ${focusRing}

  /* Aplicar variante */
  ${({ $variant }) => buttonVariants[$variant as keyof typeof buttonVariants]}

  /* Aplicar tamaño */
  ${({ $size }) => buttonSizes[$size as keyof typeof buttonSizes]}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant
  size?: ButtonSize
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'md', asChild = false, children, ...props }, ref) => {
    if (asChild) {
      return (
        <Slot ref={ref} {...props}>
          {children}
        </Slot>
      )
    }

    return (
      <StyledButton
        ref={ref}
        $variant={variant}
        $size={size}
        {...props}
      >
        {children}
      </StyledButton>
    )
  }
)

Button.displayName = "Button"

export { Button }
