import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { User, ShoppingCart, Product, ShoppingCartItem } from '../../types'
import { LocalStorageService } from '../services/localStorageService'
import { calculateCartTotal } from '../lib/utils'

// Tipos para el estado de la aplicación
interface AppState {
  user: User | null
  cart: ShoppingCart
  products: Product[]
  loading: boolean
  error: string | null
}

// Tipos para las acciones
type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_TO_CART'; payload: Product }
  | { type: 'UPDATE_CART_ITEM'; payload: { productId: string; quantity: number } }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOAD_FROM_STORAGE' }

// Estado inicial
const initialState: AppState = {
  user: null,
  cart: LocalStorageService.getDefaultCart(),
  products: [],
  loading: false,
  error: null
}

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload }
    
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload }
    
    case 'ADD_TO_CART': {
      const product = action.payload
      const existingItem = state.cart.items.find(item => item.productId === product.id)
      
      let newItems: ShoppingCartItem[]
      
      if (existingItem) {
        newItems = state.cart.items.map(item =>
          item.productId === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      } else {
        const newItem: ShoppingCartItem = {
          productId: product.id,
          name: product.name,
          quantity: 1,
          unitPrice: product.price,
          imageUrl: product.imageUrl
        }
        newItems = [...state.cart.items, newItem]
      }
      
      const newCart: ShoppingCart = {
        items: newItems,
        totalAmount: calculateCartTotal(newItems),
        updatedAt: new Date().toISOString()
      }
      
      LocalStorageService.saveShoppingCart(newCart)
      return { ...state, cart: newCart }
    }
    
    case 'UPDATE_CART_ITEM': {
      const { productId, quantity } = action.payload
      
      let newItems: ShoppingCartItem[]
      
      if (quantity <= 0) {
        newItems = state.cart.items.filter(item => item.productId !== productId)
      } else {
        newItems = state.cart.items.map(item =>
          item.productId === productId
            ? { ...item, quantity }
            : item
        )
      }
      
      const newCart: ShoppingCart = {
        items: newItems,
        totalAmount: calculateCartTotal(newItems),
        updatedAt: new Date().toISOString()
      }
      
      LocalStorageService.saveShoppingCart(newCart)
      return { ...state, cart: newCart }
    }
    
    case 'REMOVE_FROM_CART': {
      const newItems = state.cart.items.filter(item => item.productId !== action.payload)
      const newCart: ShoppingCart = {
        items: newItems,
        totalAmount: calculateCartTotal(newItems),
        updatedAt: new Date().toISOString()
      }
      
      LocalStorageService.saveShoppingCart(newCart)
      return { ...state, cart: newCart }
    }
    
    case 'CLEAR_CART': {
      const newCart = LocalStorageService.getDefaultCart()
      LocalStorageService.saveShoppingCart(newCart)
      return { ...state, cart: newCart }
    }
    
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    
    case 'LOAD_FROM_STORAGE': {
      const storedUser = LocalStorageService.getSessionUser()
      const storedCart = LocalStorageService.getShoppingCart() || LocalStorageService.getDefaultCart()
      
      return {
        ...state,
        user: storedUser,
        cart: storedCart
      }
    }
    
    default:
      return state
  }
}

// Contexto
const AppContext = createContext<{
  state: AppState
  dispatch: React.Dispatch<AppAction>
} | null>(null)

// Provider
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // Cargar datos del localStorage al inicializar
  useEffect(() => {
    dispatch({ type: 'LOAD_FROM_STORAGE' })
  }, [])

  // Guardar usuario en localStorage cuando cambie
  useEffect(() => {
    if (state.user) {
      LocalStorageService.saveSessionUser(state.user)
    } else {
      LocalStorageService.removeSessionUser()
    }
  }, [state.user])

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  )
}

// Hook personalizado
export const useApp = () => {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp debe ser usado dentro de AppProvider')
  }
  return context
}
