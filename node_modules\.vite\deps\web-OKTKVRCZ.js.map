{"version": 3, "sources": ["../../@capacitor-firebase/authentication/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nimport type {\n  ConfirmationR<PERSON>ult,\n  AuthCredential as FirebaseAuthCredential,\n  AuthProvider as FirebaseAuthProvider,\n  CustomParameters as FirebaseCustomParameters,\n  User as FirebaseUser,\n  UserCredential as FirebaseUserCredential,\n  UserInfo as FirebaseUserInfo,\n  UserMetadata as FirebaseUserMeatdata,\n} from 'firebase/auth';\nimport {\n  EmailAuthProvider,\n  FacebookAuthProvider,\n  GithubAuthProvider,\n  GoogleAuthProvider,\n  OAuthCredential,\n  OAuthProvider,\n  RecaptchaVerifier,\n  TwitterAuthProvider,\n  applyActionCode,\n  browserLocalPersistence,\n  browserSessionPersistence,\n  confirmPasswordReset,\n  connectAuthEmulator,\n  createUserWithEmailAndPassword,\n  deleteUser,\n  fetchSignInMethodsForEmail,\n  getAdditionalUserInfo,\n  getAuth,\n  getRedirectResult,\n  inMemoryPersistence,\n  indexedDBLocalPersistence,\n  isSignInWithEmailLink,\n  linkWithCredential,\n  linkWithPhoneNumber,\n  linkWithPopup,\n  linkWithRedirect,\n  reload,\n  revokeAccessToken,\n  sendEmailVerification,\n  sendPasswordResetEmail,\n  sendSignInLinkToEmail,\n  setPersistence,\n  signInAnonymously,\n  signInWithCustomToken,\n  signInWithEmailAndPassword,\n  signInWithEmailLink,\n  signInWithPhoneNumber,\n  signInWithPopup,\n  signInWithRedirect,\n  unlink,\n  updateEmail,\n  updatePassword,\n  updateProfile,\n  verifyBeforeUpdateEmail,\n} from 'firebase/auth';\n\nimport type {\n  AdditionalUserInfo,\n  ApplyActionCodeOptions,\n  AuthCredential,\n  AuthStateChange,\n  CheckAppTrackingTransparencyPermissionResult,\n  ConfirmPasswordResetOptions,\n  ConfirmVerificationCodeOptions,\n  CreateUserWithEmailAndPasswordOptions,\n  FetchSignInMethodsForEmailOptions,\n  FetchSignInMethodsForEmailResult,\n  FirebaseAuthenticationPlugin,\n  GetCurrentUserResult,\n  GetIdTokenOptions,\n  GetIdTokenResult,\n  GetTenantIdResult,\n  IsSignInWithEmailLinkOptions,\n  IsSignInWithEmailLinkResult,\n  LinkResult,\n  LinkWithEmailAndPasswordOptions,\n  LinkWithEmailLinkOptions,\n  LinkWithOAuthOptions,\n  LinkWithPhoneNumberOptions,\n  PhoneCodeSentEvent,\n  PhoneVerificationFailedEvent,\n  RequestAppTrackingTransparencyPermissionResult,\n  RevokeAccessTokenOptions,\n  SendEmailVerificationOptions,\n  SendPasswordResetEmailOptions,\n  SendSignInLinkToEmailOptions,\n  SetLanguageCodeOptions,\n  SetPersistenceOptions,\n  SetTenantIdOptions,\n  SignInResult,\n  SignInWithCustomTokenOptions,\n  SignInWithEmailAndPasswordOptions,\n  SignInWithEmailLinkOptions,\n  SignInWithGoogleOptions,\n  SignInWithOAuthOptions,\n  SignInWithOpenIdConnectOptions,\n  SignInWithPhoneNumberOptions,\n  UnlinkOptions,\n  UnlinkResult,\n  UpdateEmailOptions,\n  UpdatePasswordOptions,\n  UpdateProfileOptions,\n  UseEmulatorOptions,\n  User,\n  UserInfo,\n  UserMetadata,\n  VerifyBeforeUpdateEmailOptions,\n} from './definitions';\nimport { Persistence, ProviderId } from './definitions';\n\nexport class FirebaseAuthenticationWeb\n  extends WebPlugin\n  implements FirebaseAuthenticationPlugin\n{\n  public static readonly AUTH_STATE_CHANGE_EVENT = 'authStateChange';\n  public static readonly ID_TOKEN_CHANGE_EVENT = 'idTokenChange';\n  public static readonly PHONE_CODE_SENT_EVENT = 'phoneCodeSent';\n  public static readonly PHONE_VERIFICATION_FAILED_EVENT =\n    'phoneVerificationFailed';\n  public static readonly ERROR_NO_USER_SIGNED_IN = 'No user is signed in.';\n  public static readonly ERROR_PHONE_NUMBER_MISSING =\n    'phoneNumber must be provided.';\n  public static readonly ERROR_RECAPTCHA_VERIFIER_MISSING =\n    'recaptchaVerifier must be provided and must be an instance of RecaptchaVerifier.';\n  public static readonly ERROR_CONFIRMATION_RESULT_MISSING =\n    'No confirmation result with this verification id was found.';\n\n  private lastConfirmationResult: Map<string, ConfirmationResult> = new Map();\n\n  constructor() {\n    super();\n    const auth = getAuth();\n    auth.onAuthStateChanged(user => this.handleAuthStateChange(user));\n    auth.onIdTokenChanged(user => void this.handleIdTokenChange(user));\n  }\n\n  public async applyActionCode(options: ApplyActionCodeOptions): Promise<void> {\n    const auth = getAuth();\n    return applyActionCode(auth, options.oobCode);\n  }\n\n  public async createUserWithEmailAndPassword(\n    options: CreateUserWithEmailAndPasswordOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await createUserWithEmailAndPassword(\n      auth,\n      options.email,\n      options.password,\n    );\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async confirmPasswordReset(\n    options: ConfirmPasswordResetOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return confirmPasswordReset(auth, options.oobCode, options.newPassword);\n  }\n\n  public async confirmVerificationCode(\n    options: ConfirmVerificationCodeOptions,\n  ): Promise<SignInResult> {\n    const { verificationCode, verificationId } = options;\n    const confirmationResult = this.lastConfirmationResult.get(verificationId);\n    if (!confirmationResult) {\n      throw new Error(\n        FirebaseAuthenticationWeb.ERROR_CONFIRMATION_RESULT_MISSING,\n      );\n    }\n    const userCredential = await confirmationResult.confirm(verificationCode);\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async deleteUser(): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return deleteUser(currentUser);\n  }\n\n  public async fetchSignInMethodsForEmail(\n    options: FetchSignInMethodsForEmailOptions,\n  ): Promise<FetchSignInMethodsForEmailResult> {\n    const auth = getAuth();\n    const signInMethods = await fetchSignInMethodsForEmail(auth, options.email);\n    return {\n      signInMethods,\n    };\n  }\n\n  public async getPendingAuthResult(): Promise<SignInResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async getCurrentUser(): Promise<GetCurrentUserResult> {\n    const auth = getAuth();\n    const userResult = this.createUserResult(auth.currentUser);\n    const result: GetCurrentUserResult = {\n      user: userResult,\n    };\n    return result;\n  }\n\n  public async getIdToken(\n    options?: GetIdTokenOptions,\n  ): Promise<GetIdTokenResult> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    const idToken = await auth.currentUser.getIdToken(options?.forceRefresh);\n    const result: GetIdTokenResult = {\n      token: idToken || '',\n    };\n    return result;\n  }\n\n  public async getRedirectResult(): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await getRedirectResult(auth);\n    const authCredential = userCredential\n      ? OAuthProvider.credentialFromResult(userCredential)\n      : null;\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async getTenantId(): Promise<GetTenantIdResult> {\n    const auth = getAuth();\n    return {\n      tenantId: auth.tenantId,\n    };\n  }\n\n  public async isSignInWithEmailLink(\n    options: IsSignInWithEmailLinkOptions,\n  ): Promise<IsSignInWithEmailLinkResult> {\n    const auth = getAuth();\n    return {\n      isSignInWithEmailLink: isSignInWithEmailLink(auth, options.emailLink),\n    };\n  }\n\n  public async linkWithApple(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new OAuthProvider(ProviderId.APPLE);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithEmailAndPassword(\n    options: LinkWithEmailAndPasswordOptions,\n  ): Promise<LinkResult> {\n    const authCredential = EmailAuthProvider.credential(\n      options.email,\n      options.password,\n    );\n    const userCredential =\n      await this.linkCurrentUserWithCredential(authCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithEmailLink(\n    options: LinkWithEmailLinkOptions,\n  ): Promise<LinkResult> {\n    const authCredential = EmailAuthProvider.credentialWithLink(\n      options.email,\n      options.emailLink,\n    );\n    const userCredential =\n      await this.linkCurrentUserWithCredential(authCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithFacebook(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new FacebookAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      FacebookAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithGameCenter(): Promise<LinkResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async linkWithGithub(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new GithubAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GithubAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithGoogle(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new GoogleAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GoogleAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithMicrosoft(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new OAuthProvider(ProviderId.MICROSOFT);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithOpenIdConnect(\n    options: SignInWithOpenIdConnectOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(options.providerId);\n    this.applySignInOptions(options, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithPhoneNumber(\n    options: LinkWithPhoneNumberOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    if (!options.phoneNumber) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n    }\n    if (\n      !options.recaptchaVerifier ||\n      !(options.recaptchaVerifier instanceof RecaptchaVerifier)\n    ) {\n      throw new Error(\n        FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING,\n      );\n    }\n    try {\n      const confirmationResult = await linkWithPhoneNumber(\n        currentUser,\n        options.phoneNumber,\n        options.recaptchaVerifier,\n      );\n      const { verificationId } = confirmationResult;\n      this.lastConfirmationResult.set(verificationId, confirmationResult);\n      const event: PhoneCodeSentEvent = {\n        verificationId,\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT,\n        event,\n      );\n    } catch (error) {\n      const event: PhoneVerificationFailedEvent = {\n        message: this.getErrorMessage(error),\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT,\n        event,\n      );\n    }\n  }\n\n  public async linkWithPlayGames(): Promise<LinkResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async linkWithTwitter(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new TwitterAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      TwitterAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async linkWithYahoo(\n    options?: LinkWithOAuthOptions,\n  ): Promise<LinkResult> {\n    const provider = new OAuthProvider(ProviderId.YAHOO);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.linkCurrentUserWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async reload(): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return reload(currentUser);\n  }\n\n  public async revokeAccessToken(\n    options: RevokeAccessTokenOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return revokeAccessToken(auth, options.token);\n  }\n\n  public async sendEmailVerification(\n    options: SendEmailVerificationOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return sendEmailVerification(currentUser, options?.actionCodeSettings);\n  }\n\n  public async sendPasswordResetEmail(\n    options: SendPasswordResetEmailOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return sendPasswordResetEmail(\n      auth,\n      options.email,\n      options.actionCodeSettings,\n    );\n  }\n\n  public async sendSignInLinkToEmail(\n    options: SendSignInLinkToEmailOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    return sendSignInLinkToEmail(\n      auth,\n      options.email,\n      options.actionCodeSettings,\n    );\n  }\n\n  public async setLanguageCode(options: SetLanguageCodeOptions): Promise<void> {\n    const auth = getAuth();\n    auth.languageCode = options.languageCode;\n  }\n\n  public async setPersistence(options: SetPersistenceOptions): Promise<void> {\n    const auth = getAuth();\n    switch (options.persistence) {\n      case Persistence.BrowserLocal:\n        await setPersistence(auth, browserLocalPersistence);\n        break;\n      case Persistence.BrowserSession:\n        await setPersistence(auth, browserSessionPersistence);\n        break;\n      case Persistence.IndexedDbLocal:\n        await setPersistence(auth, indexedDBLocalPersistence);\n        break;\n      case Persistence.InMemory:\n        await setPersistence(auth, inMemoryPersistence);\n        break;\n    }\n  }\n\n  public async setTenantId(options: SetTenantIdOptions): Promise<void> {\n    const auth = getAuth();\n    auth.tenantId = options.tenantId;\n  }\n\n  public async signInAnonymously(): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInAnonymously(auth);\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithApple(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(ProviderId.APPLE);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithCustomToken(\n    options: SignInWithCustomTokenOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInWithCustomToken(auth, options.token);\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithEmailAndPassword(\n    options: SignInWithEmailAndPasswordOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInWithEmailAndPassword(\n      auth,\n      options.email,\n      options.password,\n    );\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithEmailLink(\n    options: SignInWithEmailLinkOptions,\n  ): Promise<SignInResult> {\n    const auth = getAuth();\n    const userCredential = await signInWithEmailLink(\n      auth,\n      options.email,\n      options.emailLink,\n    );\n    return this.createSignInResult(userCredential, null);\n  }\n\n  public async signInWithFacebook(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new FacebookAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      FacebookAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithGithub(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new GithubAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GithubAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithGoogle(\n    options?: SignInWithGoogleOptions,\n  ): Promise<SignInResult> {\n    const provider = new GoogleAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      GoogleAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithMicrosoft(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(ProviderId.MICROSOFT);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithOpenIdConnect(\n    options: SignInWithOpenIdConnectOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(options.providerId);\n    this.applySignInOptions(options, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithPhoneNumber(\n    options: SignInWithPhoneNumberOptions,\n  ): Promise<void> {\n    if (!options.phoneNumber) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_PHONE_NUMBER_MISSING);\n    }\n    if (\n      !options.recaptchaVerifier ||\n      !(options.recaptchaVerifier instanceof RecaptchaVerifier)\n    ) {\n      throw new Error(\n        FirebaseAuthenticationWeb.ERROR_RECAPTCHA_VERIFIER_MISSING,\n      );\n    }\n    const auth = getAuth();\n    try {\n      const confirmationResult = await signInWithPhoneNumber(\n        auth,\n        options.phoneNumber,\n        options.recaptchaVerifier,\n      );\n      const { verificationId } = confirmationResult;\n      this.lastConfirmationResult.set(verificationId, confirmationResult);\n      const event: PhoneCodeSentEvent = {\n        verificationId,\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_CODE_SENT_EVENT,\n        event,\n      );\n    } catch (error) {\n      const event: PhoneVerificationFailedEvent = {\n        message: this.getErrorMessage(error),\n      };\n      this.notifyListeners(\n        FirebaseAuthenticationWeb.PHONE_VERIFICATION_FAILED_EVENT,\n        event,\n      );\n    }\n  }\n\n  public async signInWithPlayGames(): Promise<SignInResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async signInWithGameCenter(): Promise<SignInResult> {\n    this.throwNotAvailableError();\n  }\n\n  public async signInWithTwitter(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new TwitterAuthProvider();\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential =\n      TwitterAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signInWithYahoo(\n    options?: SignInWithOAuthOptions,\n  ): Promise<SignInResult> {\n    const provider = new OAuthProvider(ProviderId.YAHOO);\n    this.applySignInOptions(options || {}, provider);\n    const userCredential = await this.signInWithPopupOrRedirect(\n      provider,\n      options?.mode,\n    );\n    const authCredential = OAuthProvider.credentialFromResult(userCredential);\n    return this.createSignInResult(userCredential, authCredential);\n  }\n\n  public async signOut(): Promise<void> {\n    const auth = getAuth();\n    await auth.signOut();\n  }\n\n  public async unlink(options: UnlinkOptions): Promise<UnlinkResult> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    const user = await unlink(auth.currentUser, options.providerId);\n    const userResult = this.createUserResult(user);\n    const result: UnlinkResult = {\n      user: userResult,\n    };\n    return result;\n  }\n\n  public async updateEmail(options: UpdateEmailOptions): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return updateEmail(currentUser, options.newEmail);\n  }\n\n  public async updatePassword(options: UpdatePasswordOptions): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return updatePassword(currentUser, options.newPassword);\n  }\n\n  public async updateProfile(options: UpdateProfileOptions): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return updateProfile(currentUser, {\n      displayName: options.displayName,\n      photoURL: options.photoUrl,\n    });\n  }\n\n  public async useAppLanguage(): Promise<void> {\n    const auth = getAuth();\n    auth.useDeviceLanguage();\n  }\n\n  public async useEmulator(options: UseEmulatorOptions): Promise<void> {\n    const auth = getAuth();\n    const port = options.port || 9099;\n    const scheme = options.scheme || 'http';\n    if (options.host.includes('://')) {\n      connectAuthEmulator(auth, `${options.host}:${port}`);\n    } else {\n      connectAuthEmulator(auth, `${scheme}://${options.host}:${port}`);\n    }\n  }\n\n  public async verifyBeforeUpdateEmail(\n    options: VerifyBeforeUpdateEmailOptions,\n  ): Promise<void> {\n    const auth = getAuth();\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return verifyBeforeUpdateEmail(\n      currentUser,\n      options?.newEmail,\n      options?.actionCodeSettings,\n    );\n  }\n\n  private handleAuthStateChange(user: FirebaseUser | null): void {\n    const userResult = this.createUserResult(user);\n    const change: AuthStateChange = {\n      user: userResult,\n    };\n    this.notifyListeners(\n      FirebaseAuthenticationWeb.AUTH_STATE_CHANGE_EVENT,\n      change,\n      true,\n    );\n  }\n\n  private async handleIdTokenChange(user: FirebaseUser | null): Promise<void> {\n    if (!user) {\n      return;\n    }\n    const idToken = await user.getIdToken(false);\n    const result: GetIdTokenResult = {\n      token: idToken,\n    };\n    this.notifyListeners(\n      FirebaseAuthenticationWeb.ID_TOKEN_CHANGE_EVENT,\n      result,\n      true,\n    );\n  }\n\n  private applySignInOptions(\n    options: SignInWithOAuthOptions,\n    provider: OAuthProvider | GoogleAuthProvider | FacebookAuthProvider,\n  ) {\n    if (options.customParameters) {\n      const customParameters: FirebaseCustomParameters = {};\n      options.customParameters.map(parameter => {\n        customParameters[parameter.key] = parameter.value;\n      });\n      provider.setCustomParameters(customParameters);\n    }\n    if (options.scopes) {\n      for (const scope of options.scopes) {\n        provider.addScope(scope);\n      }\n    }\n  }\n\n  public signInWithPopupOrRedirect(\n    provider: FirebaseAuthProvider,\n    mode?: 'popup' | 'redirect',\n  ): Promise<FirebaseUserCredential | never> {\n    const auth = getAuth();\n    if (mode === 'redirect') {\n      return signInWithRedirect(auth, provider);\n    } else {\n      return signInWithPopup(auth, provider);\n    }\n  }\n\n  public linkCurrentUserWithPopupOrRedirect(\n    provider: FirebaseAuthProvider,\n    mode?: 'popup' | 'redirect',\n  ): Promise<FirebaseUserCredential | never> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    if (mode === 'redirect') {\n      return linkWithRedirect(auth.currentUser, provider);\n    } else {\n      return linkWithPopup(auth.currentUser, provider);\n    }\n  }\n\n  public linkCurrentUserWithCredential(\n    credential: FirebaseAuthCredential,\n  ): Promise<FirebaseUserCredential> {\n    const auth = getAuth();\n    if (!auth.currentUser) {\n      throw new Error(FirebaseAuthenticationWeb.ERROR_NO_USER_SIGNED_IN);\n    }\n    return linkWithCredential(auth.currentUser, credential);\n  }\n\n  public requestAppTrackingTransparencyPermission(): Promise<RequestAppTrackingTransparencyPermissionResult> {\n    this.throwNotAvailableError();\n  }\n\n  public checkAppTrackingTransparencyPermission(): Promise<CheckAppTrackingTransparencyPermissionResult> {\n    this.throwNotAvailableError();\n  }\n\n  private createSignInResult(\n    userCredential: FirebaseUserCredential | null,\n    authCredential: FirebaseAuthCredential | null,\n  ): SignInResult {\n    const userResult = this.createUserResult(userCredential?.user || null);\n    const credentialResult = this.createCredentialResult(authCredential);\n    const additionalUserInfoResult =\n      this.createAdditionalUserInfoResult(userCredential);\n    const result: SignInResult = {\n      user: userResult,\n      credential: credentialResult,\n      additionalUserInfo: additionalUserInfoResult,\n    };\n    return result;\n  }\n\n  private createCredentialResult(\n    credential: FirebaseAuthCredential | null,\n  ): AuthCredential | null {\n    if (!credential) {\n      return null;\n    }\n    const result: AuthCredential = {\n      providerId: credential.providerId,\n    };\n    if (credential instanceof OAuthCredential) {\n      result.accessToken = credential.accessToken;\n      result.idToken = credential.idToken;\n      result.secret = credential.secret;\n    }\n    return result;\n  }\n\n  private createUserResult(user: FirebaseUser | null): User | null {\n    if (!user) {\n      return null;\n    }\n    const result: User = {\n      displayName: user.displayName,\n      email: user.email,\n      emailVerified: user.emailVerified,\n      isAnonymous: user.isAnonymous,\n      metadata: this.createUserMetadataResult(user.metadata),\n      phoneNumber: user.phoneNumber,\n      photoUrl: user.photoURL,\n      providerData: this.createUserProviderDataResult(user.providerData),\n      providerId: user.providerId,\n      tenantId: user.tenantId,\n      uid: user.uid,\n    };\n    return result;\n  }\n\n  private createUserMetadataResult(\n    metadata: FirebaseUserMeatdata,\n  ): UserMetadata {\n    const result: UserMetadata = {};\n    if (metadata.creationTime) {\n      result.creationTime = Date.parse(metadata.creationTime);\n    }\n    if (metadata.lastSignInTime) {\n      result.lastSignInTime = Date.parse(metadata.lastSignInTime);\n    }\n    return result;\n  }\n\n  private createUserProviderDataResult(\n    providerData: FirebaseUserInfo[],\n  ): UserInfo[] {\n    return providerData.map(data => ({\n      displayName: data.displayName,\n      email: data.email,\n      phoneNumber: data.phoneNumber,\n      photoUrl: data.photoURL,\n      providerId: data.providerId,\n      uid: data.uid,\n    }));\n  }\n\n  private createAdditionalUserInfoResult(\n    credential: FirebaseUserCredential | null,\n  ): AdditionalUserInfo | null {\n    if (!credential) {\n      return null;\n    }\n    const additionalUserInfo = getAdditionalUserInfo(credential);\n    if (!additionalUserInfo) {\n      return null;\n    }\n    const { isNewUser, profile, providerId, username } = additionalUserInfo;\n    const result: AdditionalUserInfo = {\n      isNewUser,\n    };\n    if (providerId !== null) {\n      result.providerId = providerId;\n    }\n    if (profile !== null) {\n      result.profile = profile as { [key: string]: unknown };\n    }\n    if (username !== null && username !== undefined) {\n      result.username = username;\n    }\n    return result;\n  }\n\n  private getErrorMessage(error: unknown): string {\n    if (\n      error instanceof Object &&\n      'message' in error &&\n      typeof error['message'] === 'string'\n    ) {\n      return error['message'];\n    }\n    return JSON.stringify(error);\n  }\n\n  private throwNotAvailableError(): never {\n    throw new Error('Not available on web.');\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHM,IAAO,4BAAP,MAAO,mCACH,UAAS;EAkBjB,cAAA;AACE,UAAK;AAHC,SAAA,yBAA0D,oBAAI,IAAG;AAIvE,UAAM,OAAO,QAAO;AACpB,SAAK,mBAAmB,UAAQ,KAAK,sBAAsB,IAAI,CAAC;AAChE,SAAK,iBAAiB,UAAQ,KAAK,KAAK,oBAAoB,IAAI,CAAC;EACnE;EAEO,MAAM,gBAAgB,SAA+B;AAC1D,UAAM,OAAO,QAAO;AACpB,WAAO,gBAAgB,MAAM,QAAQ,OAAO;EAC9C;EAEO,MAAM,+BACX,SAA8C;AAE9C,UAAM,OAAO,QAAO;AACpB,UAAM,iBAAiB,MAAM,+BAC3B,MACA,QAAQ,OACR,QAAQ,QAAQ;AAElB,WAAO,KAAK,mBAAmB,gBAAgB,IAAI;EACrD;EAEO,MAAM,qBACX,SAAoC;AAEpC,UAAM,OAAO,QAAO;AACpB,WAAO,qBAAqB,MAAM,QAAQ,SAAS,QAAQ,WAAW;EACxE;EAEO,MAAM,wBACX,SAAuC;AAEvC,UAAM,EAAE,kBAAkB,eAAc,IAAK;AAC7C,UAAM,qBAAqB,KAAK,uBAAuB,IAAI,cAAc;AACzE,QAAI,CAAC,oBAAoB;AACvB,YAAM,IAAI,MACR,2BAA0B,iCAAiC;;AAG/D,UAAM,iBAAiB,MAAM,mBAAmB,QAAQ,gBAAgB;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,IAAI;EACrD;EAEO,MAAM,aAAU;AACrB,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,WAAW,WAAW;EAC/B;EAEO,MAAM,2BACX,SAA0C;AAE1C,UAAM,OAAO,QAAO;AACpB,UAAM,gBAAgB,MAAM,2BAA2B,MAAM,QAAQ,KAAK;AAC1E,WAAO;MACL;;EAEJ;EAEO,MAAM,uBAAoB;AAC/B,SAAK,uBAAsB;EAC7B;EAEO,MAAM,iBAAc;AACzB,UAAM,OAAO,QAAO;AACpB,UAAM,aAAa,KAAK,iBAAiB,KAAK,WAAW;AACzD,UAAM,SAA+B;MACnC,MAAM;;AAER,WAAO;EACT;EAEO,MAAM,WACX,SAA2B;AAE3B,UAAM,OAAO,QAAO;AACpB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,UAAM,UAAU,MAAM,KAAK,YAAY,WAAW,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;AACvE,UAAM,SAA2B;MAC/B,OAAO,WAAW;;AAEpB,WAAO;EACT;EAEO,MAAM,oBAAiB;AAC5B,UAAM,OAAO,QAAO;AACpB,UAAM,iBAAiB,MAAM,kBAAkB,IAAI;AACnD,UAAM,iBAAiB,iBACnB,cAAc,qBAAqB,cAAc,IACjD;AACJ,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,cAAW;AACtB,UAAM,OAAO,QAAO;AACpB,WAAO;MACL,UAAU,KAAK;;EAEnB;EAEO,MAAM,sBACX,SAAqC;AAErC,UAAM,OAAO,QAAO;AACpB,WAAO;MACL,uBAAuB,sBAAsB,MAAM,QAAQ,SAAS;;EAExE;EAEO,MAAM,cACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,cAAc,WAAW,KAAK;AACnD,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,yBACX,SAAwC;AAExC,UAAM,iBAAiB,kBAAkB,WACvC,QAAQ,OACR,QAAQ,QAAQ;AAElB,UAAM,iBACJ,MAAM,KAAK,8BAA8B,cAAc;AACzD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,kBACX,SAAiC;AAEjC,UAAM,iBAAiB,kBAAkB,mBACvC,QAAQ,OACR,QAAQ,SAAS;AAEnB,UAAM,iBACJ,MAAM,KAAK,8BAA8B,cAAc;AACzD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,iBACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,qBAAoB;AACzC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,qBAAqB,qBAAqB,cAAc;AAC1D,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,qBAAkB;AAC7B,SAAK,uBAAsB;EAC7B;EAEO,MAAM,eACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,mBAAkB;AACvC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,mBAAmB,qBAAqB,cAAc;AACxD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,eACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,mBAAkB;AACvC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,mBAAmB,qBAAqB,cAAc;AACxD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,kBACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,cAAc,WAAW,SAAS;AACvD,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,sBACX,SAAuC;AAEvC,UAAM,WAAW,IAAI,cAAc,QAAQ,UAAU;AACrD,SAAK,mBAAmB,SAAS,QAAQ;AACzC,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,QAAQ,IAAI;AAEd,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,oBACX,SAAmC;AAEnC,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,QAAI,CAAC,QAAQ,aAAa;AACxB,YAAM,IAAI,MAAM,2BAA0B,0BAA0B;;AAEtE,QACE,CAAC,QAAQ,qBACT,EAAE,QAAQ,6BAA6B,oBACvC;AACA,YAAM,IAAI,MACR,2BAA0B,gCAAgC;;AAG9D,QAAI;AACF,YAAM,qBAAqB,MAAM,oBAC/B,aACA,QAAQ,aACR,QAAQ,iBAAiB;AAE3B,YAAM,EAAE,eAAc,IAAK;AAC3B,WAAK,uBAAuB,IAAI,gBAAgB,kBAAkB;AAClE,YAAM,QAA4B;QAChC;;AAEF,WAAK,gBACH,2BAA0B,uBAC1B,KAAK;aAEA,OAAO;AACd,YAAM,QAAsC;QAC1C,SAAS,KAAK,gBAAgB,KAAK;;AAErC,WAAK,gBACH,2BAA0B,iCAC1B,KAAK;;EAGX;EAEO,MAAM,oBAAiB;AAC5B,SAAK,uBAAsB;EAC7B;EAEO,MAAM,gBACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,oBAAmB;AACxC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,oBAAoB,qBAAqB,cAAc;AACzD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,cACX,SAA8B;AAE9B,UAAM,WAAW,IAAI,cAAc,WAAW,KAAK;AACnD,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,mCAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,SAAM;AACjB,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,OAAO,WAAW;EAC3B;EAEO,MAAM,kBACX,SAAiC;AAEjC,UAAM,OAAO,QAAO;AACpB,WAAO,kBAAkB,MAAM,QAAQ,KAAK;EAC9C;EAEO,MAAM,sBACX,SAAqC;AAErC,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,sBAAsB,aAAa,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,kBAAkB;EACvE;EAEO,MAAM,uBACX,SAAsC;AAEtC,UAAM,OAAO,QAAO;AACpB,WAAO,uBACL,MACA,QAAQ,OACR,QAAQ,kBAAkB;EAE9B;EAEO,MAAM,sBACX,SAAqC;AAErC,UAAM,OAAO,QAAO;AACpB,WAAO,sBACL,MACA,QAAQ,OACR,QAAQ,kBAAkB;EAE9B;EAEO,MAAM,gBAAgB,SAA+B;AAC1D,UAAM,OAAO,QAAO;AACpB,SAAK,eAAe,QAAQ;EAC9B;EAEO,MAAM,eAAe,SAA8B;AACxD,UAAM,OAAO,QAAO;AACpB,YAAQ,QAAQ,aAAa;MAC3B,KAAK,YAAY;AACf,cAAM,eAAe,MAAM,uBAAuB;AAClD;MACF,KAAK,YAAY;AACf,cAAM,eAAe,MAAM,yBAAyB;AACpD;MACF,KAAK,YAAY;AACf,cAAM,eAAe,MAAM,yBAAyB;AACpD;MACF,KAAK,YAAY;AACf,cAAM,eAAe,MAAM,mBAAmB;AAC9C;;EAEN;EAEO,MAAM,YAAY,SAA2B;AAClD,UAAM,OAAO,QAAO;AACpB,SAAK,WAAW,QAAQ;EAC1B;EAEO,MAAM,oBAAiB;AAC5B,UAAM,OAAO,QAAO;AACpB,UAAM,iBAAiB,MAAM,kBAAkB,IAAI;AACnD,WAAO,KAAK,mBAAmB,gBAAgB,IAAI;EACrD;EAEO,MAAM,gBACX,SAAgC;AAEhC,UAAM,WAAW,IAAI,cAAc,WAAW,KAAK;AACnD,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,sBACX,SAAqC;AAErC,UAAM,OAAO,QAAO;AACpB,UAAM,iBAAiB,MAAM,sBAAsB,MAAM,QAAQ,KAAK;AACtE,WAAO,KAAK,mBAAmB,gBAAgB,IAAI;EACrD;EAEO,MAAM,2BACX,SAA0C;AAE1C,UAAM,OAAO,QAAO;AACpB,UAAM,iBAAiB,MAAM,2BAC3B,MACA,QAAQ,OACR,QAAQ,QAAQ;AAElB,WAAO,KAAK,mBAAmB,gBAAgB,IAAI;EACrD;EAEO,MAAM,oBACX,SAAmC;AAEnC,UAAM,OAAO,QAAO;AACpB,UAAM,iBAAiB,MAAM,oBAC3B,MACA,QAAQ,OACR,QAAQ,SAAS;AAEnB,WAAO,KAAK,mBAAmB,gBAAgB,IAAI;EACrD;EAEO,MAAM,mBACX,SAAgC;AAEhC,UAAM,WAAW,IAAI,qBAAoB;AACzC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,qBAAqB,qBAAqB,cAAc;AAC1D,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,iBACX,SAAgC;AAEhC,UAAM,WAAW,IAAI,mBAAkB;AACvC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,mBAAmB,qBAAqB,cAAc;AACxD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,iBACX,SAAiC;AAEjC,UAAM,WAAW,IAAI,mBAAkB;AACvC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,mBAAmB,qBAAqB,cAAc;AACxD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,oBACX,SAAgC;AAEhC,UAAM,WAAW,IAAI,cAAc,WAAW,SAAS;AACvD,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,wBACX,SAAuC;AAEvC,UAAM,WAAW,IAAI,cAAc,QAAQ,UAAU;AACrD,SAAK,mBAAmB,SAAS,QAAQ;AACzC,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,QAAQ,IAAI;AAEd,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,sBACX,SAAqC;AAErC,QAAI,CAAC,QAAQ,aAAa;AACxB,YAAM,IAAI,MAAM,2BAA0B,0BAA0B;;AAEtE,QACE,CAAC,QAAQ,qBACT,EAAE,QAAQ,6BAA6B,oBACvC;AACA,YAAM,IAAI,MACR,2BAA0B,gCAAgC;;AAG9D,UAAM,OAAO,QAAO;AACpB,QAAI;AACF,YAAM,qBAAqB,MAAM,sBAC/B,MACA,QAAQ,aACR,QAAQ,iBAAiB;AAE3B,YAAM,EAAE,eAAc,IAAK;AAC3B,WAAK,uBAAuB,IAAI,gBAAgB,kBAAkB;AAClE,YAAM,QAA4B;QAChC;;AAEF,WAAK,gBACH,2BAA0B,uBAC1B,KAAK;aAEA,OAAO;AACd,YAAM,QAAsC;QAC1C,SAAS,KAAK,gBAAgB,KAAK;;AAErC,WAAK,gBACH,2BAA0B,iCAC1B,KAAK;;EAGX;EAEO,MAAM,sBAAmB;AAC9B,SAAK,uBAAsB;EAC7B;EAEO,MAAM,uBAAoB;AAC/B,SAAK,uBAAsB;EAC7B;EAEO,MAAM,kBACX,SAAgC;AAEhC,UAAM,WAAW,IAAI,oBAAmB;AACxC,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBACJ,oBAAoB,qBAAqB,cAAc;AACzD,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,gBACX,SAAgC;AAEhC,UAAM,WAAW,IAAI,cAAc,WAAW,KAAK;AACnD,SAAK,mBAAmB,WAAW,CAAA,GAAI,QAAQ;AAC/C,UAAM,iBAAiB,MAAM,KAAK,0BAChC,UACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAM,iBAAiB,cAAc,qBAAqB,cAAc;AACxE,WAAO,KAAK,mBAAmB,gBAAgB,cAAc;EAC/D;EAEO,MAAM,UAAO;AAClB,UAAM,OAAO,QAAO;AACpB,UAAM,KAAK,QAAO;EACpB;EAEO,MAAM,OAAO,SAAsB;AACxC,UAAM,OAAO,QAAO;AACpB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,UAAM,OAAO,MAAM,OAAO,KAAK,aAAa,QAAQ,UAAU;AAC9D,UAAM,aAAa,KAAK,iBAAiB,IAAI;AAC7C,UAAM,SAAuB;MAC3B,MAAM;;AAER,WAAO;EACT;EAEO,MAAM,YAAY,SAA2B;AAClD,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,YAAY,aAAa,QAAQ,QAAQ;EAClD;EAEO,MAAM,eAAe,SAA8B;AACxD,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,eAAe,aAAa,QAAQ,WAAW;EACxD;EAEO,MAAM,cAAc,SAA6B;AACtD,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,cAAc,aAAa;MAChC,aAAa,QAAQ;MACrB,UAAU,QAAQ;KACnB;EACH;EAEO,MAAM,iBAAc;AACzB,UAAM,OAAO,QAAO;AACpB,SAAK,kBAAiB;EACxB;EAEO,MAAM,YAAY,SAA2B;AAClD,UAAM,OAAO,QAAO;AACpB,UAAM,OAAO,QAAQ,QAAQ;AAC7B,UAAM,SAAS,QAAQ,UAAU;AACjC,QAAI,QAAQ,KAAK,SAAS,KAAK,GAAG;AAChC,0BAAoB,MAAM,GAAG,QAAQ,IAAI,IAAI,IAAI,EAAE;WAC9C;AACL,0BAAoB,MAAM,GAAG,MAAM,MAAM,QAAQ,IAAI,IAAI,IAAI,EAAE;;EAEnE;EAEO,MAAM,wBACX,SAAuC;AAEvC,UAAM,OAAO,QAAO;AACpB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,wBACL,aACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UACT,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,kBAAkB;EAE/B;EAEQ,sBAAsB,MAAyB;AACrD,UAAM,aAAa,KAAK,iBAAiB,IAAI;AAC7C,UAAM,SAA0B;MAC9B,MAAM;;AAER,SAAK,gBACH,2BAA0B,yBAC1B,QACA,IAAI;EAER;EAEQ,MAAM,oBAAoB,MAAyB;AACzD,QAAI,CAAC,MAAM;AACT;;AAEF,UAAM,UAAU,MAAM,KAAK,WAAW,KAAK;AAC3C,UAAM,SAA2B;MAC/B,OAAO;;AAET,SAAK,gBACH,2BAA0B,uBAC1B,QACA,IAAI;EAER;EAEQ,mBACN,SACA,UAAmE;AAEnE,QAAI,QAAQ,kBAAkB;AAC5B,YAAM,mBAA6C,CAAA;AACnD,cAAQ,iBAAiB,IAAI,eAAY;AACvC,yBAAiB,UAAU,GAAG,IAAI,UAAU;MAC9C,CAAC;AACD,eAAS,oBAAoB,gBAAgB;;AAE/C,QAAI,QAAQ,QAAQ;AAClB,iBAAW,SAAS,QAAQ,QAAQ;AAClC,iBAAS,SAAS,KAAK;;;EAG7B;EAEO,0BACL,UACA,MAA2B;AAE3B,UAAM,OAAO,QAAO;AACpB,QAAI,SAAS,YAAY;AACvB,aAAO,mBAAmB,MAAM,QAAQ;WACnC;AACL,aAAO,gBAAgB,MAAM,QAAQ;;EAEzC;EAEO,mCACL,UACA,MAA2B;AAE3B,UAAM,OAAO,QAAO;AACpB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,QAAI,SAAS,YAAY;AACvB,aAAO,iBAAiB,KAAK,aAAa,QAAQ;WAC7C;AACL,aAAO,cAAc,KAAK,aAAa,QAAQ;;EAEnD;EAEO,8BACL,YAAkC;AAElC,UAAM,OAAO,QAAO;AACpB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAI,MAAM,2BAA0B,uBAAuB;;AAEnE,WAAO,mBAAmB,KAAK,aAAa,UAAU;EACxD;EAEO,2CAAwC;AAC7C,SAAK,uBAAsB;EAC7B;EAEO,yCAAsC;AAC3C,SAAK,uBAAsB;EAC7B;EAEQ,mBACN,gBACA,gBAA6C;AAE7C,UAAM,aAAa,KAAK,kBAAiB,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,SAAQ,IAAI;AACrE,UAAM,mBAAmB,KAAK,uBAAuB,cAAc;AACnE,UAAM,2BACJ,KAAK,+BAA+B,cAAc;AACpD,UAAM,SAAuB;MAC3B,MAAM;MACN,YAAY;MACZ,oBAAoB;;AAEtB,WAAO;EACT;EAEQ,uBACN,YAAyC;AAEzC,QAAI,CAAC,YAAY;AACf,aAAO;;AAET,UAAM,SAAyB;MAC7B,YAAY,WAAW;;AAEzB,QAAI,sBAAsB,iBAAiB;AACzC,aAAO,cAAc,WAAW;AAChC,aAAO,UAAU,WAAW;AAC5B,aAAO,SAAS,WAAW;;AAE7B,WAAO;EACT;EAEQ,iBAAiB,MAAyB;AAChD,QAAI,CAAC,MAAM;AACT,aAAO;;AAET,UAAM,SAAe;MACnB,aAAa,KAAK;MAClB,OAAO,KAAK;MACZ,eAAe,KAAK;MACpB,aAAa,KAAK;MAClB,UAAU,KAAK,yBAAyB,KAAK,QAAQ;MACrD,aAAa,KAAK;MAClB,UAAU,KAAK;MACf,cAAc,KAAK,6BAA6B,KAAK,YAAY;MACjE,YAAY,KAAK;MACjB,UAAU,KAAK;MACf,KAAK,KAAK;;AAEZ,WAAO;EACT;EAEQ,yBACN,UAA8B;AAE9B,UAAM,SAAuB,CAAA;AAC7B,QAAI,SAAS,cAAc;AACzB,aAAO,eAAe,KAAK,MAAM,SAAS,YAAY;;AAExD,QAAI,SAAS,gBAAgB;AAC3B,aAAO,iBAAiB,KAAK,MAAM,SAAS,cAAc;;AAE5D,WAAO;EACT;EAEQ,6BACN,cAAgC;AAEhC,WAAO,aAAa,IAAI,WAAS;MAC/B,aAAa,KAAK;MAClB,OAAO,KAAK;MACZ,aAAa,KAAK;MAClB,UAAU,KAAK;MACf,YAAY,KAAK;MACjB,KAAK,KAAK;MACV;EACJ;EAEQ,+BACN,YAAyC;AAEzC,QAAI,CAAC,YAAY;AACf,aAAO;;AAET,UAAM,qBAAqB,sBAAsB,UAAU;AAC3D,QAAI,CAAC,oBAAoB;AACvB,aAAO;;AAET,UAAM,EAAE,WAAW,SAAS,YAAY,SAAQ,IAAK;AACrD,UAAM,SAA6B;MACjC;;AAEF,QAAI,eAAe,MAAM;AACvB,aAAO,aAAa;;AAEtB,QAAI,YAAY,MAAM;AACpB,aAAO,UAAU;;AAEnB,QAAI,aAAa,QAAQ,aAAa,QAAW;AAC/C,aAAO,WAAW;;AAEpB,WAAO;EACT;EAEQ,gBAAgB,OAAc;AACpC,QACE,iBAAiB,UACjB,aAAa,SACb,OAAO,MAAM,SAAS,MAAM,UAC5B;AACA,aAAO,MAAM,SAAS;;AAExB,WAAO,KAAK,UAAU,KAAK;EAC7B;EAEQ,yBAAsB;AAC5B,UAAM,IAAI,MAAM,uBAAuB;EACzC;;AA32BuB,0BAAA,0BAA0B;AAC1B,0BAAA,wBAAwB;AACxB,0BAAA,wBAAwB;AACxB,0BAAA,kCACrB;AACqB,0BAAA,0BAA0B;AAC1B,0BAAA,6BACrB;AACqB,0BAAA,mCACrB;AACqB,0BAAA,oCACrB;", "names": []}