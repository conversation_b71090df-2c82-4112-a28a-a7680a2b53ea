import React from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { useApp } from '../contexts/AppContext'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { formatPrice } from '../lib/utils'
import { Plus, Minus, Trash2, ShoppingBag, ArrowLeft } from 'lucide-react'

/**
 * Página del carrito de compras
 */
export const ShoppingCartPage: React.FC = () => {
  const { state, dispatch } = useApp()
  const navigate = useNavigate()

  const handleUpdateQuantity = (productId: string, quantity: number) => {
    dispatch({ type: 'UPDATE_CART_ITEM', payload: { productId, quantity } })
  }

  const handleRemoveItem = (productId: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: productId })
  }

  const handleClearCart = () => {
    dispatch({ type: 'CLEAR_CART' })
  }

  const handleCheckout = () => {
    if (!state.user) {
      navigate('/login?redirect=/checkout')
      return
    }
    navigate('/checkout')
  }

  if (state.cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
            <ShoppingBag className="w-12 h-12 text-muted-foreground" />
          </div>
          <h1 className="text-3xl font-bold mb-4">Tu carrito está vacío</h1>
          <p className="text-muted-foreground mb-8">
            Parece que aún no has agregado ningún producto a tu carrito.
          </p>
          <Button asChild>
            <Link to="/">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Continuar comprando
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="icon" asChild>
          <Link to="/">
            <ArrowLeft className="w-4 h-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Carrito de Compras</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Lista de productos */}
        <div className="lg:col-span-2 space-y-4">
          {state.cart.items.map(item => (
            <Card key={item.productId}>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  {/* Imagen del producto */}
                  <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      src={item.imageUrl}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Información del producto */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg truncate">{item.name}</h3>
                    <p className="text-muted-foreground">
                      {formatPrice(item.unitPrice)} c/u
                    </p>
                  </div>

                  {/* Controles de cantidad */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity - 1)}
                      disabled={item.quantity <= 1}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                    <span className="font-medium min-w-[3rem] text-center">
                      {item.quantity}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity + 1)}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Precio total del item */}
                  <div className="text-right">
                    <p className="font-semibold text-lg">
                      {formatPrice(item.quantity * item.unitPrice)}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveItem(item.productId)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Eliminar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Botón para limpiar carrito */}
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleClearCart}>
              <Trash2 className="w-4 h-4 mr-2" />
              Vaciar carrito
            </Button>
          </div>
        </div>

        {/* Resumen del pedido */}
        <div className="lg:col-span-1">
          <Card className="sticky top-24">
            <CardHeader>
              <CardTitle>Resumen del Pedido</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Desglose de precios */}
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal ({state.cart.items.reduce((total, item) => total + item.quantity, 0)} productos)</span>
                  <span>{formatPrice(state.cart.totalAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Envío</span>
                  <span className="text-green-600">Gratis</span>
                </div>
                <hr />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{formatPrice(state.cart.totalAmount)}</span>
                </div>
              </div>

              {/* Botón de checkout */}
              <Button 
                className="w-full" 
                size="lg"
                onClick={handleCheckout}
              >
                Proceder al Pago
              </Button>

              {/* Información adicional */}
              <div className="text-sm text-muted-foreground space-y-2">
                <p>✓ Envío gratis en pedidos superiores a $50</p>
                <p>✓ Garantía de satisfacción 100%</p>
                <p>✓ Pago seguro con encriptación SSL</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
