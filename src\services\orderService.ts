import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc,
  query,
  where,
  orderBy
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import type { Order } from '../../types'

/**
 * Servicio para gestión de pedidos
 */
export class OrderService {
  
  /**
   * Crea una nueva orden
   */
  static async createOrder(orderData: Omit<Order, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'orders'), {
        ...orderData,
        createdAt: new Date().toISOString()
      })
      
      return docRef.id
    } catch (error) {
      console.error('Error creando orden:', error)
      throw error
    }
  }
  
  /**
   * Obtiene todas las pedidos de un usuario
   */
  static async getUserOrders(userId: string): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('userId', '==', userId)
        // orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order)
      })
      
      return orders
    } catch (error) {
      console.error('Error obteniendo pedidos del usuario:', error)
      throw error
    }
  }
  
  /**
   * Obtiene todas las pedidos (para admin)
   */
  static async getAllOrders(): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order)
      })
      
      return orders
    } catch (error) {
      console.error('Error obteniendo todas las pedidos:', error)
      throw error
    }
  }
  
  /**
   * Obtiene una orden por ID
   */
  static async getOrderById(id: string): Promise<Order | null> {
    try {
      const docRef = doc(db, 'orders', id)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Order
      }
      
      return null
    } catch (error) {
      console.error('Error obteniendo orden:', error)
      throw error
    }
  }
  
  /**
   * Actualiza el estado de una orden
   */
  static async updateOrderStatus(id: string, status: Order['status'], paymentId?: string): Promise<void> {
    try {
      const docRef = doc(db, 'orders', id)
      const updateData: any = {
        status,
        updatedAt: new Date().toISOString()
      }

      if (paymentId) {
        updateData.paymentId = paymentId
      }

      await updateDoc(docRef, updateData)
    } catch (error) {
      console.error('Error actualizando estado de orden:', error)
      throw error
    }
  }
  
  /**
   * Obtiene pedidos por estado
   */
  static async getOrdersByStatus(status: Order['status']): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order)
      })
      
      return orders
    } catch (error) {
      console.error('Error obteniendo pedidos por estado:', error)
      throw error
    }
  }
}
