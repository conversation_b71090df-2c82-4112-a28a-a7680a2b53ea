# Guía de Configuración - Fuxion E-commerce

## 🚀 Inicio Rápido

La aplicación está configurada para funcionar inmediatamente con datos de ejemplo. Para una configuración completa con Firebase y Stripe, sigue los pasos a continuación.

## 📋 Requisitos Previos

- Node.js 18+ 
- npm o yarn
- Cuenta de Firebase (opcional)
- Cuenta de Stripe (opcional)

## 🔧 Configuración Paso a Paso

### 1. Instalación Básica

```bash
# Instalar dependencias
npm install

# Iniciar servidor de desarrollo
npm run dev
```

La aplicación estará disponible en `http://localhost:5174`

### 2. Configuración de Firebase (Opcional)

#### 2.1 Crear Proyecto Firebase

1. Ve a [Firebase Console](https://console.firebase.google.com)
2. Crea un nuevo proyecto
3. Habilita Authentication con Email/Password
4. Crea una base de datos Firestore

#### 2.2 Configurar Firestore

Crea las siguientes colecciones en Firestore:

- `products` - Productos de la tienda
- `users` - Usuarios registrados
- `orders` - Pedidos de compra
- `admins` - Administradores del sistema
- `userAddresses` - Direcciones de envío

#### 2.3 Reglas de Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Productos - lectura pública, escritura solo admins
    match /products/{productId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Usuarios - solo el propietario
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Pedidos - solo el propietario o admin
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || isAdmin());
    }
    
    // Direcciones - solo el propietario
    match /userAddresses/{addressId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.uid;
    }
    
    // Admins - solo lectura para verificación
    match /admins/{adminId} {
      allow read: if request.auth != null && request.auth.uid == adminId;
    }
    
    function isAdmin() {
      return request.auth != null && 
        exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
  }
}
```

#### 2.4 Actualizar Configuración

Edita `src/lib/firebase.ts` con tu configuración:

```typescript
const firebaseConfig = {
  apiKey: "tu-api-key-aqui",
  authDomain: "tu-proyecto.firebaseapp.com",
  projectId: "tu-proyecto-id",
  storageBucket: "tu-proyecto.appspot.com",
  messagingSenderId: "123456789",
  appId: "tu-app-id-aqui"
}
```

### 3. Configuración de Stripe (Opcional)

#### 3.1 Crear Cuenta Stripe

1. Ve a [Stripe](https://stripe.com)
2. Crea una cuenta
3. Obtén tus claves de API

#### 3.2 Actualizar Configuración

Edita `src/services/stripeService.ts`:

```typescript
const stripePublicKey = 'pk_test_tu_clave_publica_aqui'
```

### 4. Crear Administrador

Para acceder al panel de administración, crea un documento en la colección `admins`:

```javascript
// En Firestore Console
{
  uid: "uid-del-usuario-admin",
  email: "<EMAIL>",
  displayName: "Administrador",
  permissions: ["manageProducts", "manageOrders"],
  createdAt: "2024-01-01T00:00:00.000Z"
}
```

### 5. Datos de Ejemplo

La aplicación incluye productos de ejemplo que se cargan automáticamente si Firebase no está configurado. Estos se encuentran en `src/data/sampleProducts.ts`.

## 📱 Configuración Móvil (Capacitor)

### Agregar Plataformas

```bash
# iOS
npx cap add ios

# Android
npx cap add android
```

### Sincronizar y Abrir

```bash
# Sincronizar cambios
npx cap sync

# Abrir en Xcode (iOS)
npx cap open ios

# Abrir en Android Studio
npx cap open android
```

## 🔐 Credenciales de Demo

Para probar la funcionalidad de administrador sin configurar Firebase:

- **Email**: <EMAIL>
- **Contraseña**: admin123

*Nota: Estas credenciales solo funcionan en modo demo*

## 🛠️ Scripts Disponibles

```bash
# Desarrollo
npm run dev

# Construcción
npm run build

# Preview de producción
npm run preview

# Linting
npm run lint

# Capacitor
npx cap sync
npx cap run ios
npx cap run android
```

## 🎨 Personalización

### Colores y Tema

Los colores se definen en `src/index.css` usando variables CSS. Puedes personalizar:

- `--primary`: Color principal
- `--secondary`: Color secundario
- `--accent`: Color de acento
- `--background`: Color de fondo
- `--foreground`: Color de texto

### Componentes UI

Los componentes están en `src/components/ui/` y siguen el patrón de Shadcn UI. Puedes personalizarlos según tus necesidades.

## 🐛 Solución de Problemas

### Error de PostCSS/TailwindCSS

Si ves errores relacionados con PostCSS:

```bash
npm install @tailwindcss/postcss
```

### Firebase no conecta

Verifica que:
1. Las credenciales en `firebase.ts` sean correctas
2. Las reglas de Firestore estén configuradas
3. Authentication esté habilitado

### Stripe no funciona

Asegúrate de:
1. Usar la clave pública correcta
2. Tener un backend configurado para webhooks
3. Estar en modo de prueba para desarrollo

## 📞 Soporte

Si tienes problemas:

1. Revisa la consola del navegador para errores
2. Verifica que todas las dependencias estén instaladas
3. Asegúrate de que los servicios externos estén configurados correctamente

## 🚀 Despliegue

### Vercel/Netlify

```bash
npm run build
# Subir carpeta dist/
```

### Firebase Hosting

```bash
npm install -g firebase-tools
firebase login
firebase init hosting
npm run build
firebase deploy
```

---

¡Tu aplicación Fuxion está lista para usar! 🎉
