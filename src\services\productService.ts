import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  query,
  where,
  orderBy
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import type { Product } from '../../types'

/**
 * Servicio para gestión de productos
 */
export class ProductService {
  
  /**
   * Obtiene todos los productos activos
   */
  static async getProducts(): Promise<Product[]> {
    try {
      const q = query(
        collection(db, 'products'),
        where('isActive', '==', true),
        // orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const products: Product[] = []
      
      querySnapshot.forEach((doc) => {
        products.push({ id: doc.id, ...doc.data() } as Product)
      })
      
      return products
    } catch (error) {
      console.error('Error obteniendo productos:', error)
      throw error
    }
  }
  
  /**
   * Obtiene todos los productos (para admin)
   */
  static async getAllProducts(): Promise<Product[]> {
    try {
      const q = query(
        collection(db, 'products'),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const products: Product[] = []
      
      querySnapshot.forEach((doc) => {
        products.push({ id: doc.id, ...doc.data() } as Product)
      })
      
      return products
    } catch (error) {
      console.error('Error obteniendo todos los productos:', error)
      throw error
    }
  }
  
  /**
   * Obtiene un producto por ID
   */
  static async getProductById(id: string): Promise<Product | null> {
    try {
      const docRef = doc(db, 'products', id)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Product
      }
      
      return null
    } catch (error) {
      console.error('Error obteniendo producto:', error)
      throw error
    }
  }
  
  /**
   * Crea un nuevo producto
   */
  static async createProduct(productData: Omit<Product, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'products'), {
        ...productData,
        createdAt: new Date().toISOString()
      })
      
      return docRef.id
    } catch (error) {
      console.error('Error creando producto:', error)
      throw error
    }
  }
  
  /**
   * Actualiza un producto
   */
  static async updateProduct(id: string, productData: Partial<Product>): Promise<void> {
    try {
      const docRef = doc(db, 'products', id)
      await updateDoc(docRef, {
        ...productData,
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error actualizando producto:', error)
      throw error
    }
  }
  
  /**
   * Elimina un producto
   */
  static async deleteProduct(id: string): Promise<void> {
    try {
      const docRef = doc(db, 'products', id)
      await deleteDoc(docRef)
    } catch (error) {
      console.error('Error eliminando producto:', error)
      throw error
    }
  }
  
  /**
   * Busca productos por categoría
   */
  static async getProductsByCategory(category: string): Promise<Product[]> {
    try {
      const q = query(
        collection(db, 'products'),
        where('category', '==', category),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const products: Product[] = []
      
      querySnapshot.forEach((doc) => {
        products.push({ id: doc.id, ...doc.data() } as Product)
      })
      
      return products
    } catch (error) {
      console.error('Error obteniendo productos por categoría:', error)
      throw error
    }
  }
}
