import { Browser } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import type { ShoppingCart } from '../../types'

// Configuración de PayPal
const PAYPAL_CONFIG = {
  clientId: 'AYGKb-LV8LHkfS7-AfAfCqOGZXZG98WpjhIDPfRKOz4f44Q3lFPIe6w1kaHiz74WYvb71Xdv9JjoxT7SXNkMspp7g61Fpvlr', // Reemplazar con tu Client ID real
  environment: 'sandbox', // 'sandbox' para desarrollo, 'production' para producción
  currency: 'USD',
}

export interface PayPalOrderData {
  cart: ShoppingCart
  userId: string
  addressId: string
  description?: string
}

export interface PayPalPaymentResult {
  transactionId: string
  orderId: string
  amount: number
  status: 'completed' | 'pending' | 'failed'
}

/**
 * Servicio para manejar pagos con PayPal
 */
export class PayPalService {
  
  /**
   * Crea una orden de PayPal
   */
  static async createOrder(orderData: PayPalOrderData): Promise<{ orderId: string }> {
    try {
      const { cart, userId, addressId, description } = orderData

      // Calcular items de la orden
      const items = cart.items.map(item => ({
        name: item.name,
        description: `${item.formatName} - ${item.formatDescription}`,
        quantity: item.quantity.toString(),
        unit_amount: {
          currency_code: PAYPAL_CONFIG.currency,
          value: item.unitPrice.toFixed(2),
        },
      }))

      // Datos de la orden para PayPal
      const paypalOrderData = {
        intent: 'CAPTURE',
        purchase_units: [
          {
            reference_id: `order_${userId}_${Date.now()}`,
            description: description || 'Compra en AriFuxion',
            amount: {
              currency_code: PAYPAL_CONFIG.currency,
              value: cart.totalAmount.toFixed(2),
              breakdown: {
                item_total: {
                  currency_code: PAYPAL_CONFIG.currency,
                  value: cart.totalAmount.toFixed(2),
                },
              },
            },
            items: items,
            custom_id: addressId,
          },
        ],
        application_context: {
          brand_name: 'AriFuxion',
          landing_page: 'NO_PREFERENCE',
          user_action: 'PAY_NOW',
          return_url: `${window.location.origin}/payment-success`,
          cancel_url: `${window.location.origin}/checkout`,
        },
      }

      console.log('Creando orden PayPal:', paypalOrderData)

      // En producción, esto sería una llamada a tu backend
      // const response = await fetch('/api/paypal/create-order', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(paypalOrderData),
      // })
      // const result = await response.json()
      // return { orderId: result.id }

      // Para demostración, generar ID simulado
      const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      
      return { orderId }
    } catch (error) {
      console.error('Error creando orden PayPal:', error)
      throw error
    }
  }

  /**
   * Captura un pago de PayPal
   */
  static async capturePayment(orderId: string): Promise<PayPalPaymentResult> {
    try {
      console.log('Capturando pago PayPal:', orderId)

      // En producción, esto sería una llamada a tu backend
      // const response = await fetch(`/api/paypal/capture-order/${orderId}`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      // })
      // const result = await response.json()

      // Simular delay de procesamiento
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Simular posible error (5% de probabilidad)
      if (Math.random() < 0.05) {
        throw new Error('Error simulado: Pago rechazado por PayPal')
      }

      // Generar resultado simulado
      const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      
      const result: PayPalPaymentResult = {
        transactionId,
        orderId,
        amount: 0, // En producción, esto vendría de PayPal
        status: 'completed',
      }

      console.log('Pago capturado exitosamente:', result)
      return result
    } catch (error) {
      console.error('Error capturando pago PayPal:', error)
      throw error
    }
  }

  /**
   * Abre PayPal en navegador externo para móvil
   */
  static async openPayPalInBrowser(orderId: string): Promise<void> {
    try {
      const paypalUrl = this.getPayPalUrl(orderId)

      if (Capacitor.getPlatform() !== 'web') {
        // Abrir en navegador externo en móvil
        await Browser.open({
          url: paypalUrl,
          windowName: '_system',
          presentationStyle: 'popover',
        })
      } else {
        // En web, abrir en nueva ventana
        window.open(paypalUrl, '_blank', 'width=400,height=600')
      }
    } catch (error) {
      console.error('Error abriendo PayPal en navegador:', error)
      throw error
    }
  }

  /**
   * Genera la URL de PayPal para checkout
   */
  private static getPayPalUrl(orderId: string): string {
    const baseUrl = PAYPAL_CONFIG.environment === 'sandbox' 
      ? 'https://www.sandbox.paypal.com' 
      : 'https://www.paypal.com'
    
    return `${baseUrl}/checkoutnow?token=${orderId}`
  }

  /**
   * Procesa el pago completo (crear orden y capturar)
   */
  static async processPayment(orderData: PayPalOrderData): Promise<PayPalPaymentResult> {
    try {
      // Crear orden
      const { orderId } = await this.createOrder(orderData)
      
      // En móvil, abrir en navegador externo
      if (Capacitor.getPlatform() !== 'web') {
        await this.openPayPalInBrowser(orderId)
        
        // En producción, manejarías el retorno con deep links
        // Por ahora, simular éxito después de un delay
        await new Promise(resolve => setTimeout(resolve, 3000))
      }
      
      // Capturar pago
      const result = await this.capturePayment(orderId)
      
      return result
    } catch (error) {
      console.error('Error procesando pago PayPal:', error)
      throw error
    }
  }

  /**
   * Verifica el estado de un pago
   */
  static async verifyPayment(_orderId: string): Promise<{ status: string; transactionId?: string }> {
    try {
      // En producción, esto sería una llamada a tu backend
      // const response = await fetch(`/api/paypal/verify-payment/${orderId}`)
      // const result = await response.json()
      // return result

      // Simular verificación
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        status: 'completed',
        transactionId: `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      }
    } catch (error) {
      console.error('Error verificando pago PayPal:', error)
      throw error
    }
  }

  /**
   * Obtiene la configuración de PayPal
   */
  static getConfig() {
    return PAYPAL_CONFIG
  }
}

export default PayPalService
